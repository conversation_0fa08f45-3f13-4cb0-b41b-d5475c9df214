:root {
    --main-color: #861818;
    --highlight-color: #ffd700;
    --light-color: #f5f5f5;
    --dark-color: #262626;
    --secondary-color: #444444;
    --secondary-hover-color: #555555;
    --text-light: #f5f5f5;
    --text-dark: #262626;
}

body {
    background-color: #262626;
    color: #f5f5f5;
    font-family: 'Open Sans', sans-serif;
    margin: 0;
    padding: 0;
}

/* Main Layout */
.transport-layout {
    display: flex;
    max-width: 1300px;
    margin: 0 auto;
    padding: 20px;
    gap: 25px;
    justify-content: space-between;
}

/* Left Column: Main Content */
.transport-main {
    width: 730px;
    flex-shrink: 0;
}

/* Right Column: Reservations */
.reservations-column {
    width: 320px;
    flex-shrink: 0;
    margin-left: 10px;
    margin-right: 70px;
}

/* Reservations Box */
.reservations-box {
    background-color: #333333;
    border-radius: 12px;
    padding: 20px;
    margin-top: 80px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--main-color);
    position: sticky;
    top: 100px;
}

.reservations-box h3 {
    color: var(--highlight-color);
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 20px;
    text-shadow: 1px 1px 0 var(--main-color);
}

.add-reservation-btn-container {
    margin-bottom: 15px;
}

.add-reservation-btn {
    background-color: #444;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 5px;
    width: 100%;
}

.add-reservation-btn:hover {
    background-color: #555;
}

.empty-reservations {
    text-align: center;
    padding: 20px 0;
    color: #aaa;
}

.empty-reservations i {
    font-size: 24px;
    margin-bottom: 10px;
    color: #555;
}

.empty-reservations .hint {
    font-size: 12px;
    margin-top: 5px;
    color: #777;
}

/* Header Styling */
.transport-header {
    margin-top: 100px;
    margin-bottom: 20px;
}

.transport-header h2 {
    color: var(--highlight-color);
    margin-bottom: 10px;
    font-size: 28px;
    text-shadow: 2px 2px 0 var(--main-color);
}

/* Search Box */
.search-box {
    background-color: #333333;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--main-color);
}

/* Transport Tabs */
.transport-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid #444;
    padding-bottom: 5px;
}

.tab-btn {
    padding: 10px 20px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    font-size: 16px;
    font-weight: 600;
    color: #aaa;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tab-btn i {
    font-size: 16px;
}

.tab-btn.active {
    color: var(--highlight-color);
    border-bottom-color: var(--main-color);
    font-size: 18px;
}

.tab-btn:hover:not(.active) {
    color: #fff;
    background-color: #444;
}

/* Form Styling */
.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
    align-items: flex-end;
}

.form-group {
    flex: 1;
    min-width: 0;
}

/* Trip Type */
.trip-type {
    display: flex;
    background-color: #444;
    border-radius: 20px;
    padding: 4px;
    max-width: 180px;
    margin-bottom: 0;
    align-self: flex-end;
}

.trip-option {
    flex: 1;
}

.trip-option input[type="radio"] {
    display: none;
}

.trip-option label {
    display: block;
    position: relative;
    padding: 8px 12px 4px;
    text-align: center;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
    line-height: 1;
    transform: translateY(2px);
}

.trip-option input[type="radio"]:checked+label {
    background-color: var(--main-color);
    color: white;
    padding-bottom: 9px;
    box-shadow: 0 1px 0 var(--main-color);
}

/* Locations */
.locations {
    display: flex;
    align-items: flex-end;
    gap: 10px;
    flex: 3;
}

.location-field {
    flex: 1;
    min-width: 0;
    position: relative;
}

/* Airport Suggestions */
.suggestions {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    background-color: #444 !important;
    border: 3px solid #ff0000 !important;
    /* Bright red border for debugging */
    border-top: 3px solid #ff0000 !important;
    border-radius: 0 0 6px 6px !important;
    max-height: 300px !important;
    overflow-y: auto !important;
    z-index: 99999 !important;
    /* Extremely high z-index */
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.8) !important;
    /* Stronger shadow */
    display: none !important;
    margin-top: 4px !important;
    /* Larger gap */
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;
}

.suggestions:not(:empty) {
    display: block !important;
    /* Force display when not empty */
}

/* Make sure suggestions are visible even when inside other containers */
body .suggestions:not(:empty) {
    display: block !important;
}

/* Override any potential conflicting styles */
.location-field .suggestions,
.form-group .suggestions,
.modal-body .suggestions {
    display: none !important;
}

.location-field .suggestions:not(:empty),
.form-group .suggestions:not(:empty),
.modal-body .suggestions:not(:empty) {
    display: block !important;
}

/* Make sure suggestions appear in modal too */
.modal-body .form-group {
    position: relative;
}

.suggestion-item {
    padding: 8px 12px;
    /* Reduced vertical padding from 12px to 8px */
    cursor: pointer;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #555;
    transition: all 0.2s;
    background-color: #444;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:hover {
    background-color: #555;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.suggestion-code {
    font-weight: bold;
    color: var(--highlight-color);
    margin-right: 8px;
    padding: 3px 6px;
    background-color: rgba(134, 24, 24, 0.2);
    border-radius: 4px;
    min-width: 36px;
    text-align: center;
    font-size: 12px;
    /* Added smaller font size */
}

.suggestion-details {
    flex: 1;
    min-width: 0;
    /* Ensures text truncation works properly */
    max-width: calc(100% - 50px);
    /* Gives space for the code */
}

.suggestion-name {
    font-weight: 600;
    color: #fff;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 13px;
    /* Reduced from default size */
}

.suggestion-location {
    font-size: 11px;
    /* Reduced from 12px */
    color: #aaa;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Loading, error, and no results states */
.suggestion-loading,
.suggestion-error,
.suggestion-no-results {
    padding: 20px;
    text-align: center;
    color: #ddd;
    font-size: 14px;
    font-weight: bold;
    background-color: #444;
}

.suggestion-loading {
    color: #fff;
    background-color: #444;
    position: relative;
}

.suggestion-loading:after {
    content: '...';
    animation: loading-dots 1.5s infinite;
    display: inline-block;
    width: 20px;
    text-align: left;
}

@keyframes loading-dots {
    0% {
        content: '.';
    }

    33% {
        content: '..';
    }

    66% {
        content: '...';
    }

    100% {
        content: '.';
    }
}

.suggestion-error {
    color: #ff6b6b;
    background-color: rgba(255, 107, 107, 0.1);
    border-left: 4px solid #ff6b6b;
}

.suggestion-no-results {
    color: #aaa;
    font-style: italic;
    background-color: #3a3a3a;
}

.switch-btn {
    margin-bottom: 10px;
    display: flex;
    align-items: flex-end;
}

#switchButton {
    background-color: #444;
    color: #aaa;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
}

#switchButton:hover {
    background-color: var(--secondary-hover-color);
    color: var(--text-dark);
    border-color: var(--secondary-hover-color);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

/* Dates */
.dates {
    display: flex;
    gap: 15px;
    flex: 2;
}

.date-field {
    flex: 1;
    min-width: 0;
}

/* Form Elements */
label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
    color: #aaa;
}

input[type="text"],
input[type="time"],
select,
textarea,
.passenger-selector {
    width: 100%;
    padding: 10px;
    background-color: #444;
    border: 1px solid #555;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    box-sizing: border-box;
}

.input-hint {
    display: block;
    font-size: 12px;
    color: #aaa;
    margin-top: 4px;
    font-style: italic;
}

input[type="text"]:focus,
select:focus {
    border-color: var(--main-color);
    outline: none;
}

/* Passenger Selector */
.passenger-selector {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    position: relative;
}

.passenger-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: #444;
    border: 1px solid #555;
    border-radius: 6px;
    padding: 10px;
    z-index: 10;
    margin-top: 5px;
}

.passenger-type {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.counter {
    display: flex;
    align-items: center;
    gap: 10px;
}

.counter-btn {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: none;
    background-color: #555;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.counter-btn:hover {
    background-color: var(--main-color);
}

/* Search Button */
.search-btn {
    background-color: var(--main-color);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    width: 100%;
    margin-top: 24px;
}

.search-btn:hover {
    background-color: #a52020;
}

/* Globe Container */
.globe-container {
    width: 100%;
    height: 320px;
    background-color: #222;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 20px;
    border: 1px solid var(--main-color);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    position: relative;
}

#globeViz {
    width: 100%;
    height: 100%;
}

.globe-overlay {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
}

/* Results Container */
.results-container {
    background-color: #333;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--main-color);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Flight Result Cards */
.flight-card {
    background-color: #444;
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
    transition: all 0.3s;
    border: 1px solid #555;
    display: flex;
    flex-direction: column;
}

.flight-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    border-color: var(--highlight-color);
}

.flight-card-header {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background-color: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid #555;
}

.flight-card-header .airline-logo {
    width: 32px;
    height: 32px;
    margin-right: 12px;
    border-radius: 50%;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.flight-card-header .airline-logo img {
    max-width: 90%;
    max-height: 90%;
}

.flight-card-header .airline-logo i {
    color: #444;
    font-size: 18px;
}

.flight-card-header .airline-name {
    font-weight: 600;
    color: #fff;
    flex: 1;
}

.flight-card-header .flight-number {
    color: #aaa;
    font-size: 14px;
    margin-left: 10px;
}

.flight-card-body {
    display: flex;
    padding: 15px;
    align-items: center;
}

.flight-route {
    display: flex;
    align-items: center;
    flex: 1;
}

.flight-departure,
.flight-arrival {
    text-align: center;
}

.flight-time {
    font-size: 20px;
    font-weight: 700;
    color: #fff;
    margin-bottom: 5px;
}

.flight-airport {
    font-size: 14px;
    color: #bbb;
}

.flight-date {
    font-size: 12px;
    color: #999;
    margin-top: 3px;
}

.flight-path {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 20px;
}

.flight-path-line {
    position: relative;
    width: 100%;
    height: 2px;
    background-color: #555;
    margin: 10px 0;
}

.flight-path-line::after {
    content: '';
    position: absolute;
    right: -5px;
    top: -4px;
    width: 0;
    height: 0;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 8px solid #555;
}

.flight-path-icon {
    position: absolute;
    top: -9px;
    left: calc(50% - 10px);
    color: var(--highlight-color);
    font-size: 14px;
    background-color: #444;
    padding: 0 5px;
}

.flight-duration {
    font-size: 13px;
    color: #aaa;
}

.flight-stops {
    font-size: 12px;
    color: #ff9800;
    margin-top: 3px;
}

.flight-price-section {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: center;
    padding-left: 15px;
    border-left: 1px solid #555;
    min-width: 120px;
}

.flight-price {
    font-size: 24px;
    font-weight: 700;
    color: var(--highlight-color);
    margin-bottom: 8px;
}

.book-btn {
    background-color: var(--main-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    margin-bottom: 8px;
    text-decoration: none;
}

.book-btn:hover {
    background-color: #a52020;
    transform: translateY(-1px);
}

.select-btn {
    background-color: #2e7d32;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.select-btn:hover {
    background-color: #1b5e20;
    transform: translateY(-1px);
}

.select-btn.reserved {
    background-color: #555;
    cursor: default;
}

.flight-card-footer {
    display: flex;
    justify-content: space-between;
    padding: 10px 15px;
    background-color: rgba(0, 0, 0, 0.1);
    border-top: 1px solid #555;
    font-size: 13px;
    color: #aaa;
}

.flight-amenities {
    display: flex;
    gap: 10px;
}

.flight-amenity {
    display: flex;
    align-items: center;
    gap: 5px;
}

.flight-actions {
    display: flex;
    gap: 10px;
}

.flight-action-btn {
    background: none;
    border: none;
    color: #aaa;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 3px;
}

.flight-action-btn:hover {
    color: var(--highlight-color);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #444;
    border-bottom: 1px solid #555;
}

.route-summary {
    font-weight: 600;
}

.view-options {
    display: flex;
    gap: 10px;
}

.view-btn {
    background: none;
    border: none;
    color: #aaa;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 4px;
    transition: all 0.3s;
}

.view-btn.active,
.view-btn:hover {
    background-color: #555;
    color: white;
}

.results-body {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 0;
}

.filters-sidebar {
    padding: 20px;
    background-color: #3a3a3a;
    border-right: 1px solid #555;
}

.filter-section {
    margin-bottom: 20px;
}

.filter-section h5 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #ddd;
}

.filter-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-option {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.price-slider {
    margin-top: 10px;
}

.price-range {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
    font-size: 14px;
    color: #aaa;
}

.results-main {
    padding: 20px;
}

.sort-options {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.sort-options select {
    padding: 5px 10px;
    background-color: #444;
    border: 1px solid #555;
    border-radius: 4px;
    color: white;
}

.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    color: #aaa;
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-left-color: var(--main-color);
    border-radius: 50%;
    margin-bottom: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.map-view {
    height: 400px;
}

.map-placeholder {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #444;
    color: #aaa;
}

/* Reservations Box */
.reservations-box {
    background-color: #333;
    border-radius: 12px;
    padding: 20px;
    margin-top: 100px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--main-color);
    max-height: calc(100vh - 120px);
    overflow-y: auto;
    position: sticky;
    top: 100px;
}

.reservations-box h3 {
    color: var(--highlight-color);
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 20px;
    text-align: center;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--main-color);
    text-shadow: 2px 2px 0 var(--main-color);
}

.add-reservation-btn-container {
    text-align: center;
    margin-bottom: 15px;
}

.add-reservation-btn {
    background-color: #444;
    color: white;
    border: 1px solid #555;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.add-reservation-btn:hover {
    background-color: var(--main-color);
    border-color: var(--main-color);
}

.add-reservation-btn i {
    font-size: 12px;
}

.empty-reservations {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 0;
    color: #aaa;
    text-align: center;
}

.empty-reservations i {
    font-size: 32px;
    margin-bottom: 15px;
    color: #555;
}

.empty-reservations .hint {
    font-size: 14px;
    margin-top: 5px;
    color: #777;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    /* Changed from auto to hidden to prevent outer scrollbar */
    background-color: rgba(0, 0, 0, 0.7);
}

/* Add a class to the body when modal is open to prevent scrolling */
body.modal-open {
    overflow: hidden;
}

.modal-content {
    background-color: #333;
    margin: 120px auto 50px auto;
    /* Increased top margin to avoid navbar overlap */
    width: 90%;
    max-width: 500px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    border: 1px solid var(--main-color);
    animation: modalFadeIn 0.3s;
    max-height: calc(100vh - 170px);
    /* Limit height to prevent overflow */
    display: flex;
    flex-direction: column;
    overflow: hidden;
    /* Hide overflow from the container */
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 15px 20px;
    background-color: #444;
    border-bottom: 1px solid #555;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h4 {
    margin: 0;
    color: var(--highlight-color);
    font-size: 18px;
}

.close-modal {
    color: #aaa;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s;
}

.close-modal:hover {
    color: white;
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
    /* Add vertical scrollbar when content overflows */
    flex: 1;
    /* Allow the body to take remaining space */
    max-height: 100%;
    /* Ensure it doesn't exceed the container */
}

#externalReservationForm .form-row {
    margin-bottom: 15px;
}

#externalReservationForm textarea {
    width: 100%;
    height: 80px;
    padding: 10px;
    background-color: #444;
    border: 1px solid #555;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    resize: vertical;
    font-family: inherit;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.cancel-btn {
    background-color: #555;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 15px;
    cursor: pointer;
    transition: all 0.3s;
}

.cancel-btn:hover {
    background-color: #666;
}

.save-btn {
    background-color: var(--main-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
}

.save-btn:hover {
    background-color: #a52020;
}

/* Reservation Card Styling */
.reservation-card {
    position: relative;
    margin-bottom: 15px;
    background-color: #444;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
}

.reservation-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* External Reservation Styling */
.reservation-card.external {
    border-left: 5px solid #4caf50;
}

.reservation-card .reservation-header {
    display: flex;
    align-items: center;
    padding: 12px 15px 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.reservation-card .reservation-header i {
    color: var(--highlight-color);
    margin-right: 10px;
    font-size: 16px;
}

.reservation-card .reservation-id {
    margin-left: auto;
    font-size: 12px;
    background-color: #555;
    padding: 3px 8px;
    border-radius: 4px;
    color: #ddd;
}

.reservation-card .reservation-details {
    padding: 12px 15px;
}

.reservation-card.external .reservation-details {
    padding-left: 25px;
}

.reservation-card .reservation-route {
    font-weight: 600;
    margin-bottom: 8px;
    color: #fff;
}

.reservation-card .reservation-time,
.reservation-card .reservation-date {
    font-size: 14px;
    color: #bbb;
    margin-bottom: 5px;
}

.reservation-card .reservation-notes {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px dashed rgba(255, 255, 255, 0.1);
}

.reservation-card .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 5px;
    padding: 0 15px 12px;
    margin-left: auto;
    width: auto;
}

.reservation-card.external .action-buttons {
    padding-left: 25px;
}

.reservation-card .edit-btn,
.reservation-card .cancel-btn,
.reservation-card .add-to-itinerary-btn {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s;
    border: none;
    display: flex;
    align-items: center;
    gap: 5px;
}

.reservation-card .edit-btn {
    background-color: #555;
    color: #fff;
}

.reservation-card .edit-btn:hover {
    background-color: #666;
}

.reservation-card .add-to-itinerary-btn {
    background-color: rgba(255, 215, 0, 0.2);
    color: var(--highlight-color);
    font-weight: 600;
}

.reservation-card .add-to-itinerary-btn:hover {
    background-color: rgba(255, 215, 0, 0.3);
}

.reservation-card .add-to-itinerary-btn.added {
    background-color: rgba(76, 175, 80, 0.2);
    color: #4caf50;
}

.reservation-card .add-to-itinerary-btn.added:hover {
    background-color: rgba(76, 175, 80, 0.3);
}

.reservation-card .cancel-btn {
    background-color: rgba(255, 0, 0, 0.2);
    color: #ff6b6b;
}

.reservation-card .cancel-btn:hover {
    background-color: rgba(255, 0, 0, 0.3);
}

.reservation-notes {
    font-size: 0.85rem;
    color: #aaa;
    margin-top: 8px;
    font-style: italic;
    white-space: pre-line;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 60px;
}

/* External Booking Calendar Styling */
.flatpickr-calendar.open {
    z-index: 1100 !important;
}

.flatpickr-calendar {
    background-color: #444 !important;
    border-color: #555 !important;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3) !important;
}

.flatpickr-months {
    background-color: #333 !important;
    color: #fff !important;
    padding-bottom: 5px !important;
    margin-bottom: 5px !important;
    border-bottom: 1px solid #555 !important;
}

.flatpickr-month {
    color: #fff !important;
    height: 40px !important;
    overflow: visible !important;
}

/* Fix month dropdown */
.flatpickr-current-month {
    padding-top: 5px !important;
    font-size: 14px !important;
}

.flatpickr-current-month .flatpickr-monthDropdown-months {
    background-color: #444 !important;
    color: #fff !important;
    border: 1px solid #555 !important;
    border-radius: 4px !important;
    padding: 4px 8px !important;
    font-weight: 600 !important;
    -webkit-appearance: none !important;
    appearance: none !important;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23ffd700' viewBox='0 0 24 24'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E") !important;
    background-repeat: no-repeat !important;
    background-position: right 8px center !important;
    padding-right: 24px !important;
}

.flatpickr-current-month .flatpickr-monthDropdown-months:hover {
    background-color: #555 !important;
    border-color: var(--main-color) !important;
}

.flatpickr-current-month .numInputWrapper {
    height: auto !important;
}

.flatpickr-current-month input.cur-year {
    background-color: #444 !important;
    color: #fff !important;
    border: 1px solid #555 !important;
    border-radius: 4px !important;
    padding: 4px 8px !important;
    font-weight: 600 !important;
    height: auto !important;
}

/* Make prev/next month buttons gold */
.flatpickr-prev-month,
.flatpickr-next-month {
    background-color: rgba(0, 0, 0, 0.2) !important;
    border-radius: 4px !important;
    transition: all 0.3s ease !important;
    height: 30px !important;
    width: 30px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 5px !important;
    top: 0 !important;
    box-shadow: none !important;
}

.flatpickr-prev-month svg,
.flatpickr-next-month svg {
    fill: var(--highlight-color) !important;
    stroke-width: 1px !important;
    filter: none !important;
    height: 16px !important;
    width: 16px !important;
}

.flatpickr-prev-month:hover,
.flatpickr-next-month:hover {
    background-color: var(--main-color) !important;
}

.flatpickr-prev-month:hover svg,
.flatpickr-next-month:hover svg {
    fill: #ffffff !important;
}

.flatpickr-weekday {
    color: #aaa !important;
    background-color: #444 !important;
}

.flatpickr-day {
    color: #ddd !important;
    border-color: #555 !important;
    height: 38px !important;
    line-height: 38px !important;
}

.flatpickr-day:hover {
    background-color: #555 !important;
}

/* Month dropdown options */
.flatpickr-monthDropdown-month {
    background-color: #444 !important;
    color: #fff !important;
}

.flatpickr-monthDropdown-month:hover {
    background-color: var(--main-color) !important;
}

.flatpickr-day.selected {
    background-color: var(--main-color) !important;
    border-color: var(--main-color) !important;
}

.flatpickr-day.today {
    border-color: var(--highlight-color) !important;
}

.flatpickr-day.today:hover {
    background-color: var(--main-color) !important;
    border-color: var(--main-color) !important;
}

/* Fix for price indicators */
.flatpickr-day::after {
    content: "" !important;
    display: none !important;
}

.flatpickr-day.has-price::after {
    display: none !important;
}

/* Ensure calendar cells have enough space */
.flatpickr-calendar.hasTime .flatpickr-time {
    border-top: 1px solid #555 !important;
}

.flatpickr-time {
    background-color: #444 !important;
    color: #ddd !important;
}

.flatpickr-time input,
.flatpickr-time .flatpickr-am-pm {
    color: #ddd !important;
    background-color: #444 !important;
}

.numInputWrapper:hover {
    background-color: #555 !important;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .transport-layout {
        flex-direction: column;
        align-items: center;
    }

    .transport-main {
        width: 100%;
        max-width: 800px;
    }

    .reservations-column {
        width: 100%;
        max-width: 800px;
        margin-left: 0;
    }

    .reservations-box {
        margin-top: 40px;
        position: static;
        max-height: 400px;
    }

    .globe-container {
        height: 280px;
    }
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
    }

    .locations {
        flex-direction: column;
    }

    .switch-btn {
        align-self: center;
        margin: 5px 0;
    }

    .results-body {
        grid-template-columns: 1fr;
    }

    .filters-sidebar {
        display: none;
    }
}

/* Reservation Cards */
.reserved-list {
    max-height: 500px;
    overflow-y: auto;
    padding-right: 5px;
}

.reservation-card {
    background-color: #444;
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.3s;
    border-left: 3px solid var(--main-color);
}

.reservation-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.reservation-card.train {
    border-left-color: #7b1fa2;
    /* Purple for trains */
}

.reservation-card.bus {
    border-left-color: #2e7d32;
    /* Green for buses */
}

.reservation-header {
    background-color: #333;
    padding: 10px 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.reservation-header i {
    color: var(--main-color);
}

.reservation-card.train .reservation-header i {
    color: #7b1fa2;
}

.reservation-card.bus .reservation-header i {
    color: #2e7d32;
}

.reservation-id {
    margin-left: auto;
    font-size: 12px;
    color: #aaa;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

.reservation-details {
    padding: 12px 15px;
}

.reservation-route {
    font-weight: 600;
    margin-bottom: 5px;
    font-size: 14px;
}

.reservation-time {
    color: #ddd;
    font-size: 13px;
    margin-bottom: 3px;
}

.reservation-date {
    color: #aaa;
    font-size: 12px;
}

.reservation-notes {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #555;
    font-size: 12px;
    color: #bbb;
    font-style: italic;
}

.action-buttons {
    display: flex;
    padding: 10px 15px;
    background-color: #3a3a3a;
    gap: 8px;
}

.edit-btn,
.cancel-btn {
    flex: 1;
    padding: 6px 0;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.edit-btn {
    background-color: #555;
    color: white;
}

.edit-btn:hover {
    background-color: #666;
}

.cancel-btn {
    background-color: rgba(134, 24, 24, 0.2);
    color: #ff6b6b;
}

.cancel-btn:hover {
    background-color: rgba(134, 24, 24, 0.4);
}