-- SQL script to fix RLS policies for user_markers table
-- Run this directly in the Supabase SQL Editor

-- Add policy to allow anonymous role to insert markers
DROP POLICY IF EXISTS anon_insert_markers ON user_markers;
CREATE POLICY anon_insert_markers ON user_markers
  FOR INSERT TO anon
  WITH CHECK (true);

-- Add policy to allow anonymous role to select markers
DROP POLICY IF EXISTS anon_select_markers ON user_markers;
CREATE POLICY anon_select_markers ON user_markers
  FOR SELECT TO anon
  USING (true);

-- Add policy to allow anonymous role to update markers
DROP POLICY IF EXISTS anon_update_markers ON user_markers;
CREATE POLICY anon_update_markers ON user_markers
  FOR UPDATE TO anon
  USING (true);

-- Add policy to allow anonymous role to delete markers
DROP POLICY IF EXISTS anon_delete_markers ON user_markers;
CREATE POLICY anon_delete_markers ON user_markers
  FOR DELETE TO anon
  USING (true);
