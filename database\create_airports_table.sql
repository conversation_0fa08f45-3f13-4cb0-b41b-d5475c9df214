-- Create the airports table
CREATE TABLE IF NOT EXISTS public.airports (
    id TEXT PRIMARY KEY,
    ident TEXT,
    type TEXT,
    name TEXT,
    latitude_deg NUMERIC,
    longitude_deg NUMERIC,
    elevation_ft NUMERIC,
    continent TEXT,
    iso_country TEXT,
    iso_region TEXT,
    municipality TEXT,
    scheduled_service TEXT,
    icao_code TEXT,
    iata_code TEXT,
    gps_code TEXT,
    local_code TEXT,
    home_link TEXT,
    wikipedia_link TEXT,
    keywords TEXT
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_airports_iata_code ON public.airports (iata_code);
CREATE INDEX IF NOT EXISTS idx_airports_icao_code ON public.airports (icao_code);
CREATE INDEX IF NOT EXISTS idx_airports_ident ON public.airports (ident);
CREATE INDEX IF NOT EXISTS idx_airports_name ON public.airports (name);
CREATE INDEX IF NOT EXISTS idx_airports_municipality ON public.airports (municipality);
CREATE INDEX IF NOT EXISTS idx_airports_type ON public.airports (type);

-- Enable Row Level Security (RLS)
ALTER TABLE public.airports ENABLE ROW LEVEL SECURITY;

-- Create a policy that allows all operations for authenticated users
CREATE POLICY "Allow all operations for authenticated users" ON public.airports
    USING (true)
    WITH CHECK (true);

-- Create a policy that allows read-only access for anonymous users
CREATE POLICY "Allow read-only access for anonymous users" ON public.airports
    FOR SELECT
    USING (true);
