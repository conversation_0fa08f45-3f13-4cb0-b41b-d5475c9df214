<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Vestigia</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/navbar.css">
    <link rel="stylesheet" href="css/settings.css">
</head>
<body>
    <!-- Navbar will be loaded here -->
    <div id="navbar-container"></div>

    <main class="settings-container">
        <h1><i class="fas fa-cog"></i> Account Settings</h1>
        
        <div class="settings-section">
            <h2>Profile Information</h2>
            <form id="profile-form" class="settings-form">
                <div class="form-group">
                    <label for="full-name">Full Name</label>
                    <input type="text" id="full-name" name="fullName" required>
                </div>
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <button type="submit" class="btn btn-primary">Save Changes</button>
            </form>
        </div>

        <div class="settings-section">
            <h2>Change Password</h2>
            <form id="password-form" class="settings-form">
                <div class="form-group">
                    <label for="current-password">Current Password</label>
                    <input type="password" id="current-password" name="currentPassword" required>
                </div>
                <div class="form-group">
                    <label for="new-password">New Password</label>
                    <input type="password" id="new-password" name="newPassword" required minlength="8">
                </div>
                <div class="form-group">
                    <label for="confirm-password">Confirm New Password</label>
                    <input type="password" id="confirm-password" name="confirmPassword" required minlength="8">
                </div>
                <button type="submit" class="btn btn-primary">Update Password</button>
            </form>
        </div>

        <div class="settings-section danger-zone">
            <h2>Danger Zone</h2>
            <div class="danger-actions">
                <button id="delete-account" class="btn btn-danger">
                    <i class="fas fa-trash-alt"></i> Delete My Account
                </button>
                <p class="danger-note">Warning: This action cannot be undone. All your data will be permanently deleted.</p>
            </div>
        </div>
    </main>

    <script src="js/navbar-loader.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/settings.js"></script>
</body>
</html>
