-- Create transportation_reservations table
CREATE TABLE IF NOT EXISTS transportation_reservations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (
        type IN (
            'flight',
            'train',
            'bus',
            'car',
            'other'
        )
    ),
    provider TEXT,
    confirmation_number TEXT,
    departure_location TEXT NOT NULL,
    arrival_location TEXT NOT NULL,
    departure_date TIMESTAMP
    WITH
        TIME ZONE NOT NULL,
        arrival_date TIMESTAMP
    WITH
        TIME ZONE NOT NULL,
        price DECIMAL(10, 2),
        currency TEXT DEFAULT 'USD',
        status TEXT DEFAULT 'confirmed' CHECK (
            status IN (
                'confirmed',
                'pending',
                'cancelled'
            )
        ),
        notes TEXT,
        created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW()
);

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_transportation_reservations_user_id ON transportation_reservations (user_id);

-- Create index on dates for faster date-based queries
CREATE INDEX IF NOT EXISTS idx_transportation_reservations_dates ON transportation_reservations (departure_date, arrival_date);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_transportation_reservations_updated_at
    BEFORE UPDATE ON transportation_reservations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add RLS (Row Level Security) policies
ALTER TABLE transportation_reservations ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to view their own reservations
CREATE POLICY "Users can view their own reservations" ON transportation_reservations FOR
SELECT USING (auth.uid () = user_id);

-- Create policy to allow users to insert their own reservations
CREATE POLICY "Users can insert their own reservations" ON transportation_reservations FOR
INSERT
WITH
    CHECK (auth.uid () = user_id);

-- Create policy to allow users to update their own reservations
CREATE POLICY "Users can update their own reservations" ON transportation_reservations FOR
UPDATE USING (auth.uid () = user_id)
WITH
    CHECK (auth.uid () = user_id);

-- Create policy to allow users to delete their own reservations
CREATE POLICY "Users can delete their own reservations" ON transportation_reservations FOR DELETE USING (auth.uid () = user_id);

-- Add comments to table and columns
COMMENT ON
TABLE transportation_reservations IS 'Stores transportation reservations for users';

COMMENT ON COLUMN transportation_reservations.id IS 'Unique identifier for the reservation';

COMMENT ON COLUMN transportation_reservations.user_id IS 'Foreign key to auth.users table';

COMMENT ON COLUMN transportation_reservations.type IS 'Type of transportation (flight, train, bus, car, other)';

COMMENT ON COLUMN transportation_reservations.provider IS 'Name of the transportation provider';

COMMENT ON COLUMN transportation_reservations.confirmation_number IS 'Reservation confirmation number';

COMMENT ON COLUMN transportation_reservations.departure_location IS 'Starting location of the journey';

COMMENT ON COLUMN transportation_reservations.arrival_location IS 'Destination location of the journey';

COMMENT ON COLUMN transportation_reservations.departure_date IS 'Date and time of departure';

COMMENT ON COLUMN transportation_reservations.arrival_date IS 'Date and time of arrival';

COMMENT ON COLUMN transportation_reservations.price IS 'Cost of the reservation';

COMMENT ON COLUMN transportation_reservations.currency IS 'Currency code for the price';

COMMENT ON COLUMN transportation_reservations.status IS 'Current status of the reservation';

COMMENT ON COLUMN transportation_reservations.notes IS 'Additional notes about the reservation';

COMMENT ON COLUMN transportation_reservations.created_at IS 'Timestamp when the record was created';

COMMENT ON COLUMN transportation_reservations.updated_at IS 'Timestamp when the record was last updated';