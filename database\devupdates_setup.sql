-- Create devupdates table for storing development update subscribers
CREATE TABLE IF NOT EXISTS devupdates (
  id SERIAL PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  name TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on email for faster queries
CREATE INDEX IF NOT EXISTS idx_devupdates_email ON devupdates(email);

-- Enable Row Level Security (RLS)
ALTER TABLE devupdates ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS insert_devupdates ON devupdates;
DROP POLICY IF EXISTS select_devupdates ON devupdates;
DROP POLICY IF EXISTS update_devupdates ON devupdates;
DROP POLICY IF EXISTS delete_devupdates ON devupdates;

-- Create policy to allow anyone to insert (for public signup form)
CREATE POLICY insert_devupdates ON devupdates
  FOR INSERT TO anon, authenticated
  WITH CHECK (true);

-- Create policy to allow anyone to select their own email (for confirmation)
CREATE POLICY select_devupdates ON devupdates
  FOR SELECT TO anon, authenticated
  USING (true);

-- Create policy to allow only authenticated users to update data
CREATE POLICY update_devupdates ON devupdates
  FOR UPDATE TO authenticated
  USING (true);

-- Create policy to allow only authenticated users to delete data
CREATE POLICY delete_devupdates ON devupdates
  FOR DELETE TO authenticated
  USING (true);

-- Comment on table
COMMENT ON TABLE devupdates IS 'Stores email addresses of users who want to receive development updates';
