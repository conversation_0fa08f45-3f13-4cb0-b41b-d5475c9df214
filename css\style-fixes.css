/**
 * Style Fixes for Flight Cards and External Reservation Button
 */

/* Error message styling */
.error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    margin: 20px 0;
    border-left: 4px solid #f5c6cb;
}

.error-message i {
    font-size: 32px;
    color: #dc3545;
    margin-bottom: 15px;
    display: block;
}

.error-details {
    font-family: monospace;
    background-color: #f1f1f1;
    padding: 8px;
    border-radius: 4px;
    margin: 10px 0;
    color: #555;
    font-size: 14px;
    text-align: left;
}

.error-help {
    font-style: italic;
    margin-top: 10px;
    font-size: 14px;
    color: #555;
}

.retry-btn {
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    margin-top: 15px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.2s;
}

.retry-btn:hover {
    background-color: #0069d9;
}

/* Server note styling */
.server-note {
    background-color: #fff3cd;
    color: #856404;
    padding: 8px 12px;
    border-radius: 4px;
    margin-top: 10px;
    font-size: 14px;
    display: inline-block;
    border-left: 3px solid #ffeeba;
}

/* Fix for flight cards */
.flight-card {
    background-color: #333;
    border: 1px solid #444;
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    transition: transform 0.2s, box-shadow 0.2s;
}

.flight-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    border-color: var(--highlight-color);
}

.flight-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background-color: #3a3a3a;
    border-bottom: 1px solid #444;
}

.airline {
    display: flex;
    align-items: center;
    gap: 8px;
}

.airline-name {
    font-weight: 600;
    color: #fff;
}

.flight-number {
    color: #aaa;
    font-size: 13px;
}

.flight-price {
    font-weight: 700;
    color: var(--highlight-color);
    font-size: 18px;
}

.flight-details {
    padding: 15px;
}

.flight-times {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 5px 0;
}

.departure,
.arrival {
    text-align: center;
    flex: 1;
    min-width: 0;
    /* Allows flex items to shrink below content size */
    max-width: 120px;
    /* Limit maximum width */
    width: 120px;
    /* Set a fixed width */
}

.time {
    font-size: 18px;
    font-weight: 600;
    color: #fff;
    margin-bottom: 2px;
}

.date {
    font-size: 12px;
    color: #aaa;
    margin-bottom: 3px;
}

.airport {
    font-size: 14px;
    color: #bbb;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.flight-duration {
    flex: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 15px;
    min-width: 0;
    /* Allows flex items to shrink below content size */
    flex-grow: 1;
    /* Allow this to take up remaining space */
}

.duration-line {
    position: relative;
    width: 100%;
    height: 2px;
    background-color: #555;
    margin: 10px 0;
}

.duration-line .dot {
    position: absolute;
    width: 6px;
    height: 6px;
    background-color: #555;
    border-radius: 50%;
    top: -2px;
}

.duration-line .dot:first-child {
    left: 0;
}

.duration-line .dot:last-child {
    right: 0;
}

.duration-text {
    font-size: 13px;
    color: #aaa;
    margin-bottom: 5px;
}

.stops-text {
    font-size: 12px;
    color: #aaa;
}

.stops-text.nonstop {
    color: #4caf50;
    font-weight: 600;
}

.stops-text.one-stop {
    color: #ff9800;
}

.stops-text.multi-stop {
    color: #f44336;
}

/* Fix for flight action buttons */
.flight-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 15px;
}

/* Fix for Add to Reservations button */
.add-reservation-btn {
    background-color: var(--main-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-weight: 600;
}

.add-reservation-btn:hover {
    background-color: #a52020;
}

.add-reservation-btn.added {
    background-color: #4caf50;
}

/* Fix for Book button */
.book-btn {
    background-color: #2e7d32;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-weight: 600;
}

.book-btn:hover {
    background-color: #1b5e20;
}

/* Fix for External Reservation Button in the sidebar */
.add-reservation-btn-container {
    margin-bottom: 15px;
}

#addExternalReservation.add-reservation-btn {
    background-color: var(--highlight-color);
    color: #333;
    border: none;
    border-radius: 6px;
    padding: 10px 15px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

#addExternalReservation.add-reservation-btn:hover {
    background-color: #e6c200;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

#addExternalReservation.add-reservation-btn i {
    font-size: 14px;
}

/* Fix for modal buttons */
.modal .form-actions .save-btn {
    background-color: var(--highlight-color);
    color: #333;
    border: none;
    border-radius: 4px;
    padding: 10px 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
}

.modal .form-actions .save-btn:hover {
    background-color: #e6c200;
}

.modal .form-actions .cancel-modal-btn {
    background-color: #555;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 15px;
    cursor: pointer;
    transition: all 0.3s;
}

.modal .form-actions .cancel-modal-btn:hover {
    background-color: #666;
}

/* Fix for booking confirmation modal */
.booking-flight-info {
    background-color: #3a3a3a;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 3px solid var(--highlight-color);
}

.booking-flight-info h3 {
    margin-top: 0;
    color: #fff;
    font-size: 18px;
}

.booking-route {
    font-size: 16px;
    margin-bottom: 12px;
    color: #ddd;
}

.booking-time {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #bbb;
    margin-bottom: 12px;
    font-size: 14px;
    background-color: #333;
    padding: 10px;
    border-radius: 6px;
}

.booking-time>div:nth-child(2) {
    color: #777;
    font-size: 12px;
}

.booking-price {
    font-weight: 600;
    color: var(--highlight-color);
    font-size: 18px;
    text-align: right;
    margin-top: 5px;
}

/* Fix for booking confirmation buttons */
.booking-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

.confirm-btn {
    background-color: #4caf50;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.2s;
}

.confirm-btn:hover {
    background-color: #388e3c;
}

.cancel-btn {
    background-color: #555;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.2s;
}

.cancel-btn:hover {
    background-color: #666;
}