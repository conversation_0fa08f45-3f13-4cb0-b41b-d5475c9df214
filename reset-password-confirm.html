<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Vestigia</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="icon" href="images/vestigiaLogo.png" type="image/png">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap">
    <style>
        :root {
            --dark-color: #262626;
            /* Dark Background */
            --main-color: #861818;
            /* Main Red Color */
            --highlight-color: #ffd700;
            /* New Gold highlight */
            --light-color: #f5f5f5;
            /* Light gray text */
            --error-color: red;
            /* Error color */
        }

        * {
            margin: 0;
            padding: 0;
            font-family: 'Open Sans', sans-serif;
            /* Apply Open Sans font */
        }

        html {
            color: var(--light-color);
            text-align: center;
        }

        body {
            min-height: 100vh;
            background-image: url(images/signupBackground.jpg);
            background-size: cover;
            background-position: right;
            overflow: hidden;
        }

        .wrapper {
            box-sizing: border-box;
            background-color: #262626;
            min-height: 100vh;
            /* Adjusted height */
            width: 60%;
            /* Adjusted width */
            padding: 40px;
            /* Increased padding */
            border-radius: 20px;
            /* Adjusted border radius */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            /* Ensure content is spaced out */
            transform: scale(1);
            /* Reset scale */
            margin: auto;
            /* Center the wrapper */
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            /* Center the wrapper */
            text-align: center;
            /* Ensure text is centered */
        }

        form {
            width: min(400px, 100%);
            margin-top: 5px;
            /* Further reduced margin */
            margin-bottom: 5px;
            /* Further reduced margin */
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            /* Further reduced gap */
        }

        form>div {
            width: 100%;
            display: flex;
            justify-content: center;
        }

        form input {
            border-radius: 10px;
            padding: 0.5em;
            /* Further reduced padding */
            border: 2px solid transparent;
            /* Set initial border to transparent */
            background-color: var(--main-color);
            color: var(--highlight-color);
            width: 100%;
            box-sizing: border-box;
            transition: background-color 150ms ease, border-color 150ms ease;
            /* Transition only background-color and border-color */
        }

        form input::placeholder {
            color: var(--highlight-color);
            /* Set placeholder text color to highlight color */
        }

        form input:hover::placeholder {
            color: var(--main-color);
            /* Change placeholder text color to main color on hover */
        }

        .logo {
            width: 250px;
            /* Set a fixed width */
            height: 250px;
            /* Set a fixed height */
            object-fit: contain;
            /* Ensure the image fits within the container without stretching */
            margin-bottom: -75px;
            margin-top: -50px;
        }

        form input:hover {
            background-color: var(--highlight-color);
            border-color: black;
            /* Change border color on hover */
            color: var(--main-color);
            cursor: pointer;
            /* Added to change the cursor to pointer */
        }

        form input:focus {
            outline: none;
        }

        form button {
            margin-top: 5px;
            /* Further reduced margin */
            border: none;
            border-radius: 1000px;
            padding: .5em 2em;
            /* Further reduced padding */
            background-color: var(--main-color);
            color: var(--highlight-color);
            font-weight: 600;
            cursor: pointer;
            /* Added to change the cursor to pointer */
        }

        form button:hover {
            background-color: var(--highlight-color);
            color: var(--main-color);
        }

        form button.reset {
            margin-top: 5px;
            /* Further reduced margin */
            border: none;
            border-radius: 1000px;
            padding: 0.5em 1em;
            /* Further reduced padding */
            font-size: 0.9em;
            /* Further reduced font size */
            background: var(--highlight-color);
            color: var(--dark-color);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease-in-out;
            box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2);
        }

        form button.reset:hover {
            background: var(--main-color);
            color: var(--highlight-color);
            transform: scale(1.05);
            box-shadow: 0px 6px 8px rgba(0, 0, 0, 0.3);
        }

        a:hover {
            color: var(--light-color);
            text-decoration: underline;
        }

        a {
            color: var(--highlight-color);
        }

        .links {
            margin-top: 5px;
            /* Further reduced margin */
            text-align: center;
            /* Ensure text is centered */
        }

        .alert {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            width: 100%;
            box-sizing: border-box;
        }

        .alert-danger {
            background-color: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid #dc3545;
        }

        .alert-success {
            background-color: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid #28a745;
        }

        .alert-info {
            background-color: rgba(23, 162, 184, 0.2);
            color: #17a2b8;
            border: 1px solid #17a2b8;
        }

        .input-err {
            color: var(--error-color);
            font-size: 0.8em;
            text-align: left;
            width: 100%;
            margin-top: 2px;
        }

        @media (max-width: 1100px) {
            .wrapper {
                width: min(600px, 100%);
                border-radius: 0;
            }
        }

        @media (max-width: 768px) {
            body {
                background-image: none;
                /* Hide background image on smaller screens */
                background-color: var(--dark-color);
                /* Set a fallback background color */
            }

            .wrapper {
                width: 100%;
                border-radius: 0;
            }
        }

        @media (min-width: 769px) and (max-width: 1100px) {
            .wrapper {
                width: min(600px, 100%);
                border-radius: 0;
            }
        }

        @media (min-width: 1101px) {
            .wrapper {
                width: max(40%);
                border-radius: 0 20px 20px 0;
            }
        }

        .back-link {
            position: absolute;
            top: 20px;
            left: 20px;
            color: var(--highlight-color);
            font-size: 1.2em;
            text-decoration: none;
        }

        .back-link:hover {
            color: var(--light-color);
        }
    </style>
</head>

<body>
    <a href="index.html" class="back-link"><i class="fas fa-arrow-left"></i> Back to Home</a>
    <div class="wrapper">
        <img src="images/vestigiaLogoBig.png" alt="Vestigia Logo" class="logo">
        <div id="message-container"></div>
        <form id="resetPasswordForm">
            <h2 style="color: var(--highlight-color);">Reset Your Password</h2>
            <p style="color: var(--light-color); margin-bottom: 20px;">Enter your new password below.</p>

            <div class="input-group">
                <input type="password" name="password" id="password" placeholder="New Password" required />
                <div class="input-err" id="password-error"></div>
            </div>
            <div class="input-group">
                <input type="password" name="confirm_password" id="confirm_password" placeholder="Confirm New Password" required />
                <div class="input-err" id="confirm-password-error"></div>
            </div>

            <button type="submit" class="reset">
                Reset Password
            </button>
        </form>

        <div class="links">
            <br>
            <p>Remember your password? <a href="auth-login.html">Login Here</a><br>&copy; 2025 Vestigia. All Rights Reserved.</p>
        </div>
    </div>

    <!-- Include Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Include Authentication functionality -->
    <script src="js/auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const resetPasswordForm = document.getElementById('resetPasswordForm');
            const messageContainer = document.getElementById('message-container');
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirm_password');
            const passwordError = document.getElementById('password-error');
            const confirmPasswordError = document.getElementById('confirm-password-error');

            // Initialize Supabase client
            const supabaseUrl = 'https://vwpyvjoycnrgmpatzqzk.supabase.co';
            const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ3cHl2am95Y25yZ21wYXR6cXprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDExMzk0MTksImV4cCI6MjA1NjcxNTQxOX0.sG7amfnd8UTMv3zzmaa3KKyPRTzW70cDW9VVcUCSq4c';
            // Use the existing Supabase client that was initialized in supabase-client.js

            // Handle form submission
            resetPasswordForm.addEventListener('submit', async function (e) {
                e.preventDefault();

                // Clear previous error messages
                passwordError.textContent = '';
                confirmPasswordError.textContent = '';
                messageContainer.innerHTML = '';

                const password = passwordInput.value.trim();
                const confirmPassword = confirmPasswordInput.value.trim();

                // Basic validation
                let isValid = true;

                if (!password) {
                    passwordError.textContent = 'Password is required';
                    isValid = false;
                } else if (password.length < 6) {
                    passwordError.textContent = 'Password must be at least 6 characters long';
                    isValid = false;
                }

                if (!confirmPassword) {
                    confirmPasswordError.textContent = 'Please confirm your password';
                    isValid = false;
                } else if (password !== confirmPassword) {
                    confirmPasswordError.textContent = 'Passwords do not match';
                    isValid = false;
                }

                if (!isValid) return;

                try {
                    // Show loading message
                    messageContainer.innerHTML = '<div class="alert alert-info">Updating your password...</div>';

                    // Update password using Supabase
                    const { error } = await supabase.auth.updateUser({
                        password: password
                    });

                    if (error) {
                        throw new Error(error.message);
                    }

                    // Show success message
                    messageContainer.innerHTML = '<div class="alert alert-success">Password updated successfully! Redirecting to login page...</div>';

                    // Redirect to login page after successful password reset
                    setTimeout(() => {
                        window.location.href = 'auth-login.html';
                    }, 3000);
                } catch (error) {
                    // Show error message
                    messageContainer.innerHTML = `<div class="alert alert-danger">${error.message || 'Failed to update password. Please try again.'}</div>`;
                }
            });
        });
    </script>
</body>

</html>
