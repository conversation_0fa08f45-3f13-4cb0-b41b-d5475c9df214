// Netlify function for searching locations using Nominatim API
const axios = require('axios');

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  try {
    // Parse query parameters
    const params = event.queryStringParameters || {};
    const { q } = params;

    // Validate required parameters
    if (!q) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing required parameter: q (query)' })
      };
    }

    // Call Nominatim API with proper User-Agent header
    const response = await axios({
      method: 'get',
      url: 'https://nominatim.openstreetmap.org/search',
      params: {
        format: 'json',
        q
      },
      headers: {
        'User-Agent': 'Vestigia-Travel-App/1.0'
      },
      timeout: 8000 // 8 seconds timeout
    });

    // Return successful response
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify(response.data)
    };
  } catch (error) {
    console.error('--- Geocode Search Error ---');
    console.error('Timestamp:', new Date().toISOString());
    console.error('Query Parameters:', event.queryStringParameters);
    console.error('Error Code:', error.code);
    console.error('Error Message:', error.message);
    if (error.response) {
      console.error('External API Status:', error.response.status);
      // Log a snippet of response data to avoid overly large logs or issues with binary data
      const responseData = error.response.data;
      let responseDataSnippet = '';
      if (typeof responseData === 'string') {
        responseDataSnippet = responseData.substring(0, 500);
      } else if (responseData) {
        try {
          responseDataSnippet = JSON.stringify(responseData).substring(0, 500);
        } catch (e) {
          responseDataSnippet = '[Could not stringify response data]';
        }
      } else {
        responseDataSnippet = '[No response data]';
      }
      console.error('External API Response Data Snippet:', responseDataSnippet);
    } else {
      console.error('No error.response object. This might be a network issue or client-side timeout with axios.');
    }
    // Log a simplified error object for better diagnostics
    const simplifiedError = {
        message: error.message,
        name: error.name,
        code: error.code,
        // Avoid logging full config as it can be verbose and contain sensitive info
        config_url: error.config ? error.config.url : undefined,
        config_method: error.config ? error.config.method : undefined,
        config_timeout: error.config ? error.config.timeout : undefined,
        stack: error.stack ? error.stack.substring(0, 500) : undefined // Log a snippet of the stack trace
    };
    console.error('Simplified error object:', JSON.stringify(simplifiedError));
    console.error('--- End Geocode Search Error ---');

    // Return error response
    return {
      statusCode: error.response?.status || 500, // If axios times out, error.response is undefined, so 500.
      headers,
      body: JSON.stringify({
        error: 'Failed to search locations',
        detail: error.message // Provide more specific detail to the client
      })
    };
  }
};
