/**
 * Itinerary Sync Module for Vestigia
 * Similar to marker sync functionality but for events
 *
 * This module handles syncing events between local storage and Supabase,
 * with duplicate prevention and conflict resolution.
 */

console.log('Loading itinerary sync module...');

/**
 * Save events to local storage
 * @param {Array} events - Array of events to save
 */
function saveEventsToLocalStorage(events) {
  try {
    console.log(`Saving ${events.length} events to localStorage`);
    localStorage.setItem('events', JSON.stringify(events));
    console.log('Events saved to localStorage successfully');
  } catch (error) {
    console.error('Error saving events to localStorage:', error);
  }
}

/**
 * Clean up duplicate events in local storage
 * @returns {number} Number of unique events after cleanup
 */
function cleanupLocalStorageEventDuplicates() {
  try {
    console.log('Cleaning up duplicate events in local storage...');
    const eventsJson = localStorage.getItem('events');
    const storedEvents = JSON.parse(eventsJson) || [];
    console.log(`Found ${storedEvents.length} events in local storage`);

    if (storedEvents.length === 0) {
      return 0;
    }

    // Create a map to track unique events
    const uniqueEvents = [];
    const uniqueKeys = new Set();

    storedEvents.forEach(event => {
      // Create a unique key for each event based on id, title, start time, and location
      const key = `${event.id || 'no-id'}_${event.title || 'no-title'}_${event.start || 'no-start'}_${event.location || 'no-location'}`;

      if (!uniqueKeys.has(key)) {
        uniqueKeys.add(key);
        uniqueEvents.push(event);
      } else {
        console.log(`Removed duplicate event from local storage: ${event.title} at ${event.start}`);
      }
    });

    console.log(`After cleanup: ${uniqueEvents.length} unique events`);

    // Save the deduplicated events back to local storage
    localStorage.setItem('events', JSON.stringify(uniqueEvents));

    return uniqueEvents.length;
  } catch (error) {
    console.error("Error cleaning up local storage events:", error);
    return 0;
  }
}

/**
 * Load events from local storage
 * @returns {Array} Array of events from local storage
 */
function loadEventsFromLocalStorage() {
  try {
    const eventsJson = localStorage.getItem('events');
    const events = JSON.parse(eventsJson) || [];
    console.log(`Loaded ${events.length} events from localStorage`);
    return events;
  } catch (error) {
    console.error('Error loading events from localStorage:', error);
    return [];
  }
}

/**
 * Load events from Supabase
 * @returns {Promise<Array>} Array of events from Supabase
 */
async function loadEventsFromSupabase() {
  try {
    console.log('Loading events from Supabase...');
    const user = await checkAuthentication();
    if (!user) {
      console.log('User not authenticated, skipping Supabase load');
      return [];
    }
    const events = await window.itineraryAPI.getUserEvents(user.id);
    console.log(`Loaded ${events.length} events from Supabase`);
    return events;
  } catch (error) {
    console.error('Error loading events from Supabase:', error);
    return [];
  }
}

/**
 * Clean up duplicate events in Supabase
 * @returns {Promise<Object>} Cleanup result
 */
async function cleanupSupabaseEventDuplicates() {
  try {
    const user = await checkAuthentication();
    if (!user) {
      console.log('User not authenticated, skipping Supabase cleanup');
      return { success: false, message: 'User not authenticated' };
    }

    console.log(`Cleaning up duplicate events in Supabase for user ${user.id}`);

    // Get all events for this user
    const events = await window.itineraryAPI.getUserEvents(user.id);
    if (!events || !Array.isArray(events)) {
      console.error('Invalid response from getUserEvents:', events);
      return {
        error: 'Invalid response from getUserEvents',
        success: false,
        duplicates_removed: 0
      };
    }

    console.log(`Fetched ${events.length} events for deduplication`);

    // Track unique events using a Map with compound key
    const uniqueEvents = new Map();
    const duplicates = [];

    // Sort events by created_at to ensure we keep the oldest entries
    const sortedEvents = [...events].sort((a, b) => {
      const dateA = a.created_at ? new Date(a.created_at) : new Date(0);
      const dateB = b.created_at ? new Date(b.created_at) : new Date(0);
      return dateA - dateB;
    });

    sortedEvents.forEach(event => {
      // Create a unique key based on event_id, title, start time, and location
      const key = `${event.event_id || event.id}_${event.title}_${event.start_time || event.start}_${event.location || ''}`;

      if (!uniqueEvents.has(key)) {
        uniqueEvents.set(key, event);
      } else {
        // If we find a duplicate, keep the one with the earlier created_at
        const existing = uniqueEvents.get(key);
        if (new Date(event.created_at) < new Date(existing.created_at)) {
          duplicates.push(existing.id);
          uniqueEvents.set(key, event);
        } else {
          duplicates.push(event.id);
        }
      }
    });

    // If we found duplicates, delete them
    if (duplicates.length > 0) {
      // Delete events one by one to ensure consistency
      for (const id of duplicates) {
        try {
          await window.itineraryAPI.deleteUserEvent(id, user.id);
        } catch (deleteError) {
          console.error(`Error deleting event ${id}:`, deleteError);
          // Continue with other deletions even if one fails
        }
      }
      return {
        success: true,
        message: `Cleaned up ${duplicates.length} duplicate events`,
        duplicates_removed: duplicates.length
      };
    }

    return {
      success: true,
      message: "No duplicates found",
      duplicates_removed: 0
    };
  } catch (error) {
    console.error('Error in cleanupSupabaseEventDuplicates:', error);
    return {
      error: error.message,
      success: false,
      duplicates_removed: 0
    };
  }
}

/**
 * Merge events from different sources, removing duplicates
 * @param {Array} localEvents - Events from local storage
 * @param {Array} remoteEvents - Events from Supabase
 * @returns {Array} Merged and deduplicated events
 */
function mergeEvents(localEvents, remoteEvents) {
  console.log(`Merging ${localEvents.length} local events with ${remoteEvents.length} remote events`);

  const eventMap = new Map();

  // Add local events first
  localEvents.forEach(event => {
    if (event && event.id) {
      // Create a more comprehensive key for better deduplication
      const key = `${event.id}_${event.title}_${event.start}_${event.location || ''}`;
      eventMap.set(key, { ...event, source: 'local' });
    }
  });

  // Add remote events, overriding local ones (remote takes precedence)
  remoteEvents.forEach(event => {
    if (event && (event.id || event.event_id)) {
      // Handle both id and event_id fields from Supabase
      const eventId = event.event_id || event.id;
      const key = `${eventId}_${event.title}_${event.start_time || event.start}_${event.location || ''}`;

      // Transform Supabase event format to match local format
      const normalizedEvent = {
        id: eventId,
        title: event.title,
        start: event.start_time || event.start,
        end: event.end_time || event.end,
        description: event.description,
        location: event.location,
        category: event.category,
        source: 'remote'
      };

      eventMap.set(key, normalizedEvent);
    }
  });

  // Convert back to array and remove source property
  const mergedEvents = Array.from(eventMap.values())
    .map(({ source, ...event }) => event)
    .filter(event => event && event.id);

  console.log(`Merged into ${mergedEvents.length} unique events`);
  return mergedEvents;
}

/**
 * Sync events with Supabase
 * @param {Array} events - Events to sync
 * @returns {Promise<Object>} Sync results
 */
async function syncEventsWithSupabase(events) {
  try {
    console.log(`Syncing ${events.length} events with Supabase`);

    // Check if user is authenticated using unified auth
    const user = await checkAuthentication();
    console.log('syncEventsWithSupabase - Authentication check result:', user);
    if (!user) {
      console.log('User not authenticated, skipping Supabase sync');
      return { success: false, error: 'Not authenticated' };
    }

    console.log('Syncing events with Supabase for user:', user.id || user.email);

    // Use the itineraryAPI client to sync events
    const result = await window.itineraryAPI.syncUserEvents(user.id, events);
    console.log('Events synced with Supabase:', result);
    return { success: true, data: result };
  } catch (error) {
    console.error('Error during Supabase sync:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Check if user is authenticated using multiple auth sources
 * @returns {Promise<Object|null>} User object if authenticated, null otherwise
 */
async function checkAuthentication() {
  try {
    console.log('checkAuthentication - Starting auth check...');

    // First try Supabase auth
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      console.log('checkAuthentication - User authenticated via Supabase:', user.email);
      return user;
    }
    console.log('checkAuthentication - No Supabase user found');

    // If Supabase auth fails, check the old auth system
    console.log('checkAuthentication - Checking old auth system...');
    console.log('checkAuthentication - auth object available:', typeof auth !== 'undefined');
    if (typeof auth !== 'undefined') {
      console.log('checkAuthentication - auth.isLoggedIn available:', typeof auth.isLoggedIn === 'function');
      if (auth.isLoggedIn) {
        console.log('checkAuthentication - auth.isLoggedIn():', auth.isLoggedIn());
      }
    }

    if (typeof auth !== 'undefined' && auth.isLoggedIn && auth.isLoggedIn()) {
      console.log('checkAuthentication - User authenticated via old auth system:', auth.user?.email);
      // Try to get a valid session for API calls
      const sessionStr = localStorage.getItem('vestigia_session');
      console.log('checkAuthentication - Session string found:', !!sessionStr);
      if (sessionStr) {
        const session = JSON.parse(sessionStr);
        const isExpired = new Date(session.expires_at * 1000) <= new Date();
        console.log('checkAuthentication - Session expired:', isExpired);
        // Check if session is not expired
        if (!isExpired) {
          const userObj = {
            id: auth.user.id,
            email: auth.user.email,
            access_token: session.access_token
          };
          console.log('checkAuthentication - Returning user object:', userObj);
          return userObj;
        }
      }
    }

    console.log('checkAuthentication - No valid authentication found');
    return null;
  } catch (error) {
    console.error('checkAuthentication - Error checking authentication:', error);
    return null;
  }
}

// Track if sync is in progress to prevent multiple simultaneous syncs
let syncInProgress = false;

/**
 * Initialize event sync - load from both sources and merge
 * @returns {Promise<Array>} Merged events
 */
async function initEventSync() {
  if (syncInProgress) {
    console.log('Sync already in progress, skipping...');
    return loadEventsFromLocalStorage();
  }

  syncInProgress = true;
  console.log('Initializing event sync...');

  try {
    // First, clean up local storage duplicates
    console.log('Cleaning up local storage duplicates...');
    cleanupLocalStorageEventDuplicates();

    // Load events from local storage
    const localEvents = loadEventsFromLocalStorage();
    console.log(`Loaded ${localEvents.length} events from localStorage`);

    // Check if user is authenticated
    const user = await checkAuthentication();
    const isAuthenticated = !!user;
    console.log('User authenticated:', isAuthenticated);

    if (!isAuthenticated) {
      // Not authenticated, just use local events
      console.log('User not authenticated, using local events only');
      return localEvents;
    }

    // If authenticated, load and merge remote events
    try {
      console.log('Attempting to load events from Supabase...');
      const remoteEvents = await loadEventsFromSupabase();
      console.log(`Loaded ${remoteEvents.length} events from Supabase`);

      // Clean up Supabase duplicates if we have events
      if (remoteEvents.length > 0) {
        console.log('Cleaning up Supabase duplicates...');
        const cleanupResult = await cleanupSupabaseEventDuplicates();
        console.log('Supabase cleanup result:', cleanupResult);

        // Reload events after cleanup if duplicates were removed
        if (cleanupResult.success && cleanupResult.duplicates_removed > 0) {
          console.log('Reloading events after Supabase cleanup...');
          const cleanedRemoteEvents = await loadEventsFromSupabase();
          console.log(`Reloaded ${cleanedRemoteEvents.length} events after cleanup`);

          // Merge events
          const mergedEvents = mergeEvents(localEvents, cleanedRemoteEvents);
          console.log(`Merged into ${mergedEvents.length} total events`);

          // Update local storage with merged events
          saveEventsToLocalStorage(mergedEvents);
          console.log('Saved merged events to local storage');

          return mergedEvents;
        }
      }

      // Merge events
      const mergedEvents = mergeEvents(localEvents, remoteEvents);
      console.log(`Merged into ${mergedEvents.length} total events`);

      // Update local storage with merged events
      saveEventsToLocalStorage(mergedEvents);
      console.log('Saved merged events to local storage');

      // Sync merged events back to Supabase (in case local had new events)
      console.log('Attempting to sync merged events back to Supabase...');
      const syncResult = await syncEventsWithSupabase(mergedEvents);
      console.log('Sync result:', syncResult);

      return mergedEvents;
    } catch (error) {
      console.error('Error during event sync initialization:', error);

      // Fall back to local events if sync fails
      console.log('Falling back to local events due to sync error');
      return localEvents;
    }
  } catch (error) {
    console.error('Error in initEventSync:', error);
    // Return empty array in case of error
    return [];
  } finally {
    syncInProgress = false;
  }
}

/**
 * Add a new event and sync
 * @param {Object} event - Event to add
 * @returns {Promise<Array>} Updated events array
 */
async function addEvent(event) {
  console.log('Adding new event:', event.title);

  try {
    // Load current events
    let events = loadEventsFromLocalStorage();

    // Add new event
    events.push(event);

    // Save to local storage
    saveEventsToLocalStorage(events);

    // Save to Supabase if authenticated
    const user = await checkAuthentication();
    if (user) {
      console.log('User authenticated, saving event to Supabase...');
      try {
        // Transform event object to match API expectations
        const eventForAPI = {
          user_id: user.id,
          event_id: event.id,
          title: event.title,
          start: event.start,
          end: event.end,
          description: event.description,
          location: event.location,
          category: event.category
        };
        console.log('Transformed event for API:', eventForAPI);
        await window.itineraryAPI.saveUserEvent(eventForAPI);
        console.log('Event saved to Supabase successfully');
      } catch (error) {
        console.error('Error saving event to Supabase:', error);
        // Continue even if Supabase save fails
      }
    }

    console.log('Event added successfully');
    return events;
  } catch (error) {
    console.error('Error adding event:', error);
    throw error;
  }
}

/**
 * Update an existing event and sync
 * @param {Object} updatedEvent - Updated event data
 * @returns {Promise<Array>} Updated events array
 */
async function updateEvent(updatedEvent) {
  console.log('Updating event:', updatedEvent.id);

  try {
    // Load current events
    let events = loadEventsFromLocalStorage();

    // Update the event
    events = events.map(event =>
      event.id === updatedEvent.id ? updatedEvent : event
    );

    // Save to local storage
    saveEventsToLocalStorage(events);

    // Update in Supabase if authenticated
    const user = await checkAuthentication();
    if (user) {
      console.log('User authenticated, updating event in Supabase...');
      try {
        // Transform event object to match API expectations
        const eventForAPI = {
          user_id: user.id,
          event_id: updatedEvent.id,
          title: updatedEvent.title,
          start: updatedEvent.start,
          end: updatedEvent.end,
          description: updatedEvent.description,
          location: updatedEvent.location,
          category: updatedEvent.category
        };
        console.log('Transformed updated event for API:', eventForAPI);
        await window.itineraryAPI.saveUserEvent(eventForAPI);
        console.log('Event updated in Supabase successfully');
      } catch (error) {
        console.error('Error updating event in Supabase:', error);
        // Continue even if Supabase update fails
      }
    }

    console.log('Event updated successfully');
    return events;
  } catch (error) {
    console.error('Error updating event:', error);
    throw error;
  }
}

/**
 * Delete an event and sync
 * @param {string} eventId - ID of event to delete
 * @returns {Promise<Array>} Updated events array
 */
async function deleteEvent(eventId) {
  console.log('Deleting event:', eventId);

  try {
    // Load current events
    let events = loadEventsFromLocalStorage();

    // Remove the event
    events = events.filter(event => event.id !== eventId);

    // Save to local storage
    saveEventsToLocalStorage(events);

    // Delete from Supabase if authenticated
    const user = await checkAuthentication();
    if (user) {
      console.log('User authenticated, deleting from Supabase...');
      try {
        await window.itineraryAPI.deleteUserEvent(eventId, user.id);
      } catch (error) {
        console.error('Error deleting from Supabase:', error);
        // Continue even if Supabase deletion fails
      }
    }

    console.log('Event deleted successfully');
    return events;
  } catch (error) {
    console.error('Error deleting event:', error);
    throw error;
  }
}

// Export the sync functions
const itinerarySync = {
  saveEventsToLocalStorage,
  loadEventsFromLocalStorage,
  loadEventsFromSupabase,
  mergeEvents,
  syncEventsWithSupabase,
  initEventSync,
  addEvent,
  updateEvent,
  deleteEvent,
  cleanupLocalStorageEventDuplicates,
  cleanupSupabaseEventDuplicates
};

console.log('Itinerary sync module loaded, setting up itinerarySync object');
window.itinerarySync = itinerarySync;
console.log('itinerarySync object created with methods:', Object.keys(itinerarySync));
