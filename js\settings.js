/**
 * Settings Page Functionality
 * Handles user settings and profile updates
 */

document.addEventListener('DOMContentLoaded', () => {
    // Load user data when the page loads
    loadUserData();
    
    // Setup form submission handlers
    setupFormHandlers();
    
    // Setup delete account button
    const deleteBtn = document.getElementById('delete-account');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', handleDeleteAccount);
    }
});

/**
 * Load the current user's data into the form
 */
async function loadUserData() {
    try {
        // Get the current user's data from the auth module
        const user = auth.getCurrentUser();
        
        if (user) {
            // Populate the profile form with user data
            document.getElementById('full-name').value = user.user_metadata?.full_name || '';
            document.getElementById('email').value = user.email || '';
        } else {
            // If no user is logged in, redirect to login
            window.location.href = 'auth-login.html';
        }
    } catch (error) {
        console.error('Error loading user data:', error);
        showNotification('Failed to load user data. Please try again.', 'error');
    }
}

/**
 * Setup form submission handlers
 */
function setupFormHandlers() {
    const profileForm = document.getElementById('profile-form');
    const passwordForm = document.getElementById('password-form');
    
    if (profileForm) {
        profileForm.addEventListener('submit', handleProfileUpdate);
    }
    
    if (passwordForm) {
        passwordForm.addEventListener('submit', handlePasswordChange);
    }
}

/**
 * Handle profile update form submission
 * @param {Event} e - Form submit event
 */
async function handleProfileUpdate(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const fullName = formData.get('fullName');
    const email = formData.get('email');
    
    try {
        // TODO: Call your backend API to update the user's profile
        // This is a placeholder - replace with actual API call
        console.log('Updating profile with:', { fullName, email });
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        showNotification('Profile updated successfully!', 'success');
    } catch (error) {
        console.error('Error updating profile:', error);
        showNotification('Failed to update profile. Please try again.', 'error');
    }
}

/**
 * Handle password change form submission
 * @param {Event} e - Form submit event
 */
async function handlePasswordChange(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const currentPassword = formData.get('currentPassword');
    const newPassword = formData.get('newPassword');
    const confirmPassword = formData.get('confirmPassword');
    
    // Basic client-side validation
    if (newPassword !== confirmPassword) {
        showNotification('New passwords do not match!', 'error');
        return;
    }
    
    if (newPassword.length < 8) {
        showNotification('Password must be at least 8 characters long', 'error');
        return;
    }
    
    try {
        // TODO: Call your backend API to update the password
        // This is a placeholder - replace with actual API call
        console.log('Updating password...');
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Clear the form
        e.target.reset();
        showNotification('Password updated successfully!', 'success');
    } catch (error) {
        console.error('Error updating password:', error);
        showNotification('Failed to update password. Please try again.', 'error');
    }
}

/**
 * Handle account deletion
 */
async function handleDeleteAccount() {
    if (!confirm('Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently lost.')) {
        return;
    }
    
    try {
        // TODO: Call your backend API to delete the account
        // This is a placeholder - replace with actual API call
        console.log('Deleting account...');
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Logout the user after account deletion
        await auth.logout();
        
        // Redirect to home page
        window.location.href = 'index.html';
    } catch (error) {
        console.error('Error deleting account:', error);
        showNotification('Failed to delete account. Please try again.', 'error');
    }
}

/**
 * Show a notification to the user
 * @param {string} message - The message to display
 * @param {string} type - The type of notification (success, error, warning, info)
 */
function showNotification(message, type = 'info') {
    // Remove any existing notifications
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Add to the page
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => notification.remove(), 300);
    }, 5000);
}

// Add some basic styles for notifications
const style = document.createElement('style');
style.textContent = `
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 25px;
        border-radius: 4px;
        color: white;
        font-weight: 500;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        animation: slideIn 0.3s ease-out;
        max-width: 350px;
    }
    
    .notification.success {
        background-color: #2ecc71;
    }
    
    .notification.error {
        background-color: #e74c3c;
    }
    
    .notification.warning {
        background-color: #f39c12;
    }
    
    .notification.info {
        background-color: #3498db;
    }
    
    .notification.fade-out {
        animation: fadeOut 0.3s ease-out;
        opacity: 0;
    }
    
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }
`;

document.head.appendChild(style);
