<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Vestigia</title>
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Playfair+Display:wght@700;900&display=swap">
    <link rel="icon" type="image/png" sizes="96x96" href="favicon/favicon-96x96.png">
    <link rel="icon" href="favicon/favicon.ico">
    <style>
        /* Add smooth scrolling and box sizing */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        html {
            scroll-behavior: smooth;
        }
        
        body {
            overflow-x: hidden;
        }
        
        .error-message, .success-message {
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
            font-size: 0.9em;
        }
        
        .error-message {
            color: #e74c3c;
            background-color: #fde8e8;
        }
        
        .success-message {
            color: #2ecc71;
            background-color: #e8f8f0;
        }
    </style>
</head>
<body class="auth-page">
    <!-- Navbar will be injected here by navbar-loader.js -->
    <div id="navbar-container"></div>
    <div class="auth-overlay"></div>

    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header" style="background: transparent;">
                <img src="images/vestigiaLogoBig.png" alt="Vestigia Logo" class="auth-logo" style="background: #262626; padding: 10px; border-radius: 8px;">
                <h1>Welcome Back</h1>
                <p>Sign in to your Vestigia account</p>
            </div>
            
            <form class="auth-form" id="loginForm">
                <div id="error-message" class="error-message"></div>
                <div class="auth-form-content">
                    <div class="auth-form-column">
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <div class="input-with-icon">
                                <i class="fas fa-envelope"></i>
                                <input type="email" id="email" name="email" placeholder="Enter your email" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <div class="password-label-container">
                                <label for="password">Password</label>
                            </div>
                            <div class="input-with-icon">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="password" name="password" placeholder="Enter your password" required>
                            </div>
                            <div class="forgot-password-container">
                                <a href="forgot-password.html" class="forgot-password">Forgot Password?</a>
                            </div>
                        </div>
                        
                        <div class="form-group remember-me">
                            <input type="checkbox" id="remember" name="remember">
                            <label for="remember">Remember me</label>
                        </div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary btn-block">Sign In</button>
                </div>
                
                <div class="divider">
                    <span>or continue with</span>
                </div>
                
                <div class="social-login">
                    <a href="#" class="social-btn btn-google">
                        <i class="fab fa-google"></i>
                        Continue with Google
                    </a>
                    <a href="#" class="social-btn btn-facebook">
                        <i class="fab fa-facebook-f"></i>
                        Continue with Facebook
                    </a>
                </div>
                
<div class="auth-footer">
                    <p>Don't have an account? <a href="auth-signup.html">Sign Up</a></p>
                </div>
            </form>
        </div>
    </div>

    <script src="js/navbar-loader.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Add any additional scripts here
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize social login buttons
            document.querySelectorAll('.social-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const provider = this.classList.contains('btn-google') ? 'google' : 'facebook';
                    // Handle social login
                    console.log(`Logging in with ${provider}`);
                    // You would typically redirect to your OAuth endpoint here
                    // window.location.href = `/auth/${provider}`;
                });
            });
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const errorMessage = document.getElementById('error-message');
            const togglePassword = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');

            // Check for success message after signup
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('signup') === 'success') {
                // Create success message element if it doesn't exist
                let successMessage = document.getElementById('success-message');
                if (!successMessage) {
                    successMessage = document.createElement('div');
                    successMessage.id = 'success-message';
                    successMessage.className = 'success-message';
                    loginForm.insertBefore(successMessage, loginForm.firstChild);
                }
                successMessage.textContent = 'Registration successful! Please log in with your credentials.';
                successMessage.style.display = 'block';
                
                // Remove the success parameter from URL without refreshing
                const newUrl = window.location.pathname;
                window.history.replaceState({}, document.title, newUrl);
            }

            // Toggle password visibility
            if (togglePassword) {
                togglePassword.addEventListener('click', function() {
                    const type = passwordInput.type === 'password' ? 'text' : 'password';
                    passwordInput.type = type;
                    this.classList.toggle('fa-eye');
                    this.classList.toggle('fa-eye-slash');
                });
            }


            // Handle form submission
            if (loginForm) {
                loginForm.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    
                    const email = document.getElementById('email').value;
                    const password = document.getElementById('password').value;
                    const remember = document.getElementById('remember').checked;
                    
                    try {
                        // Clear any previous error messages
                        errorMessage.textContent = '';
                        errorMessage.style.display = 'none';
                        
                        // Call auth.login() which will handle the redirect
                        await auth.login(email, password);
                        
                        // If we get here, login was successful and redirect should happen
                    } catch (error) {
                        console.error('Login error:', error);
                        errorMessage.textContent = error.message || 'An error occurred. Please try again.';
                        errorMessage.style.display = 'block';
                    }
                });
            }
        });
    </script>
</body>
</html>
