-- Simple SQL script to create user_markers table with all needed columns
-- Run this directly in the Supabase SQL Editor

-- Drop the table if it exists and start fresh
DROP TABLE IF EXISTS user_markers;

-- Create the table with all needed columns
CREATE TABLE user_markers (
  id SERIAL PRIMARY KEY,
  user_id TEXT NOT NULL,
  lat FLOAT NOT NULL,
  lng FLOAT NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  place_name TEXT,
  is_poi BOOLEAN DEFAULT FALSE,
  is_custom BOOLEAN DEFAULT FALSE,
  wiki_name TEXT,
  added_to_itinerary BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for faster queries
CREATE INDEX idx_user_markers_user_id ON user_markers(user_id);
CREATE INDEX idx_user_markers_location ON user_markers(lat, lng);

-- Enable Row Level Security
ALTER TABLE user_markers ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to select only their own markers
CREATE POLICY select_own_markers ON user_markers
  FOR SELECT USING (auth.uid()::text = user_id);

-- Create policy to allow users to insert only their own markers
CREATE POLICY insert_own_markers ON user_markers
  FOR INSERT WITH CHECK (auth.uid()::text = user_id);

-- Create policy to allow users to update only their own markers
CREATE POLICY update_own_markers ON user_markers
  FOR UPDATE USING (auth.uid()::text = user_id);

-- Create policy to allow users to delete only their own markers
CREATE POLICY delete_own_markers ON user_markers
  FOR DELETE USING (auth.uid()::text = user_id);

-- Create policy to allow service role to access all markers
CREATE POLICY service_role_access ON user_markers
  FOR ALL USING (auth.role() = 'service_role');
