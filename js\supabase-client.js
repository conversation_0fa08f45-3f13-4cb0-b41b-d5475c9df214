/**
 * Supabase Client for Vestigia
 *
 * This file initializes the Supabase client with the public key
 * and provides functions to interact with the Supabase database.
 */

// Initialize the Supabase client with autoRefreshToken and persistSession enabled
// Check if already initialized to prevent duplicate declarations
if (typeof window.SUPABASE_URL === 'undefined') {
  window.SUPABASE_URL = 'https://vwpyvjoycnrgmpatzqzk.supabase.co';
  window.SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ3cHl2am95Y25yZ21wYXR6cXprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDExMzk0MTksImV4cCI6MjA1NjcxNTQxOX0.sG7amfnd8UTMv3zzmaa3KKyPRTzW70cDW9VVcUCSq4c';
}

// Create a single instance of the Supabase client with proper auth settings
// Check if already initialized to prevent duplicate initialization
if (!window.supabaseClient) {
  try {
    // Check if supabase library is available in window
    if (!window.supabase || !window.supabase.createClient) {
      throw new Error('Supabase client library not found in window object');
    }

    // Initialize Supabase client and store it
    window.supabaseClient = window.supabase.createClient(window.SUPABASE_URL, window.SUPABASE_KEY, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        storage: window.localStorage,
        storageKey: 'sb-vwpyvjoycnrgmpatzqzk-auth-token'
      }
    });

    // Create a global reference for backward compatibility
    window.supabase = window.supabaseClient;

    // Verify session on initialization (async)
    window.supabaseClient.auth.getSession().then(({ data: { session } }) => {
      console.log('Supabase client initialized. Session:', session ? 'Active' : 'No active session');
    }).catch(error => {
      console.error('Error checking initial session:', error);
    });

    // Set up auth state change listener
    const { data: { subscription } } = window.supabaseClient.auth.onAuthStateChange((event, session) => {
      console.log('Auth state changed:', event, 'Session:', session ? 'Active' : 'None');
    });

    // Store the subscription to clean up later if needed
    window.supabaseSubscription = subscription;

  } catch (error) {
    console.error('Error initializing Supabase client:', error);
    throw error;
  }
} else {
  console.log('Supabase client already initialized');
  // Ensure global reference exists
  if (!window.supabase) {
    window.supabase = window.supabaseClient;
  }
}

// Create a global airportAPI object
window.airportAPI = {
  /**
   * Search for airports by query
   * @param {string} query - Search query (name, city, IATA code, etc.)
   * @returns {Promise<Array>} Airport search results
   */
  searchAirports: async function (query) {
    if (!query || query.length < 2) return [];

    try {
      console.log('Searching airports with query:', query);

      // Search in the airports table
      // This uses Supabase's built-in text search capabilities
      const { data, error } = await window.supabase
        .from('airports')
        .select('id, name, iata_code, icao_code, municipality, iso_country')
        .or(`name.ilike.%${query}%,iata_code.ilike.%${query}%,municipality.ilike.%${query}%`)
        .order('type', { ascending: false }) // Prioritize large airports
        .limit(10);

      if (error) {
        console.error('Error searching airports:', error);
        return [];
      }

      console.log(`Found ${data.length} airports matching "${query}"`);

      // Format the results to match the expected format
      return data.map(airport => ({
        code: airport.iata_code || airport.icao_code,
        name: airport.name,
        city: airport.municipality || '',
        country: airport.iso_country || '',
        displayName: `${airport.iata_code || airport.icao_code} - ${airport.name}, ${airport.municipality || ''}`
      }));
    } catch (error) {
      console.error('Error in searchAirports:', error);
      return [];
    }
  },

  /**
   * Test the Supabase connection
   * @returns {Promise<boolean>} True if connection is successful
   */
  testConnection: async function () {
    try {
      const { data, error } = await window.supabase
        .from('airports')
        .select('count()', { count: 'exact', head: true });

      if (error) {
        console.error('Error testing Supabase connection:', error);
        return false;
      }

      console.log('Supabase connection successful');
      return true;
    } catch (error) {
      console.error('Error testing Supabase connection:', error);
      return false;
    }
  }
};
