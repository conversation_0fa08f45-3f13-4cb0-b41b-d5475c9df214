/* Booking Confirmation Modal Styles */

/* Add top margin to booking confirmation modal */
#bookingConfirmationModal .modal-content {
    margin: 100px auto 5% auto;
    /* Increased top margin to 100px to avoid navbar overlap */
}

/* Booking details container */
.booking-details {
    background-color: #333;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border-left: 3px solid var(--main-color);
}

/* Flight info in booking details */
.booking-flight-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 15px;
}

.booking-flight-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #444;
}

.booking-carrier {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #fff;
}

.booking-carrier i {
    color: var(--main-color);
}

.booking-flight-number {
    color: #aaa;
    font-size: 14px;
}

.booking-route {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
}

.booking-airport {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.booking-time {
    font-size: 18px;
    font-weight: 600;
    color: #fff;
}

.booking-code {
    font-size: 14px;
    color: #aaa;
}

.booking-route-divider {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    margin: 0 15px;
}

.booking-route-line {
    height: 2px;
    background-color: #555;
    width: 100%;
    position: relative;
}

.booking-route-line:before,
.booking-route-line:after {
    content: '';
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #555;
    top: -2px;
}

.booking-route-line:before {
    left: 0;
}

.booking-route-line:after {
    right: 0;
}

.booking-duration {
    font-size: 12px;
    color: #aaa;
    margin-top: 5px;
}

.booking-details-row {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #444;
}

.booking-detail {
    display: flex;
    flex-direction: column;
}

.booking-detail-label {
    font-size: 12px;
    color: #aaa;
    margin-bottom: 3px;
}

.booking-detail-value {
    font-size: 14px;
    color: #fff;
}

/* Booking action buttons */
.booking-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

.confirm-btn {
    background-color: var(--main-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s;
}

.confirm-btn:hover {
    background-color: #3a8f40;
    transform: translateY(-2px);
}

.cancel-btn {
    background-color: #444;
    color: #ddd;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s;
}

.cancel-btn:hover {
    background-color: #555;
    transform: translateY(-2px);
}

/* Modal paragraph */
.modal-body p {
    text-align: center;
    margin: 15px 0;
    color: #ddd;
    font-size: 16px;
}

/* External Reservation Modal Styles */
#externalReservationModal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.7);
}

#externalReservationModal .modal-content {
    background-color: #222;
    margin: 125px auto 5% auto;
    /* Set top margin to 125px to avoid navbar overlap */
    padding: 0;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    width: 90%;
    max-width: 600px;
    position: relative;
    animation: modalFadeIn 0.3s;
}

#externalReservationModal .modal-header {
    padding: 15px 20px;
    background-color: #333;
    border-bottom: 1px solid #444;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#externalReservationModal .modal-header h4 {
    margin: 0;
    color: #fff;
    font-size: 18px;
}

#externalReservationModal .close-modal {
    color: #aaa;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s;
}

#externalReservationModal .close-modal:hover {
    color: #fff;
}

#externalReservationModal .modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

#externalReservationForm .form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

#externalReservationForm .form-group {
    flex: 1;
    position: relative;
}

/* Trip type styling */
#externalReservationForm .trip-type {
    flex: 0.8;
}

#externalReservationForm .trip-options {
    display: flex;
    gap: 10px;
    margin-top: 5px;
}

#externalReservationForm .trip-option {
    display: flex;
    align-items: center;
    gap: 5px;
    background-color: #333;
    border: 1px solid #444;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
    transition: all 0.2s;
}

#externalReservationForm .trip-option:hover {
    background-color: #444;
}

#externalReservationForm .trip-option input[type="radio"] {
    width: auto;
    margin: 0;
    padding: 0;
}

#externalReservationForm .trip-option label {
    margin: 0;
    cursor: pointer;
    font-size: 13px;
}

#externalReservationForm .trip-option input[type="radio"]:checked+label {
    color: var(--main-color);
    font-weight: 600;
}

#externalReservationForm label {
    display: block;
    margin-bottom: 5px;
    color: #ddd;
    font-size: 14px;
}

#externalReservationForm input,
#externalReservationForm select,
#externalReservationForm textarea {
    width: 100%;
    padding: 10px;
    background-color: #333;
    border: 1px solid #444;
    border-radius: 4px;
    color: #fff;
    font-size: 14px;
}

#externalReservationForm textarea {
    min-height: 80px;
    resize: vertical;
}

#externalReservationForm .input-hint {
    display: block;
    font-size: 12px;
    color: #aaa;
    margin-top: 3px;
}

#externalReservationForm .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

#externalReservationForm .save-btn {
    background-color: var(--main-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s;
}

#externalReservationForm .save-btn:hover {
    background-color: #3a8f40;
    transform: translateY(-2px);
}

/* Animation for modal */
@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}