# Flight Search Setup Guide

This guide will help you set up the Aviasales API integration for the Vestigia website.

## Prerequisites

1. A Netlify account with your site deployed
2. An Aviasales API token (Travelpayouts API key)

## Getting an Aviasales API Token

1. Go to [Travelpayouts](https://www.travelpayouts.com/)
2. Sign up or log in to your account
3. Navigate to your profile and find your API token

## Setting Up Environment Variables in Netlify

1. Go to your Netlify dashboard
2. Select your Vestigia website
3. Go to **Site settings** > **Build & deploy** > **Environment**
4. Click on **Edit variables**
5. Add these two environment variables:
   - **Key**: `AVIASALES_API_TOKEN`
     **Value**: Your Aviasales API token
   - **Key**: `AVIASALES_USER_ID`
     **Value**: Your Aviasales user ID

## Local Development

For local development, create a `.env` file in the root of your project with the following content:

```
AVIASALES_API_TOKEN=your_api_token_here
AVIASALES_USER_ID=your_user_id_here
```

## Testing the Integration

1. Start your local development server:
   ```bash
   netlify dev
   ```
2. Open http://localhost:8888/transportation.html
3. Enter your search criteria and click "Search Flights"
4. You should see flight results appear below the search form

## Troubleshooting

- **No results found**: Ensure your API token is valid and has sufficient permissions
- **API errors**: Check the browser's console and Netlify function logs for detailed error messages
- **CORS issues**: Make sure your Netlify function is properly configured and deployed

## Rate Limits

Be aware of the Aviasales API rate limits:
- Free tier: 1 request per second, 1000 requests per day
- Paid plans offer higher limits

## Support

For additional help, refer to the [Aviasales API Documentation](https://support.travelpayouts.com/hc/en-us/articles/*********-Aviasales-API) or contact Travelpayouts support.
