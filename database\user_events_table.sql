-- SQL script to create user_events table for storing itinerary events
-- Run this directly in the Supabase SQL Editor

-- Create the table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_events (
  id SERIAL PRIMARY KEY,
  user_id TEXT NOT NULL,
  event_id TEXT NOT NULL, -- Client-generated ID for syncing
  title TEXT NOT NULL,
  start_time TIMESTAMP WITH TIME ZONE,
  end_time TIMESTAMP WITH TIME ZONE,
  description TEXT,
  location TEXT,
  category TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_user_events_user_id ON user_events(user_id);
CREATE INDEX IF NOT EXISTS idx_user_events_event_id ON user_events(event_id);
CREATE INDEX IF NOT EXISTS idx_user_events_start_time ON user_events(start_time);

-- Enable Row Level Security
ALTER TABLE user_events ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to select only their own events
DROP POLICY IF EXISTS select_own_events ON user_events;
CREATE POLICY select_own_events ON user_events
  FOR SELECT USING (auth.uid()::text = user_id);

-- Create policy to allow users to insert only their own events
DROP POLICY IF EXISTS insert_own_events ON user_events;
CREATE POLICY insert_own_events ON user_events
  FOR INSERT WITH CHECK (auth.uid()::text = user_id);

-- Create policy to allow users to update only their own events
DROP POLICY IF EXISTS update_own_events ON user_events;
CREATE POLICY update_own_events ON user_events
  FOR UPDATE USING (auth.uid()::text = user_id);

-- Create policy to allow users to delete only their own events
DROP POLICY IF EXISTS delete_own_events ON user_events;
CREATE POLICY delete_own_events ON user_events
  FOR DELETE USING (auth.uid()::text = user_id);

-- Create policy to allow service role to access all events
DROP POLICY IF EXISTS service_role_access_events ON user_events;
CREATE POLICY service_role_access_events ON user_events
  FOR ALL USING (auth.role() = 'service_role');

-- Add policy to allow anonymous role to insert events
DROP POLICY IF EXISTS anon_insert_events ON user_events;
CREATE POLICY anon_insert_events ON user_events
  FOR INSERT TO anon
  WITH CHECK (true);

-- Add policy to allow anonymous role to select events
DROP POLICY IF EXISTS anon_select_events ON user_events;
CREATE POLICY anon_select_events ON user_events
  FOR SELECT TO anon
  USING (true);

-- Add policy to allow anonymous role to update events
DROP POLICY IF EXISTS anon_update_events ON user_events;
CREATE POLICY anon_update_events ON user_events
  FOR UPDATE TO anon
  USING (true);

-- Add policy to allow anonymous role to delete events
DROP POLICY IF EXISTS anon_delete_events ON user_events;
CREATE POLICY anon_delete_events ON user_events
  FOR DELETE TO anon
  USING (true);
