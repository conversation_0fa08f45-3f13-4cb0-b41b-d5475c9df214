/**
 * Flight Search Styles
 * Styles for flight search results, loading states, and error messages
 */

/* Loading state */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--highlight-color);
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Error message */
.error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px;
    text-align: center;
    background-color: #fff5f5;
    border: 1px solid #ffcccc;
    border-radius: 8px;
    margin: 20px 0;
}

.error-message i {
    font-size: 32px;
    color: #e53e3e;
    margin-bottom: 15px;
}

.error-message p {
    margin-bottom: 15px;
    color: #333;
}

.retry-btn {
    background-color: var(--highlight-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.2s;
}

.retry-btn:hover {
    background-color: var(--highlight-hover-color, #c9a227);
}

/* No results */
.no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    text-align: center;
    color: #666;
}

.no-results i {
    font-size: 32px;
    color: #999;
    margin-bottom: 15px;
}

.no-results p {
    margin-bottom: 5px;
}

/* Flight card enhancements */
.flight-card {
    margin-bottom: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    background-color: white;
    transition: transform 0.2s, box-shadow 0.2s;
}

.flight-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stops-text.nonstop {
    color: #22c55e;
    font-weight: 600;
}

.stops-text.one-stop {
    color: #f59e0b;
}

.stops-text.multi-stop {
    color: #ef4444;
}

/* Add to reservations button */
.add-reservation-btn {
    background-color: #4f46e5;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 8px;
    font-size: 14px;
    transition: background-color 0.2s;
}

.add-reservation-btn:hover {
    background-color: #4338ca;
}

.add-reservation-btn.added {
    background-color: #10b981;
    cursor: default;
}

/* Book button */
.book-btn {
    background-color: var(--highlight-color);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.book-btn:hover {
    background-color: var(--highlight-hover-color, #c9a227);
}

/* Flight actions container */
.flight-actions {
    display: flex;
    align-items: center;
    margin-top: 10px;
}

/* Booking confirmation modal enhancements */
.booking-flight-info {
    background-color: #f9fafb;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.booking-flight-info h3 {
    margin-top: 0;
    color: #111827;
}

.booking-route {
    font-size: 16px;
    margin-bottom: 8px;
}

.booking-time {
    color: #4b5563;
    margin-bottom: 8px;
}

.booking-price {
    font-weight: 600;
    color: var(--highlight-color);
    font-size: 18px;
}

.booking-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.confirm-btn {
    background-color: #10b981;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
}

.confirm-btn:hover {
    background-color: #059669;
}

.cancel-btn {
    background-color: #6b7280;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
}

.cancel-btn:hover {
    background-color: #4b5563;
}