/**
 * Date Picker Initialization
 * Initializes flatpickr date pickers for all date inputs on the transportation page
 */

document.addEventListener('DOMContentLoaded', function () {
    // Common configuration for all date pickers
    const commonConfig = {
        dateFormat: 'Y-m-d',
        minDate: 'today',
        disableMobile: true, // Prevents using the mobile native date picker
        static: true, // Prevents the calendar from repositioning as you scroll
        showMonths: 1,
        animate: true,
        closeOnSelect: true,

        // Disable past dates
        disable: [
            function (date) {
                // Disable dates before today
                return date < new Date().setHours(0, 0, 0, 0);
            }
        ],

        // Custom styling for past dates (additional to CSS)
        onDayCreate: function (dObj, dStr, fp, dayElem) {
            // Add a class to past dates for additional styling
            if (dayElem.dateObj < new Date().setHours(0, 0, 0, 0)) {
                dayElem.classList.add('past-date');
            }

            // Add classes for better styling of prev/next month days
            if (dayElem.classList.contains('prevMonthDay')) {
                dayElem.classList.add('adjacent-month');
            }
            if (dayElem.classList.contains('nextMonthDay')) {
                dayElem.classList.add('adjacent-month');
            }
        },

        // Position the calendar below the input
        position: 'below',

        // Ensure the calendar is properly sized
        onOpen: function (selectedDates, dateStr, instance) {
            // Force the calendar to recalculate its dimensions
            instance.calendarContainer.classList.add('open');

            // Ensure the calendar is positioned correctly
            const inputRect = instance.input.getBoundingClientRect();
            instance.calendarContainer.style.top = (inputRect.bottom + window.scrollY + 5) + 'px';
            instance.calendarContainer.style.left = (inputRect.left + window.scrollX) + 'px';

            // Ensure the calendar is not wider than the input
            instance.calendarContainer.style.width = Math.max(280, inputRect.width) + 'px';
        }
    };

    // Initialize main search form date pickers
    const departDatePicker = flatpickr('#departDate', {
        ...commonConfig,
        onChange: function (selectedDates, dateStr, instance) {
            // If return date picker exists and has a date earlier than the selected departure date,
            // update the return date to match the departure date
            if (returnDatePicker && returnDatePicker.selectedDates.length > 0) {
                if (selectedDates[0] > returnDatePicker.selectedDates[0]) {
                    returnDatePicker.setDate(selectedDates[0]);
                }
            }

            // Update the return date picker's minimum date
            if (returnDatePicker) {
                returnDatePicker.set('minDate', dateStr);
            }
        }
    });

    // Initialize return date picker with reference to departure date picker
    const returnDatePicker = flatpickr('#returnDate', {
        ...commonConfig,
        onChange: function (selectedDates, dateStr, instance) {
            // Ensure return date is not earlier than departure date
            if (departDatePicker && departDatePicker.selectedDates.length > 0) {
                if (selectedDates[0] < departDatePicker.selectedDates[0]) {
                    instance.setDate(departDatePicker.selectedDates[0]);
                }
            }
        }
    });

    // Configuration specific to external reservation modal date pickers
    const externalConfig = {
        ...commonConfig,
        // Make the calendar smaller for the external reservation modal
        onOpen: function (selectedDates, dateStr, instance) {
            // Force the calendar to recalculate its dimensions
            instance.calendarContainer.classList.add('open');
            instance.calendarContainer.classList.add('external-calendar');

            // Ensure the calendar is positioned correctly
            const inputRect = instance.input.getBoundingClientRect();
            instance.calendarContainer.style.top = (inputRect.bottom + window.scrollY + 5) + 'px';
            instance.calendarContainer.style.left = (inputRect.left + window.scrollX) + 'px';

            // Make the calendar smaller for the external modal
            instance.calendarContainer.style.width = '240px';

            // Add a class to identify this as an external modal calendar
            document.querySelectorAll('.flatpickr-day').forEach(day => {
                day.style.height = '30px';
                day.style.lineHeight = '30px';
                day.style.fontSize = '11px';
            });

            // Ensure the year input is visible
            setTimeout(() => {
                // Force the year input to be visible
                const yearInput = instance.calendarContainer.querySelector('.numInputWrapper');
                if (yearInput) {
                    yearInput.style.display = 'inline-block';
                    yearInput.style.visibility = 'visible';
                    yearInput.style.opacity = '1';
                }

                // Also make the year input itself visible
                const curYearInput = instance.calendarContainer.querySelector('.cur-year');
                if (curYearInput) {
                    curYearInput.style.display = 'inline-block';
                    curYearInput.style.visibility = 'visible';
                    curYearInput.style.opacity = '1';
                }

                // Make sure month dropdown is not too wide
                const monthDropdown = instance.calendarContainer.querySelector('.flatpickr-monthDropdown-months');
                if (monthDropdown) {
                    monthDropdown.style.width = '80px';
                }

                // Force the arrowUp and arrowDown to be visible
                const arrows = instance.calendarContainer.querySelectorAll('.arrowUp, .arrowDown');
                arrows.forEach(arrow => {
                    arrow.style.display = 'block';
                    arrow.style.visibility = 'visible';
                    arrow.style.opacity = '1';
                });
            }, 10);

            // Additional hack to ensure year is visible - set altInput to true and then back to false
            instance.set('altInput', true);
            setTimeout(() => {
                instance.set('altInput', false);
            }, 20);
        }
    };

    // Initialize external reservation form date pickers
    const externalDatePicker = flatpickr('#externalDate', {
        ...externalConfig,
        onChange: function (selectedDates, dateStr, instance) {
            // No need to update return date as reservations are one-way
            // Removed references to externalReturnDatePicker
        }
    });

    // External return date picker is no longer needed as reservations are one-way
    // Removed initialization for #externalReturnDate

    // Make sure the return date picker's minimum date is set to the departure date
    if (departDatePicker && departDatePicker.selectedDates.length > 0 && returnDatePicker) {
        returnDatePicker.set('minDate', departDatePicker.selectedDates[0]);
    }

    // External return date is no longer needed
});
