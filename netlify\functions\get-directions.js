// Netlify function for getting directions from OpenRouteService API
const axios = require('axios');

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  try {
    // Parse query parameters
    const params = event.queryStringParameters || {};
    const { start, end, profile } = params;

    // Validate required parameters
    if (!start || !end) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing required parameters: start and end coordinates' })
      };
    }

    // Parse coordinates - OpenRouteService expects [longitude, latitude]
    // The format from the client is already "longitude,latitude"
    const startCoords = start.split(',').map(Number);
    const endCoords = end.split(',').map(Number);

    // Validate coordinates
    if (startCoords.length !== 2 || endCoords.length !== 2 ||
      isNaN(startCoords[0]) || isNaN(startCoords[1]) ||
      isNaN(endCoords[0]) || isNaN(endCoords[1])) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Invalid coordinates format' })
      };
    }

    // Use the API key from environment variables or fallback to a default
    // Note: It's better to use environment variables in production
    const apiKey = process.env.OPENROUTE_API_KEY || "5b3ce3597851110001cf6248298dfe6c5ab14101a9f15d53ce5de996";

    // Build the request body
    const requestBody = {
      coordinates: [startCoords, endCoords],
      format: 'geojson'
    };

    // Log the request for debugging
    console.log('OpenRouteService request:', {
      url: `https://api.openrouteservice.org/v2/directions/${profile || 'driving-car'}`,
      coordinates: requestBody.coordinates
    });

    // Make the API request to OpenRouteService
    const url = `https://api.openrouteservice.org/v2/directions/${profile || 'driving-car'}/geojson`;
    console.log(`Making request to: ${url}`);
    console.log(`With coordinates: ${JSON.stringify(requestBody.coordinates)}`);

    const response = await axios({
      method: 'post',
      url: url,
      data: requestBody,
      headers: {
        'Authorization': `Bearer ${apiKey}`, // API key in header with Bearer prefix
        'Content-Type': 'application/json',
        'Accept': 'application/json, application/geo+json'
      }
    });

    // Log the successful response
    console.log('OpenRouteService response status:', response.status);
    console.log('OpenRouteService response has features:', !!response.data.features);
    if (response.data.features) {
      console.log('Number of coordinates in route:', response.data.features[0].geometry.coordinates.length);
    }

    // Return successful response
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify(response.data)
    };

  } catch (error) {
    // Log detailed error information
    console.error('Error getting directions:');
    if (error.response) {
      // The request was made and the server responded with a status code outside of 2xx
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data));
      console.error('Response headers:', error.response.headers);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Request error:', error.message);
    }

    // Return error response
    return {
      statusCode: error.response?.status || 500,
      headers,
      body: JSON.stringify({
        error: 'Failed to get directions',
        details: error.response?.data || error.message
      })
    };
  }
};
