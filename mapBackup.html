<!DOCTYPE HTML>
<html>
  <head>
    <title>Map - Vestigia</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
    <link rel="stylesheet" href="css/main.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap">
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="96x96" href="favicon/favicon-96x96.png">
    <link rel="icon" href="favicon/favicon.ico">
    <link rel="manifest" href="favicon/site.webmanifest">
    <meta name="msapplication-TileColor" content="#262626">
    <meta name="theme-color" content="#262626">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
     integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
     crossorigin=""/>
     <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
     integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
     crossorigin=""></script>
     <link rel="stylesheet" href="css/map.css">
	 <link rel="stylesheet" href="css/navbar.css">
     <link rel="stylesheet" href="css/footer.css">
     <script src="https://unpkg.com/leaflet-control-geocoder/dist/Control.Geocoder.js" charset="utf-8"></script>
     <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster/dist/MarkerCluster.css" />
     <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster/dist/MarkerCluster.Default.css" />
     <script src="https://unpkg.com/leaflet.markercluster/dist/leaflet.markercluster.js"></script>
     <!-- Supabase JS is still needed for client-side operations -->
     <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2.38.4/dist/umd/supabase.min.js"></script>
     <!-- Added for enhanced interactivity -->
     <link rel="stylesheet" href="https://unpkg.com/leaflet.fullscreen@2.4.0/Control.FullScreen.css" />
     <script src="https://unpkg.com/leaflet.fullscreen@2.4.0/Control.FullScreen.js"></script>
     <script src="https://cdn.jsdelivr.net/npm/leaflet-easybutton@2/src/easy-button.js"></script>
     <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet-easybutton@2/src/easy-button.css">
     <script src="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>
     <!-- Authentication client -->
     <script src="js/auth.js"></script>
     <!-- Navbar loader -->
     <script src="js/navbar-loader.js"></script>
     <!-- Secure API client -->
     <script src="js/secure-api-client.js"></script>
     <!-- Map API Client (includes Supabase wrapper) -->
     <script src="js/map-api-client.js"></script>
     <script data-noptimize="1" data-cfasync="false" data-wpfc-render="false">
      (function () {
          var script = document.createElement("script");
          script.async = 1;
          script.src = 'https://emrld.cc/NDEyOTUz.js?t=412953';
          document.head.appendChild(script);
      })();
    </script>
     <style>
       /* Ensure map container is stable during zoom operations */
       #map {
         position: relative;
         overflow: hidden;
         transform-origin: center center;
         will-change: transform;
         transition: transform 0.25s ease-out;
         touch-action: none; /* Disable browser handling of all panning and zooming gestures */
       }

       /* Add this CSS to change the cursor to a grab icon */
       #placesList li {
         cursor: grab;
         transition: transform 0.2s ease, box-shadow 0.2s ease;
         padding: 10px;
         margin-bottom: 8px;
         background-color: #f5f5f5;
         border-radius: 6px;
         border-left: 3px solid #861818;
       }
       #placesList li:active {
         cursor: grabbing;
       }
       #placesList li:hover {
         transform: translateY(-3px);
         box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
       }

       /* Mobile-friendly scrollbar styles */
       #placesList::-webkit-scrollbar {
         width: 8px;
         background-color: #f5f5f5;
         border-radius: 4px;
       }

       #placesList::-webkit-scrollbar-thumb {
         background-color: #861818;
         border-radius: 4px;
       }

       #placesList::-webkit-scrollbar-thumb:hover {
         background-color: #9a1c1c;
       }

       /* Ensure the list is more touch-friendly on mobile */
       @media (max-width: 768px) {
         #placesList {
           -webkit-overflow-scrolling: touch;
           overflow-y: scroll; /* Force scrollbar to be visible on mobile */
           padding-right: 15px; /* Add extra padding for the scrollbar */
           scrollbar-width: thin;
           scrollbar-color: #861818 #f5f5f5;
         }

         #placesList li {
           padding: 12px; /* Larger touch target */
           margin-bottom: 10px; /* More space between items */
         }

         #placesList .button-container {
           flex-wrap: wrap;
           gap: 6px; /* More space between buttons */
         }
       }

       /* Style for the item title and description */
       #placesList li > div:first-child {
         margin-bottom: 8px;
         font-size: 0.95em;
       }

       #placesList li > div:first-child b {
         color: #861818;
       }

       /* Styles for the button container */
       #placesList .button-container {
         display: flex;
         flex-wrap: wrap;
         gap: 4px;
         margin-top: 6px;
       }

       /* Styles for the action buttons */
       #placesList .button-container button {
         padding: 4px 8px;
         border-radius: 3px;
         cursor: pointer;
         transition: all 0.2s ease;
         font-size: 0.8em;
         flex: 1 0 auto;
         min-width: 90px;
         max-width: 140px;
         white-space: nowrap;
         overflow: hidden;
         text-overflow: ellipsis;
       }

       /* All buttons now use the action-btn style */
       #placesList .button-container .action-btn {
         background-color: #861818;
         color: #ffd700;
         border: 2px solid #861818;
         font-weight: bold;
       }

       #placesList .button-container .action-btn:hover {
         background-color: #9a1c1c;
         transform: translateY(-2px);
         box-shadow: 0 3px 8px rgba(134, 24, 24, 0.3);
       }

       #placesList .button-container button i {
         margin-right: 3px;
         font-size: 0.9em;
       }

       /* Styles for search suggestions */
       .suggestions-container {
         position: absolute;
         top: 100%;
         left: 0;
         background: #262626; /* Dark gray background */
         border: 2px solid #861818;
         border-top: none;
         border-radius: 0 0 5px 5px;
         max-height: 200px;
         overflow-y: auto;
         z-index: 1000;
         display: none; /* Hide by default, will be shown when there are suggestions */
         margin-top: -2px; /* Connect to the search bar */
         box-sizing: border-box;
         box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
       }
       .suggestion-item {
         padding: 8px 10px;
         cursor: pointer;
         border-bottom: 1px solid #444;
         color: #ffd700; /* Gold text color */
         transition: background-color 0.2s ease, transform 0.1s ease;
       }
       .suggestion-item:hover {
         background-color: #333;
         transform: translateX(5px);
       }
       .suggestion-item:last-child {
         border-bottom: none;
       }

       /* Position the search control next to zoom controls */
       .leaflet-control-search {
         margin-left: 50px !important; /* Move search bar to the right of custom controls */
         margin-top: 25px !important; /* Move down more */
         clear: none !important;
         float: left !important;
       }

       /* Adjust the position of the zoom control container */
       .leaflet-top.leaflet-left {
         display: flex;
         align-items: flex-start;
       }

       /* Style the search input */
       #mapLocationSearch {
         box-sizing: border-box; /* Ensure width includes borders */
         margin: 0;
         display: block;
         transition: all 0.3s ease;
         border: 2px solid #861818;
       }

       #mapLocationSearch:focus {
         box-shadow: 0 0 8px rgba(134, 24, 24, 0.6);
         border-color: #ffd700;
       }

       /* Style the search input placeholder */
       #mapLocationSearch::placeholder {
         color: #aaa;
         opacity: 1;
       }

       /* Popup styles with improved appearance */
       .leaflet-popup {
         z-index: 1000;
       }

       .custom-popup .leaflet-popup-content-wrapper {
         background: #262626;
         color: #ffd700;
         border-radius: 8px;
         padding: 10px;
         box-shadow: 0 8px 25px rgba(0,0,0,0.5);
         transform: translateY(0);
         transition: transform 0.3s ease, box-shadow 0.3s ease;
         border: 2px solid #861818;
       }

       .custom-popup:hover .leaflet-popup-content-wrapper {
         transform: translateY(-5px);
         box-shadow: 0 12px 30px rgba(0,0,0,0.6);
       }

       .custom-popup .leaflet-popup-tip {
         background: #262626;
         box-shadow: 0 3px 14px rgba(0,0,0,0.4);
         border: 1px solid #861818;
       }

       .custom-popup .leaflet-popup-content {
         margin: 8px 12px;
         line-height: 1.4;
       }

       .custom-popup .leaflet-popup-close-button {
         color: #ffd700;
         top: 8px;
         right: 8px;
         transition: transform 0.2s ease;
       }

       .custom-popup .leaflet-popup-close-button:hover {
         transform: scale(1.2);
         color: #ff6b6b;
       }

       .leaflet-routing-container {
         background: #262626;
         color: #ffd700;
       }
       .leaflet-routing-alt {
         background: #262626;
         color: #ffd700;
       }

       /* Custom cluster colors based on density */
       .marker-cluster-small {
         background-color: rgba(76, 175, 80, 0.3);  /* Light green with high transparency */
         transition: transform 0.3s ease;
       }
       .marker-cluster-small div {
         background-color: rgba(76, 175, 80, 0.5);  /* Slightly more opaque green */
       }
       .marker-cluster-medium {
         background-color: rgba(255, 140, 0, 0.3);  /* Orange with high transparency */
         transition: transform 0.3s ease;
       }
       .marker-cluster-medium div {
         background-color: rgba(255, 140, 0, 0.5);  /* More opaque orange */
       }
       .marker-cluster-large {
         background-color: rgba(134, 24, 24, 0.3);  /* Red with high transparency */
         transition: transform 0.3s ease;
       }
       .marker-cluster-large div {
         background-color: rgba(134, 24, 24, 0.5);  /* More opaque red */
       }

       .marker-cluster:hover {
         transform: scale(1.1);
       }

       /* cluster text */
       .marker-cluster span {
         color: #ffffff;
         text-shadow: 0 0 3px rgba(0, 0, 0, 0.8);
       }

       /* Control buttons styling */
       .leaflet-control-fullscreen a,
       .leaflet-control-zoom a,
       .leaflet-bar a {
         background-color: #262626 !important;
         color: #ffd700 !important;
         border: 1px solid #861818 !important;
         transition: all 0.2s ease;
       }

       /* Specific styling for fullscreen button */
       .leaflet-control-fullscreen-button {
         background-color: #262626 !important;
         color: #ffd700 !important;
       }

       .leaflet-control-fullscreen-button:before {
         color: #ffd700 !important;
       }

       /* Force the fullscreen icon to be gold */
       .leaflet-control-fullscreen a:after {
         color: #ffd700 !important;
         border-color: #ffd700 !important;
       }

       .leaflet-control-fullscreen a:hover,
       .leaflet-control-zoom a:hover,
       .leaflet-bar a:hover {
         background-color: #861818 !important;
         transform: scale(1.1);
       }

       /* Floating action button */
       .floating-action-btn {
         position: fixed;
         bottom: 30px;
         right: 30px;
         width: 60px;
         height: 60px;
         border-radius: 50%;
         background-color: #861818;
         color: #ffd700;
         display: flex;
         align-items: center;
         justify-content: center;
         font-size: 24px;
         box-shadow: 0 4px 12px rgba(0,0,0,0.3);
         cursor: pointer;
         z-index: 1000;
         transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
       }

       .floating-action-btn:hover {
         transform: scale(1.1) rotate(45deg);
         box-shadow: 0 6px 16px rgba(0,0,0,0.4);
       }

       /* Quick actions menu */
       .quick-actions {
         position: fixed;
         bottom: 100px;
         right: 30px;
         background-color: #262626;
         border-radius: 10px;
         padding: 10px;
         box-shadow: 0 4px 12px rgba(0,0,0,0.3);
         z-index: 100000000000000;
         display: none;
         flex-direction: column;
         gap: 10px;
         border: 2px solid #861818;
       }

       .quick-action-btn {
         background-color: #333;
         color: #ffd700;
         border: none;
         padding: 10px;
         border-radius: 5px;
         cursor: pointer;
         display: flex;
         align-items: center;
         gap: 8px;
         transition: all 0.2s ease;
       }

       .quick-action-btn:hover {
         background-color: #861818;
         transform: translateX(5px);
       }

       /* Weather info box */
       .weather-info {
         position: absolute;
         top: 10px;
         right: 10px;
         background-color: rgba(38, 38, 38, 0.9);
         color: #ffd700;
         padding: 15px;
         border-radius: 8px;
         z-index: 1000;
         box-shadow: 0 4px 15px rgba(0,0,0,0.4);
         backdrop-filter: blur(8px);
         border: 2px solid #861818;
         display: none;
         min-width: 280px;
         max-width: 350px;
         transition: all 0.3s ease;
         position: relative;
       }

       .weather-info:hover {
         box-shadow: 0 6px 20px rgba(0,0,0,0.5);
         transform: translateY(-2px);
       }

       .weather-close-btn {
         position: absolute;
         top: 5px;
         right: 5px;
         background-color: transparent;
         color: #ffd700;
         border: none;
         font-size: 16px;
         cursor: pointer;
         width: 24px;
         height: 24px;
         display: flex;
         align-items: center;
         justify-content: center;
         border-radius: 50%;
         transition: all 0.2s ease;
       }

       .weather-close-btn:hover {
         background-color: rgba(134, 24, 24, 0.5);
         transform: scale(1.1);
       }

       /* Pulse animation for markers */
       @keyframes pulse {
         0% {
           transform: scale(1);
           opacity: 1;
         }
         50% {
           transform: scale(1.3);
           opacity: 0.7;
         }
         100% {
           transform: scale(1);
           opacity: 1;
         }
       }

       .pulse-marker {
         animation: pulse 1.5s infinite;
       }
     </style>
  </head>
  <body style="font-family: 'Open Sans', sans-serif;" class="map-page" onload="if(typeof auth !== 'undefined') { auth.init(); setTimeout(updateMapAuthUI, 200); }">
	<div id="navbar-placeholder"></div>

  <!-- Main Content (Pitch Project Placeholder Entries) -->
  <section id="main">
    <div class="container">
      <br><BR>
      <h1 style="color: black;">Map</h1>

    <!-- Directions Box -->
    <div id="directions-box" style="margin-bottom: 20px;">
      <input type="text" id="startLocation" placeholder="Type start location..." style="padding: 5px; border: 2px solid #861818; border-radius: 5px 0 0 5px; outline: none; margin-bottom: 5px;">
      <button id="switchButton" style="padding: 5px 10px; background-color: #861818; color: white; border: 2px solid #861818; border-radius: 5px; cursor: pointer; margin: 0 5px;">
        <i class="fas fa-exchange-alt" style="color: #ffd700;"></i>
      </button>
      <input type="text" id="endLocation" placeholder="Type end location..." style="padding: 5px; border: 2px solid #861818; border-radius: 5px 0 0 5px; outline: none; margin-bottom: 5px;">
      <select id="transportMode" style="padding: 5px; border: 2px solid #861818; border-radius: 5px; outline: none; margin-bottom: 5px;">
        <option value="driving-car">Car</option>
        <option value="cycling-regular">Bike</option>
        <option value="foot-walking">Walking</option>
        <option value="flying">Plane</option>
      </select>
      <button id="directionsButton" style="padding: 5px 10px; background-color: #861818; color: white; border: 2px solid #861818; border-radius: 5px; cursor: pointer;">
        Get Directions
      </button>
    </div>

    <div id="map" style="width: 100%; height: 400px; position: relative; border-radius: 10px; box-shadow: 0 8px 30px rgba(0,0,0,0.3); overflow: hidden; border: 3px solid #861818;">
    </div>

    <!-- Weather info box -->
    <div class="weather-info" id="weatherInfo">
      <button class="weather-close-btn" onclick="closeWeather()" title="Close weather">
        <i class="fas fa-times"></i>
      </button>
      <div id="weatherContent"></div>
    </div>
    <!-- Floating action button - commented out -->
    <!--
    <div class="floating-action-btn" id="floatingActionBtn">
      <i class="fas fa-plus"></i>
    </div>
    -->
    <!-- Quick actions menu - commented out -->
    <!--
    <div class="quick-actions" id="quickActions">
      <button class="quick-action-btn" id="toggleWeatherBtn">
        <i class="fas fa-cloud-sun"></i> Weather
      </button>
      <button class="quick-action-btn" id="findNearbyBtn">
        <i class="fas fa-compass"></i> Find Nearby
      </button>
      <button class="quick-action-btn" id="shareLocationBtn">
        <i class="fas fa-share-alt"></i> Share Location
      </button>
    </div>
    -->
<script>
  // Initialize Supabase client using environment variables
  // We'll use the secure API client instead of direct Supabase calls
  // This prevents API keys from being exposed in the frontend

  // Use the API client for OpenRouteService API calls

  let tempMarkers = []; // Array to store temporary markers

  // All popup creation instances will use this offset
  const POPUP_OFFSET = [0, -15];

  function getRoute(start, end, transportMode) {
    // Check if the transport mode is flying/plane
    if (transportMode === 'flying') {
      // Redirect to transportation.html with origin and destination parameters
      // Use reverse geocoding to get location names
      const startPromise = fetchLocationName(start.lat, start.lng);
      const endPromise = fetchLocationName(end.lat, end.lng);

      Promise.all([startPromise, endPromise])
        .then(([startLocation, endLocation]) => {
          // Redirect to transportation.html with the location names
          window.location.href = `transportation.html?departure=${encodeURIComponent(startLocation)}&destination=${encodeURIComponent(endLocation)}`;
        })
        .catch(error => {
          console.error('Error getting location names:', error);
          // Fallback to coordinates if location names can't be retrieved
          window.location.href = `transportation.html?departure=${encodeURIComponent(`${start.lat},${start.lng}`)}&destination=${encodeURIComponent(`${end.lat},${end.lng}`)}`;
        });
      return;
    }

    // For non-flying transport modes, use the secure API client to get directions
    // This calls the backend API which protects our API keys
    const startCoords = `${start.lng},${start.lat}`;
    const endCoords = `${end.lng},${end.lat}`;

    window.secureAPI.map.getDirections(startCoords, endCoords, transportMode)
      .then(data => {
        // Process the response data
        return data;
      })
      .then(response => response.json())
      .then(data => {
        if (data.features) {
          const routeCoords = data.features[0].geometry.coordinates.map(coord => [coord[1], coord[0]]);

          // Clear existing routes and temporary markers
          map.eachLayer(layer => {
            if (layer instanceof L.Polyline && !layer._popup) {
              map.removeLayer(layer);
            }
          });
          tempMarkers.forEach(marker => map.removeLayer(marker));
          tempMarkers = [];

          // Add route to the map
          const routePolyline = L.polyline(routeCoords, { color: '#861818', weight: 10, interactive: true }).addTo(map);

          // Pan and zoom the map to fit the route
          map.fitBounds(routePolyline.getBounds());

          // Add markers to start and end points if they are not user-defined markers or POIs
          if (!start.isUserMarker && !start.isPOI) {
            const startMarker = L.marker([start.lat, start.lng], {icon: L.icon({
              iconUrl: 'images/marker.svg',
              iconSize: [20, 32],
              iconAnchor: [10, 32],
              popupAnchor: POPUP_OFFSET,
            })}).addTo(map).bindPopup("Start Location", { className: 'custom-popup', offset: POPUP_OFFSET });
            tempMarkers.push(startMarker);
          }
          if (!end.isUserMarker && !end.isPOI) {
            const endMarker = L.marker([end.lat, end.lng], {icon: L.icon({
              iconUrl: 'images/marker.svg',
              iconSize: [20, 32],
              iconAnchor: [10, 32],
              popupAnchor: POPUP_OFFSET,
            })}).addTo(map).bindPopup("End Location", { className: 'custom-popup', offset: POPUP_OFFSET });
            tempMarkers.push(endMarker);
          }

          // Add popup to the route line
          routePolyline.on('click', function(e) {
            const googleMapsMode = transportMode === 'driving-car' ? 'driving' :
                                   transportMode === 'cycling-regular' ? 'bicycling' :
                                   transportMode === 'foot-walking' ? 'walking' :
                                   transportMode === 'driving-hgv' ? 'transit' : 'driving';
            const googleMapsUrl = `https://www.google.com/maps/dir/?api=1&origin=${start.lat},${start.lng}&destination=${end.lat},${end.lng}&travelmode=${googleMapsMode}`;
            L.popup({ className: 'custom-popup', offset: POPUP_OFFSET })
              .setLatLng(e.latlng)
              .setContent(`
                <div style="text-align: center;">
                  <a href="${googleMapsUrl}" target="_blank" style="color: #ffd700; size=10px;">Start your route now</a><br><br>

                  <button onclick="removeRoute()" style="background-color: #262626; color: #ffd700; border: none; cursor: pointer; margin-top: 10px;size=10px">Remove Route</button>
                </div>
              `)
              .openOn(map);
          });

          // Ensure the routePolyline is interactive
          routePolyline.bringToFront();
        } else {
          alert("No route found.");
        }
      })
      .catch(error => console.error("Error fetching route:", error));
  }

  function getDirections() {
    const startLocationName = document.getElementById("startLocation").value;
    const endLocationName = document.getElementById("endLocation").value;
    const transportMode = document.getElementById("transportMode").value;

    let startMarker, endMarker;

    if (startLocationName === "Current Location" && userMarker) {
        startMarker = { lat: userMarker.getLatLng().lat, lng: userMarker.getLatLng().lng };
    } else {
        const startUserMarker = userMarkers.find(marker => marker.name === startLocationName);
        if (startUserMarker) {
            startMarker = { lat: startUserMarker.lat, lng: startUserMarker.lng, isUserMarker: true };
        } else {
            const startPOI = poiMarkers.find(poi => poi.name === startLocationName);
            if (startPOI) {
                startMarker = { lat: startPOI.lat, lng: startPOI.lng, isPOI: true };
            }
        }
    }

    if (endLocationName === "Current Location" && userMarker) {
        endMarker = { lat: userMarker.getLatLng().lat, lng: userMarker.getLatLng().lng };
    } else {
        const endUserMarker = userMarkers.find(marker => marker.name === endLocationName);
        if (endUserMarker) {
            endMarker = { lat: endUserMarker.lat, lng: endUserMarker.lng, isUserMarker: true };
        } else {
            const endPOI = poiMarkers.find(poi => poi.name === endLocationName);
            if (endPOI) {
                endMarker = { lat: endPOI.lat, lng: endPOI.lng, isPOI: true };
            }
        }
    }

    if (!startMarker || !endMarker) {
        // If either location wasn't found in markers, try geocoding
        if (!startMarker) {
            fetchLocationCoordinates(startLocationName, (coords) => {
                startMarker = coords;
                if (!endMarker) {
                    fetchLocationCoordinates(endLocationName, (coords) => {
                        endMarker = coords;
                        getRoute(startMarker, endMarker, transportMode);
                    });
                } else {
                    getRoute(startMarker, endMarker, transportMode);
                }
            });
        } else if (!endMarker) {
            fetchLocationCoordinates(endLocationName, (coords) => {
                endMarker = coords;
                getRoute(startMarker, endMarker, transportMode);
            });
        }
    } else {
        getRoute(startMarker, endMarker, transportMode);
    }
}

  function fetchLocationCoordinates(locationName, callback) {
    // Use the Netlify function instead of direct API call
    window.mapAPI.searchLocations(locationName)
      .then(data => {
        if (data.length > 0) {
          const { lat, lon } = data[0];
          callback({ lat: parseFloat(lat), lng: parseFloat(lon) });
        } else {
          alert(`Location "${locationName}" not found.`);
        }
      })
      .catch(error => console.error("Error fetching location coordinates:", error));
  }

  // Function to get location name from coordinates using reverse geocoding
  function fetchLocationName(lat, lng) {
    return new Promise((resolve, reject) => {
      // Use the secure API client to reverse geocode the coordinates
      window.mapAPI.getLocationName(lat, lng)
        .then(data => {
          if (data && data.display_name) {
            // Extract city or airport name from the display name
            const parts = data.display_name.split(',');
            // Use the first part (usually the most specific location) or a reasonable subset
            const locationName = parts[0].trim();
            resolve(locationName);
          } else {
            // If no name is found, use the coordinates
            resolve(`${lat.toFixed(4)},${lng.toFixed(4)}`);
          }
        })
        .catch(error => {
          console.error("Error fetching location name:", error);
          reject(error);
        });
    });
  }

  // Function to switch start and end locations
  function switchLocations() {
    const startLocation = document.getElementById("startLocation");
    const endLocation = document.getElementById("endLocation");
    const temp = startLocation.value;
    startLocation.value = endLocation.value;
    endLocation.value = temp;
  }

  // Add event listeners for directions buttons
  document.addEventListener('DOMContentLoaded', function() {
    // Get directions button
    const directionsButton = document.getElementById('directionsButton');
    if (directionsButton) {
      directionsButton.addEventListener('click', getDirections);
    }

    // Switch locations button
    const switchButton = document.getElementById('switchButton');
    if (switchButton) {
      switchButton.addEventListener('click', switchLocations);
    }

    // Enter key on end location field
    const endLocation = document.getElementById('endLocation');
    if (endLocation) {
      endLocation.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          getDirections();
        }
      });
    }

    // No datalist initialization needed anymore
  });

  // Variable to track saved markers state
  let savedUserMarkers = [];

  // Array to store user markers
  let userMarkers = [];

  // Variable to store the POI marker cluster group
  let markerCluster = null;

  // Array to store POI markers loaded from Supabase
  let poiMarkers = [];
  let poiMarkerObjects = []; // Store the actual marker objects for POIs
  let markers = [];

  // Function to clean up duplicates in local storage
  function cleanupLocalStorageDuplicates() {
    console.log("Cleaning up duplicates in local storage...");
    try {
      // Get current markers from local storage
      const storedMarkers = JSON.parse(localStorage.getItem('userMarkers')) || [];
      console.log(`Found ${storedMarkers.length} markers in local storage`);

      // Create a map to track unique markers
      const uniqueMarkers = [];
      const uniqueKeys = new Set();

      storedMarkers.forEach(marker => {
        // Create a unique key for each marker
        const key = `${parseFloat(marker.lat).toFixed(5)},${parseFloat(marker.lng).toFixed(5)},${marker.name}`;

        if (!uniqueKeys.has(key)) {
          uniqueKeys.add(key);
          uniqueMarkers.push(marker);
        } else {
          console.log(`Removed duplicate from local storage: ${marker.name} at ${marker.lat},${marker.lng}`);
        }
      });

      console.log(`After cleanup: ${uniqueMarkers.length} unique markers`);

      // Save the deduplicated markers back to local storage
      localStorage.setItem('userMarkers', JSON.stringify(uniqueMarkers));

      return uniqueMarkers.length;
    } catch (error) {
      console.error("Error cleaning up local storage:", error);
      return 0;
    }
  }

  // Run cleanup on page load
  const cleanedCount = cleanupLocalStorageDuplicates();
  console.log(`Cleaned up ${cleanedCount} markers in local storage`);
  // Function to clean up duplicates in Supabase
  async function cleanupDuplicatesInSupabase() {
    if (auth && auth.isLoggedIn() && auth.user) {
      try {
        console.log(`Cleaning up duplicate markers in Supabase for user ${auth.user.id}`);

        // Get all markers for this user
        const markers = await window.mapAPI.getUserMarkers();
        if (!markers || !Array.isArray(markers)) {
          console.error('Invalid response from getUserMarkers:', markers);
          return {
            error: 'Invalid response from getUserMarkers',
            success: false,
            duplicates_removed: 0
          };
        }
        
        console.log(`Fetched ${markers.length} markers for deduplication`);
        
        // Track unique markers using a Map with compound key of lat,lng,name
        const uniqueMarkers = new Map();
        const duplicates = [];
        
        // Sort markers by created_at to ensure we keep the oldest entries
        const sortedMarkers = [...markers].sort((a, b) => {
          const dateA = a.created_at ? new Date(a.created_at) : new Date(0);
          const dateB = b.created_at ? new Date(b.created_at) : new Date(0);
          return dateA - dateB;
        });
        
        markers.forEach(marker => {
          // Normalize coordinates to 5 decimal places to avoid floating point comparison issues
          const key = `${parseFloat(marker.lat).toFixed(5)},${parseFloat(marker.lng).toFixed(5)},${marker.name}`;
          
          if (!uniqueMarkers.has(key)) {
            uniqueMarkers.set(key, marker);
          } else {
            // If we find a duplicate, keep the one with the earlier created_at
            const existing = uniqueMarkers.get(key);
            if (new Date(marker.created_at) < new Date(existing.created_at)) {
              duplicates.push(existing.id);
              uniqueMarkers.set(key, marker);
            } else {
              duplicates.push(marker.id);
            }
          }
        });        // If we found duplicates, delete them
        if (duplicates.length > 0) {
          // Delete markers one by one to ensure consistency
          for (const id of duplicates) {
            try {
              await window.mapAPI.deleteUserMarker(auth.user.id, id);
            } catch (deleteError) {
              console.error(`Error deleting marker ${id}:`, deleteError);
              // Continue with other deletions even if one fails
            }
          }
          return {
            success: true,
            message: `Cleaned up ${duplicates.length} duplicate markers`,
            duplicates_removed: duplicates.length
          };
        }

        return {
          success: true,
          message: "No duplicates found",
          duplicates_removed: 0
        };
      } catch (error) {
        console.error('Error in cleanupDuplicatesInSupabase:', error);
        return {
          error: error.message,
          success: false,
          duplicates_removed: 0
        };
      }
    } else {
      console.log('User not logged in, skipping Supabase cleanup');
      return {
        error: 'User not logged in',
        success: false,
        duplicates_removed: 0
      };
    }
  }

  // Initialize map with a default location (e.g., London)
  const map = L.map('map', {
    worldCopyJump: false, // Disable world copy jump to prevent shifting
    fullscreenControl: false, // Disable default fullscreen control, we'll use our own
    zoomControl: false, // Disable default zoom control
    zoomSnap: 1, // Snap to integer zoom levels
    zoomDelta: 1, // Zoom changes by 1
    wheelPxPerZoomLevel: 120, // More precise mouse wheel zooming
    doubleClickZoom: false, // Disable double click zoom (can cause shifting)
    bounceAtZoomLimits: false, // Don't bounce at zoom limits
    scrollWheelZoom: false // Disable default scroll wheel zoom, we'll implement our own
  }).setView([51.505, -0.09], 13);

  // Create a custom zoom in button with gold color
  const zoomInBtn = document.createElement('button');
  zoomInBtn.innerHTML = '<i class="fas fa-plus" style="color: #ffd700;"></i>';
  zoomInBtn.style.backgroundColor = '#262626';
  zoomInBtn.style.border = '1px solid #861818';
  zoomInBtn.style.width = '30px';
  zoomInBtn.style.height = '30px';
  zoomInBtn.style.cursor = 'pointer';
  zoomInBtn.style.borderRadius = '4px';
  zoomInBtn.style.marginBottom = '5px';
  zoomInBtn.onclick = function(e) {
    // Get current center before zooming
    const currentCenter = map.getCenter();
    const currentZoom = map.getZoom();

    // Check if we're at max zoom level
    if (currentZoom < map.options.maxZoom) {
      // Zoom in while maintaining the current center point
      map.setView(currentCenter, currentZoom + 1, {
        animate: true,
        duration: 0.25
      });
    }

    // Prevent any default behavior or event propagation
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
  };

  // Create a custom zoom out button with gold color
  const zoomOutBtn = document.createElement('button');
  zoomOutBtn.innerHTML = '<i class="fas fa-minus" style="color: #ffd700;"></i>';
  zoomOutBtn.style.backgroundColor = '#262626';
  zoomOutBtn.style.border = '1px solid #861818';
  zoomOutBtn.style.width = '30px';
  zoomOutBtn.style.height = '30px';
  zoomOutBtn.style.cursor = 'pointer';
  zoomOutBtn.style.borderRadius = '4px';
  zoomOutBtn.style.marginBottom = '5px';
  zoomOutBtn.onclick = function(e) {
    // Get current center before zooming
    const currentCenter = map.getCenter();
    const currentZoom = map.getZoom();

    // Check if we're at min zoom level
    if (currentZoom > map.options.minZoom) {
      // Zoom out while maintaining the current center point
      map.setView(currentCenter, currentZoom - 1, {
        animate: true,
        duration: 0.25
      });
    }

    // Prevent any default behavior or event propagation
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
  };

  // Create a custom fullscreen button with gold color
  const fullscreenBtn = document.createElement('button');
  fullscreenBtn.innerHTML = '<i class="fas fa-expand" style="color: #ffd700;"></i>';
  fullscreenBtn.style.backgroundColor = '#262626';
  fullscreenBtn.style.border = '1px solid #861818';
  fullscreenBtn.style.width = '30px';
  fullscreenBtn.style.height = '30px';
  fullscreenBtn.style.cursor = 'pointer';
  fullscreenBtn.style.borderRadius = '4px';
  fullscreenBtn.onclick = function() {
    const mapElement = document.getElementById('map');

    if (!document.fullscreenElement) {
      // Enter fullscreen
      if (mapElement.requestFullscreen) {
        mapElement.requestFullscreen();
      } else if (mapElement.mozRequestFullScreen) { // Firefox
        mapElement.mozRequestFullScreen();
      } else if (mapElement.webkitRequestFullscreen) { // Chrome, Safari and Opera
        mapElement.webkitRequestFullscreen();
      } else if (mapElement.msRequestFullscreen) { // IE/Edge
        mapElement.msRequestFullscreen();
      }
      // Change icon to exit fullscreen
      fullscreenBtn.innerHTML = '<i class="fas fa-compress" style="color: #ffd700;"></i>';
    } else {
      // Exit fullscreen
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.mozCancelFullScreen) { // Firefox
        document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) { // Chrome, Safari and Opera
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) { // IE/Edge
        document.msExitFullscreen();
      }
      // Change icon back to enter fullscreen
      fullscreenBtn.innerHTML = '<i class="fas fa-expand" style="color: #ffd700;"></i>';
    }
  };

  // Create a container for the custom controls
  const customControlsContainer = document.createElement('div');
  customControlsContainer.style.position = 'absolute';
  customControlsContainer.style.top = '10px';
  customControlsContainer.style.left = '10px';
  customControlsContainer.style.zIndex = '2000'; // Higher z-index to ensure it's above other controls
  customControlsContainer.style.display = 'flex';
  customControlsContainer.style.flexDirection = 'column';

  // Add the buttons to the container
  customControlsContainer.appendChild(zoomInBtn);
  customControlsContainer.appendChild(zoomOutBtn);
  customControlsContainer.appendChild(fullscreenBtn);

  // Add the container to the map
  setTimeout(function() {
    document.getElementById('map').appendChild(customControlsContainer);

    // Hide only the default zoom control, keep fullscreen control
    const defaultZoomControls = document.querySelectorAll('.leaflet-control-zoom');
    defaultZoomControls.forEach(control => {
      control.style.display = 'none';
    });

    // Hide any Leaflet fullscreen controls
    const fullscreenControls = document.querySelectorAll('.leaflet-control-fullscreen, .leaflet-control-fullscreen-button');
    fullscreenControls.forEach(control => {
      control.style.display = 'none'; // Hide the default one since we have our custom button
    });

    // Also hide any fullscreen control containers
    const fullscreenContainers = document.querySelectorAll('.leaflet-control-container .leaflet-top.leaflet-left .leaflet-control-fullscreen');
    fullscreenContainers.forEach(container => {
      container.style.display = 'none';
    });

    // Add event listener to maintain center during zoom operations
    let lastValidCenter = map.getCenter();
    // Flag to disable center maintenance during geolocation operations
    let disableCenterMaintenance = false;
    let wheelZoomTimeout = null;

    // Make the flag accessible globally
    window.disableCenterMaintenance = function(disable) {
      disableCenterMaintenance = disable;
    };

    // Implement custom scroll wheel zoom
    document.getElementById('map').addEventListener('wheel', function(e) {
      if (e.cancelable) {
        e.preventDefault(); // Prevent default scroll behavior
      }

      // Get current center and zoom
      const currentCenter = map.getCenter();
      const currentZoom = map.getZoom();

      // Clear any pending zoom operations
      clearTimeout(wheelZoomTimeout);

      // Determine zoom direction (in or out)
      const zoomDirection = e.deltaY < 0 ? 1 : -1;
      const newZoom = Math.max(map.options.minZoom, Math.min(map.options.maxZoom, currentZoom + zoomDirection));

      // Only proceed if the zoom level would actually change
      if (newZoom !== currentZoom) {
        // Get mouse position relative to the map container
        const mapRect = map.getContainer().getBoundingClientRect();
        const mousePoint = L.point(
          e.clientX - mapRect.left,
          e.clientY - mapRect.top
        );

        // Convert mouse position to geographical point
        const targetPoint = map.containerPointToLatLng(mousePoint);

        // Delay the zoom operation slightly to prevent too many rapid zooms
        wheelZoomTimeout = setTimeout(() => {
          // Set the view to the new zoom level, centered on the mouse position
          map.setView(targetPoint, newZoom, {
            animate: true,
            duration: 0.25
          });
        }, 50);
      }
    }, { passive: false });

    map.on('zoomstart', function() {
      // Store the center point before zoom starts, but only if center maintenance is not disabled
      if (!disableCenterMaintenance) {
        lastValidCenter = map.getCenter();
      }
    });

    // No temporary pins when zooming

    // Add event listener for fullscreen change to update button icon
    document.addEventListener('fullscreenchange', updateFullscreenButtonIcon);
    document.addEventListener('webkitfullscreenchange', updateFullscreenButtonIcon);
    document.addEventListener('mozfullscreenchange', updateFullscreenButtonIcon);
    document.addEventListener('MSFullscreenChange', updateFullscreenButtonIcon);

    function updateFullscreenButtonIcon() {
      if (document.fullscreenElement ||
          document.webkitFullscreenElement ||
          document.mozFullScreenElement ||
          document.msFullscreenElement) {
        fullscreenBtn.innerHTML = '<i class="fas fa-compress" style="color: #ffd700;"></i>';
      } else {
        fullscreenBtn.innerHTML = '<i class="fas fa-expand" style="color: #ffd700;"></i>';
      }
    }
  }, 100);

  // Function to add POI markers to the map
  async function addPOIMarkers() {
    const poiIcon = L.icon({
      iconUrl: 'images/poiMarker.svg',
      iconSize: [20, 32],
      iconAnchor: [10, 32],
      popupAnchor: POPUP_OFFSET  // Use the constant
    });

    // Create a marker cluster group to hold all POI markers
    markerCluster = L.markerClusterGroup({
      maxClusterRadius: function(zoom) {
        // Define clustering radius based on zoom level ranges
        if (zoom <= 3) {        // World view - continental clusters
          return 25;           // Larger radius to group by continents
        } else if (zoom <= 5) { // Regional view - country clusters
          return 20;           // Group by countries/regions
        } else if (zoom <= 7) { // Country view - state/province clusters
          return 15;            // Group by states/provinces
        } else if (zoom <= 9) { // State view - city clusters
          return 10;            // Group by cities/areas
        } else if (zoom <= 11) { // City view - district clusters
          return 10;            // Group by districts
        } else {                // Local view - individual POIs
          return 5;             // Minimal clustering at high zoom levels
        }
      },
      // Custom thresholds for cluster sizes
      iconCreateFunction: function(cluster) {
        var childCount = cluster.getChildCount();

        var c = ' marker-cluster-';
        if (childCount < 5) {
          c += 'small';  // Green clusters for 2-4 markers
        } else if (childCount < 20) {
          c += 'medium'; // Orange clusters for 5-19 markers
        } else {
          c += 'large';  // Red clusters for 20+ markers
        }

        return new L.DivIcon({
          html: '<div><span>' + childCount + '</span></div>',
          className: 'marker-cluster' + c,
          iconSize: new L.Point(40, 40)
        });
      },
      spiderfyOnMaxZoom: true,
      showCoverageOnHover: false,
      zoomToBoundsOnClick: true,
      animate: true
    });

    try {
      // Fetch POI markers from Netlify Function instead of direct Supabase access
      console.log('Fetching POI markers from Netlify Function');
      const response = await window.mapAPI.fetchPOIMarkers();

      // Update the poiMarkers array with data from Netlify Function
      poiMarkers = response || [];
      console.log('Loaded POI markers from Netlify Function:', poiMarkers);
    } catch (error) {
      console.error('Error in addPOIMarkers:', error);
    }

    poiMarkers.forEach(poi => {
      const marker = L.marker([poi.lat, poi.lng], { icon: poiIcon });
      const photosHtml = poi.photos ? poi.photos.map(photo => `<img src="${photo}" alt="${poi.name}" style="width: 100px; height: auto; margin: 5px;">`).join('') : '';

      // Check if this POI is already in user's list
      const isInUserList = userMarkers.some(m =>
        Math.abs(m.lat - poi.lat) < 0.0001 &&
        Math.abs(m.lng - poi.lng) < 0.0001 &&
        m.name === poi.name
      );

      // For POI markers, we only show "Add to My List" button if not in user list
      // We don't show a delete button even if it's in the user list
      const buttonHtml = isInUserList ?
        `<div style="color: #ffd700;">Added to your list</div>` :
        `<button onclick="addToUserList(${poi.lat}, ${poi.lng}, '${poi.name.replace(/'/g, "\\'")}', '${poi.description.replace(/'/g, "\\'")}')" style="background-color: #262626; color: #ffd700; border: none; cursor: pointer;">Add to My List</button>`;

      marker.bindPopup(`
        <div style="text-align: center;">
          <b style="color: #ffd700;">${poi.name}</b><br />
          <span style="color: #ffd700;">${poi.description}</span><br />
          <div style="display: flex; justify-content: space-between; margin-top: 10px;">
            ${buttonHtml}
            <a href="https://en.wikipedia.org/wiki/${encodeURIComponent(poi.wikiName || poi.name)}" target="_blank" style="color: #ffd700;">Wiki</a>
          </div>
        </div>
      `, { className: 'custom-popup', offset: POPUP_OFFSET });

      // Store the marker object with its coordinates for later reference
      poiMarkerObjects.push({
        marker: marker,
        lat: poi.lat,
        lng: poi.lng,
        name: poi.name
      });

      // Add marker to cluster group instead of directly to map
      markerCluster.addLayer(marker);
    });

    // Add the cluster group to the map
    map.addLayer(markerCluster);

    return markerCluster;
  }

  // Try to get user's current location on page load
  if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(
      function(position) {
        const { latitude, longitude } = position.coords;
        // Center map on user's location with a reasonable zoom level
        map.setView([latitude, longitude], 13);
        // Add a marker at the user's location with custom SVG
        L.marker([latitude, longitude], {
          icon: L.icon({
            iconUrl: 'images/marker.svg',
            iconSize: [32, 32],
            iconAnchor: [16, 32],
            popupAnchor: [0, -32],
            className: 'user-location-marker'
          })
        }).addTo(map).bindPopup(
          '<div style="text-align: center;">' +
          '<b style="color: #ffd700;">Current Location</b><br/>' +
          '<span style="color: #ffd700; font-size: 0.9em;"></span>' +
          '</div>',
          { className: 'custom-popup' }
        ).openPopup();
      },
      function(error) {
        console.warn('Could not get user location:', error);
        // Fall back to default location if geolocation fails
        map.setView([51.505, -0.09], 13);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0
      }
    );
  } else {
    // Fall back to default location if geolocation is not supported
    map.setView([51.505, -0.09], 13);
  }

  // Set map boundaries
  const southWest = L.latLng(-90, -180); // Bottom-left corner of the world
  const northEast = L.latLng(90, 180);   // Top-right corner of the world
  const bounds = L.latLngBounds(southWest, northEast);

  // Load OpenStreetMap tiles with smooth transitions
  const mainLayer = L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
    maxZoom: 19,
    attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
  }).addTo(map);

  // Add satellite layer for toggle option
  const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
    maxZoom: 19,
    attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community'
  });

  // Add layer control
  const baseLayers = {
    "Standard": mainLayer,
    "Satellite": satelliteLayer
  };

  L.control.layers(baseLayers, null, {position: 'topright'}).addTo(map);

  // Set maxBounds, maxZoom, and minZoom options
  map.setMaxBounds(bounds.pad(0.1)); // Sets the maximum map extent with some padding
  map.options.minZoom = 2;  // Minimum zoom level
  map.options.maxZoom = 18; // Maximum zoom level

  // Add a moveend event to ensure the map stays within bounds
  map.on('moveend', function() {
    if (!map.getBounds().intersects(bounds)) {
      map.panInsideBounds(bounds, { animate: true });
    }
  });

  let userMarker;

  // Add this helper function before saveUserMarkers
  function serializeMarker(marker) {
  // Only serialize the necessary data, avoiding circular references
  return {
    lat: marker.lat,
    lng: marker.lng,
    name: marker.name,
    description: marker.description || "",
    placeName: marker.placeName || marker.description || "",
    isPOI: marker.isPOI || false,
    isCustom: marker.isCustom || false,  // Include the isCustom flag
    wikiName: marker.wikiName || null,
    addedToItinerary: marker.addedToItinerary || false, // Track if location is added to itinerary
    // Don't include the marker or popup objects
  };
}

// Function to show notifications to the user
function showNotification(message, type = 'info') {
  // Check if we have a notification container
  let notificationContainer = document.getElementById('notification-container');
  if (!notificationContainer) {
    // Create notification container if it doesn't exist
    notificationContainer = document.createElement('div');
    notificationContainer.id = 'notification-container';
    notificationContainer.style.position = 'fixed';
    notificationContainer.style.bottom = '20px';
    notificationContainer.style.right = '20px';
    notificationContainer.style.zIndex = '1000';
    document.body.appendChild(notificationContainer);
  }

  // Create notification element
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.style.padding = '15px 25px';
  notification.style.margin = '10px 0';
  notification.style.borderRadius = '4px';
  notification.style.color = 'white';
  notification.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
  notification.style.animation = 'slideIn 0.3s ease-out';
  notification.style.width = '300px';
  
  // Set background color based on type
  const colors = {
    success: '#4CAF50',
    error: '#f44336',
    warning: '#ff9800',
    info: '#2196F3'
  };
  
  notification.style.backgroundColor = colors[type] || colors.info;
  notification.textContent = message;

  // Add to container
  notificationContainer.appendChild(notification);

  // Auto-remove after 5 seconds
  setTimeout(() => {
    notification.style.animation = 'fadeOut 0.3s ease-out';
    setTimeout(() => {
      notification.remove();
    }, 300);
  }, 5000);
}

// Add some basic animations for the notifications
const style = document.createElement('style');
style.textContent = `
  @keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }
  @keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
  }
`;
document.head.appendChild(style);

async function saveUserMarkers() {
  try {
    // Check if user is properly authenticated
    if (!auth || !auth.isLoggedIn || !auth.isLoggedIn() || !auth.user || !auth.user.id) {
      console.error('User not authenticated. Cannot save markers to Supabase.');
      return;
    }

    // Get the current user
    const user = auth.getCurrentUser();
    if (!user) {
      console.error('No user found in session');
      return;
    }

    console.log('Saving user markers to Supabase for user:', user.id);
    
    // Create a clean copy of markers without circular references
    const markersToSave = userMarkers.map(marker => ({
      id: marker.id,
      lat: marker.lat,
      lng: marker.lng,
      name: marker.name,
      description: marker.description || '',
      place_name: marker.placeName || '',
      is_poi: !!marker.isPoi,
      is_custom: !!marker.isCustom,
      wiki_name: marker.wikiName || '',
      added_to_itinerary: !!marker.addedToItinerary
    }));

    // Save each marker to Supabase
    const savePromises = markersToSave.map(async (markerData) => {
      try {
        // Only save if it's a custom marker or a POI
        if (markerData.is_custom || markerData.is_poi) {
          await window.mapAPI.saveUserMarker(markerData);
        }
      } catch (error) {
        console.error('Error saving marker:', error);
        throw error;
      }
    });

    await Promise.all(savePromises);
    console.log('All markers saved to Supabase');
    
    // Clean up duplicates in Supabase
    try {
      await cleanupDuplicatesInSupabase();
    } catch (cleanupError) {
      console.error('Error cleaning up duplicates in Supabase:', cleanupError);
      // Continue even if cleanup fails
    }
    
    // Update the saved markers state with the clean data
    savedUserMarkers = JSON.parse(JSON.stringify(markersToSave));
    
  } catch (error) {
    console.error('Error saving markers:', error);
  }
}

async function loadUserMarkers() {
  try {
    // Clear existing markers
    userMarkers = [];
    
    // Load markers from local storage first
    const localStorageMarkers = JSON.parse(localStorage.getItem('userMarkers') || '[]');
    console.log('Loaded markers from local storage:', localStorageMarkers);

    // Load deleted markers from local storage
    const deletedMarkers = JSON.parse(localStorage.getItem('deletedMarkers') || '[]');
    console.log('Loaded deleted markers:', deletedMarkers);

    // Create a map to track markers by location and name for deduplication
    const markerMap = new Map();
    const uniqueKeys = new Set();
    const uniqueMarkers = [];

    // Add local storage markers to the map, excluding deleted ones
    if (Array.isArray(localStorageMarkers)) {
      localStorageMarkers.forEach(marker => {
        if (marker && typeof marker === 'object' && marker.lat != null && marker.lng != null && marker.name) {
          const key = `${parseFloat(marker.lat).toFixed(5)},${parseFloat(marker.lng).toFixed(5)},${String(marker.name).toLowerCase()}`;
          const isDeleted = deletedMarkers.some(deleted => 
            Math.abs(deleted.lat - marker.lat) < 0.0001 && 
            Math.abs(deleted.lng - marker.lng) < 0.0001 &&
            deleted.name.toLowerCase() === marker.name.toLowerCase()
          );
          
          if (!isDeleted && !markerMap.has(key)) {
            markerMap.set(key, { ...marker, source: 'localStorage' });
          }
        }
      });
    }

    // Check if user is properly authenticated before trying to sync with Supabase
    let isAuthenticated = false;
    let userId = null;
    let authToken = localStorage.getItem('sb-access-token');
    
    // Check both the auth object and localStorage for auth state
    if (auth && (typeof auth.isLoggedIn === 'function' && auth.isLoggedIn()) && auth.user && auth.user.id) {
      isAuthenticated = true;
      userId = auth.user.id;
      console.log('User is authenticated with ID:', userId);
      
      // Make sure we have the latest token
      if (auth.session && auth.session.access_token) {
        authToken = auth.session.access_token;
        localStorage.setItem('sb-access-token', authToken);
      }
    } else {
      // Fallback to localStorage check
      const sessionStr = localStorage.getItem('vestigia_session');
      const userStr = localStorage.getItem('vestigia_user');
      isAuthenticated = !!(sessionStr && userStr && authToken);
      console.log('Auth state from localStorage - isAuthenticated:', isAuthenticated, 'token exists:', !!authToken);
      
      if (isAuthenticated) {
        try {
          const userData = JSON.parse(userStr);
          userId = userData.id;
          console.log('Using user ID from localStorage:', userId);
        } catch (e) {
          console.error('Error parsing user data from localStorage:', e);
        }
      }
    }
    
    if (isAuthenticated && userId && authToken) {
      console.log('User is authenticated, syncing with Supabase...');
      
      try {
        // Get markers from Supabase
        const supabaseResponse = await window.mapAPI.getUserMarkers();
        
        if (Array.isArray(supabaseResponse)) {
          console.log('Successfully loaded markers from Supabase:', supabaseResponse);
          
          // Add Supabase markers to the map, overriding local storage markers
          supabaseResponse.forEach(marker => {
            if (marker && typeof marker === 'object' && marker.lat != null && marker.lng != null && marker.name) {
              const key = `${parseFloat(marker.lat).toFixed(5)},${parseFloat(marker.lng).toFixed(5)},${String(marker.name).toLowerCase()}`;
              markerMap.set(key, { ...marker, source: 'supabase' });
            }
          });
          
          // Update local storage with the merged set of markers
          const mergedMarkers = Array.from(markerMap.values())
            .map(({ source, ...rest }) => rest)
            .filter(marker => marker && typeof marker === 'object');
            
          try {
            localStorage.setItem('userMarkers', JSON.stringify(mergedMarkers));
          } catch (storageError) {
            console.error('Error saving to localStorage:', storageError);
          }
        } else {
          console.warn('Unexpected response format from getUserMarkers:', supabaseResponse);
        }
      } catch (error) {
        console.error('Error syncing with Supabase:', error);
        // Continue with local storage markers if Supabase sync fails
      }
    }

    // Convert the map to an array of markers and filter out any invalid entries
    const allMarkers = Array.from(markerMap.values())
      .filter(marker => marker && typeof marker === 'object' && 
              marker.lat != null && 
              marker.lng != null && 
              marker.name);
              
    console.log('Combined markers:', allMarkers);

    // Additional deduplication check by name and coordinates
    allMarkers.forEach(marker => {
      const key = `${parseFloat(marker.lat).toFixed(5)},${parseFloat(marker.lng).toFixed(5)},${String(marker.name).toLowerCase()}`;
      if (!uniqueKeys.has(key)) {
        uniqueKeys.add(key);
        uniqueMarkers.push(marker);
      } else {
        console.log(`Duplicate marker found and removed: ${marker.name} at ${marker.lat},${marker.lng}`);
      }
    });

    console.log('After additional deduplication:', uniqueMarkers.length, 'markers');

    // Separate custom markers and saved POI markers
    // Handle both camelCase and snake_case field names from different sources
    const customMarkers = uniqueMarkers.filter(marker => marker.isCustom || marker.is_custom);
    const savedPoiMarkers = uniqueMarkers.filter(marker => marker.isPOI || marker.is_poi);
    const otherMarkers = uniqueMarkers.filter(marker =>
      (!marker.isCustom && !marker.isPOI) &&
      (!marker.is_custom && !marker.is_poi)
    );

    console.log('Custom markers:', customMarkers);
    console.log('Saved POI markers:', savedPoiMarkers);
    console.log('Other markers:', otherMarkers);

    // Make sure all custom markers have the isCustom flag set to true
    customMarkers.forEach(marker => {
      marker.isCustom = true;
    });

    // Make sure all saved POI markers have the isPOI flag set to true
    savedPoiMarkers.forEach(marker => {
      marker.isPOI = true;
    });

    // Clear existing markers
    userMarkers.forEach(marker => {
      if (marker.marker) {
        map.removeLayer(marker.marker);
      }
    });
    userMarkers = [];

    // Create a map to track existing markers by location
    const existingMarkerMap = new Map();

    // First check the map for existing markers
    map.eachLayer(layer => {
      if (layer instanceof L.Marker) {
        const latLng = layer.getLatLng();
        const key = `${latLng.lat.toFixed(5)},${latLng.lng.toFixed(5)}`;
        existingMarkerMap.set(key, layer);
      }
    });

    // Add custom markers to the map
    customMarkers.forEach(markerData => {
      // Use destructuring with defaults for potentially missing fields
      const { lat, lng, name } = markerData;
      // Use optional properties with defaults, handling both camelCase and snake_case
      const placeName = markerData.placeName || markerData.place_name || '';
      
      // Add the marker to the map and userMarkers array
      addMarkerToMap(lat, lng, name, placeName, {
        isCustom: true,
        description: markerData.description || '',
        wikiName: markerData.wikiName || markerData.wiki_name || ''
      });
    });

    // Add saved POI markers to the map
    savedPoiMarkers.forEach(markerData => {
      const { lat, lng, name } = markerData;
      const placeName = markerData.placeName || markerData.place_name || '';
      
      // Add the POI marker to the map and userMarkers array
      addMarkerToMap(lat, lng, name, placeName, {
        isPoi: true,
        description: markerData.description || '',
        wikiName: markerData.wikiName || markerData.wiki_name || ''
      });
    });

    // Add other markers to the map
    otherMarkers.forEach(markerData => {
      // Use destructuring with defaults for potentially missing fields
      const { lat, lng, name } = markerData;
      // Use optional properties with defaults, handling both camelCase and snake_case
      const placeName = markerData.placeName || markerData.place_name || '';
      const description = markerData.description || '';
      const addedToItinerary = markerData.addedToItinerary === true || markerData.added_to_itinerary === true;

      // Check if a marker already exists at this location
      const key = `${parseFloat(lat).toFixed(5)},${parseFloat(lng).toFixed(5)}`;
      let marker = existingMarkerMap.get(key);

      // If no existing marker, create a new one
      if (!marker) {
        const icon = L.icon({
          iconUrl: 'images/marker.svg',
          iconSize: [20, 32],
          iconAnchor: [10, 32],
          popupAnchor: POPUP_OFFSET
        });

        marker = L.marker([lat, lng], { icon }).addTo(map);
      }

      const popupContent = `
        <div style="text-align: center;">
          <b style="color: #ffd700;">${name}</b><br />
          <span style="color: #ffd700;">${placeName || description}</span><br />
          <button onclick="removeUserMarker(${lat}, ${lng})" style="background-color: #262626; color: #ffd700; border: none; cursor: pointer; margin-top: 10px;">Delete</button>
        </div>`;

      marker.bindPopup(popupContent, {
        className: 'custom-popup',
        offset: POPUP_OFFSET
      });

      userMarkers.push({
        lat,
        lng,
        name,
        placeName: placeName || description,
        description,
        marker,
        isPOI: false,
        isCustom: false,
        addedToItinerary: addedToItinerary || false
      });
    });

    // Update the places list
    updatePlacesList();
    
    console.log('Finished loading markers');

    // If user is logged in and we have local markers that aren't in Supabase, sync them
    if (auth && auth.isLoggedIn && auth.user && localStorageMarkers.length > 0) {
      // Get markers from Supabase for comparison
      let supabaseMarkers = [];
      try {
        const response = await window.mapAPI.getUserMarkers();
        if (Array.isArray(response)) {
          supabaseMarkers = response;
        }
      } catch (error) {
        console.error('Error fetching markers from Supabase for sync:', error);
      }

      // Check if we need to sync local storage markers to Supabase
      const localOnlyMarkers = localStorageMarkers.filter(localMarker => {
        // Check if this local marker exists in the Supabase markers
        return !supabaseMarkers.some(supabaseMarker =>
          supabaseMarker && 
          localMarker &&
          Math.abs(supabaseMarker.lat - localMarker.lat) < 0.0001 &&
          Math.abs(supabaseMarker.lng - localMarker.lng) < 0.0001 &&
          supabaseMarker.name === localMarker.name
        );
      });

      if (localOnlyMarkers.length > 0) {
        console.log(`Found ${localOnlyMarkers.length} local markers not in Supabase, syncing...`);
        try {
          // Add user_id to each marker
          const markersWithUserId = localOnlyMarkers.map(marker => ({
            ...marker,
            user_id: auth.user.id
          }));

          // Save each local-only marker to Supabase individually
          const savePromises = markersWithUserId.map(marker => 
            window.mapAPI.saveUserMarker(marker).catch(err => {
              console.error('Error saving marker to Supabase:', err);
              return null; // Continue with other markers if one fails
            })
          );
          
          const saveResults = await Promise.all(savePromises);
          const successfulSaves = saveResults.filter(result => result !== null);
          console.log(`Successfully synced ${successfulSaves.length} of ${markersWithUserId.length} local markers to Supabase`);
        } catch (syncError) {
          console.error('Error syncing local markers with Supabase:', syncError);
        }
      }
    }

    updatePlacesList();
  } catch (error) {
    console.error("Error loading markers:", error);
  }
}

// Add marker to map and to the markers array
  function addMarkerToMap(lat, lng, name, placeName) {
    const userIcon = L.icon({
      iconUrl: 'images/marker.svg', // Use the red marker icon for user-added markers
      iconSize: [20, 32], // Smaller size
      iconAnchor: [10, 32],
      popupAnchor: POPUP_OFFSET  // Use the constant
    });

    // Create marker and add directly to map
    const marker = L.marker([lat, lng], { icon: userIcon }).addTo(map);

    // Create popup content as a simple HTML string
    // Note: This function is used for custom markers, not POI markers
    const popupContent = '<div style="text-align: center;">' +
      '<b style="color: #ffd700;">' + name + '</b><br />' +
      '<b style="color: #ffd700;">' + placeName + '</b><br />' +
      '<button onclick="removeUserMarker(' + lat + ', ' + lng + ')" style="background-color: #262626; color: #ffd700; border: none; cursor: pointer;">Delete</button>' +
      '</div>';

    // Bind popup directly to marker
    marker.bindPopup(popupContent, {
      className: 'custom-popup',
      offset: POPUP_OFFSET  // Offset to position popup above the marker
    }).openPopup();

    userMarkers.push({ lat, lng, name, placeName, marker });
    updatePlacesList();
  }

  // Function to deduplicate the userMarkers array
  function deduplicateUserMarkers() {
    console.log('Deduplicating userMarkers array...');
    const originalLength = userMarkers.length;

    // Create a map to track unique markers
    const uniqueMarkers = [];
    const uniqueKeys = new Set();

    userMarkers.forEach(marker => {
      // Create a unique key for each marker
      const key = `${parseFloat(marker.lat).toFixed(5)},${parseFloat(marker.lng).toFixed(5)},${marker.name}`;

      if (!uniqueKeys.has(key)) {
        uniqueKeys.add(key);
        uniqueMarkers.push(marker);
      } else {
        console.log(`Duplicate marker found in userMarkers: ${marker.name} at ${marker.lat},${marker.lng}`);
      }
    });

    // Replace the userMarkers array with the deduplicated version
    userMarkers = uniqueMarkers;

    console.log(`Deduplicated userMarkers: ${originalLength} -> ${userMarkers.length}`);
    return userMarkers.length;
  }

  // Update the list of places below the map
  function updatePlacesList() {
    console.log('Updating places list with markers:', userMarkers);
    const placesList = document.getElementById('placesList');
    if (!placesList) {
      console.error('placesList element not found!');
      return;
    }

    // First deduplicate the userMarkers array
    deduplicateUserMarkers();

    // Clear the list
    placesList.innerHTML = '';

    console.log('Displaying markers in places list:', userMarkers.length);

    userMarkers.forEach((marker, index) => {
      console.log(`Processing marker ${index}:`, marker);
      const listItem = document.createElement('li');

    // Determine if the marker has been added to the itinerary
    const itineraryButtonHtml = marker.addedToItinerary ?
      `<button onclick="window.location.href='itinerary.html'" class="action-btn">
        <i class="fa fa-calendar"></i> View in Itinerary
      </button>` :
      `<button onclick="openAddToItinerary('${encodeURIComponent(marker.name)}')" class="action-btn">
        <i class="fa fa-calendar"></i> Add to Itinerary
      </button>`;

    // Always show the delete button, even for POI markers
    const deleteButtonHtml = `<button onclick="removeUserMarker(${marker.lat}, ${marker.lng})" class="action-btn">
      <i class="fa fa-trash"></i> Delete
    </button>`;

    listItem.innerHTML = `
      <div>
        <b>${marker.name}</b> - ${marker.placeName || marker.description}
      </div>
      <div class="button-container">
        <button onclick="editMarker(${index})" class="action-btn">
          <i class="fa fa-edit"></i> Edit
        </button>
        <button onclick="goToLocation(${marker.lat}, ${marker.lng}, ${index})" class="action-btn">
          <i class="fa fa-map-marker"></i> Go to Location
        </button>
        ${deleteButtonHtml}
        ${itineraryButtonHtml}
        <button onclick="window.location.href='transportation.html?from=${encodeURIComponent(marker.name)}'" class="action-btn">
          <i class="fa fa-bus"></i> Find Transport
        </button>
      </div>
    `;
    placesList.appendChild(listItem);
    console.log(`Added marker ${index} to places list`);
  });

  new Sortable(placesList, {
    animation: 150,
    onEnd: function (evt) {
      const newOrder = Array.from(placesList.children).map(item => {
        const name = item.querySelector('b').innerText;
        return userMarkers.find(marker => marker.name === name);
      });
      userMarkers = newOrder;
      saveUserMarkers();
    }
  });

  // Check if we need to show the scroll indicator (for mobile)
  const scrollIndicator = document.getElementById('scrollIndicator');
  if (scrollIndicator && placesList) {
    // Only show on mobile devices
    if ((window.innerWidth <= 768) || ('ontouchstart' in window) || (navigator.maxTouchPoints > 0)) {
      // Check if content is taller than container
      if (placesList.scrollHeight > placesList.clientHeight) {
        // Only show if not already scrolled to bottom
        if (placesList.scrollHeight - placesList.scrollTop > placesList.clientHeight + 20) {
          scrollIndicator.style.display = 'block';
        } else {
          scrollIndicator.style.display = 'none';
        }
      } else {
        scrollIndicator.style.display = 'none';
      }
    } else {
      scrollIndicator.style.display = 'none';
    }
  }
}

  // updateDropdowns function removed as it's no longer needed

  // Function to add marker to itinerary
  function addToItinerary(index) {
    const marker = userMarkers[index];
  }

  // Function to open Wikipedia page for the location
  function openWiki(placeName) {
    const url = `https://en.wikipedia.org/wiki/${encodeURIComponent(placeName)}`;
    window.open(url, '_blank');
  }

  // Function to open Google Maps directions to the location
  function openDirections(lat, lng) {
    const url = `transportation.php?start=${userMarker.getLatLng().lat},${userMarker.getLatLng().lng}&end=${lat},${lng}`;
    window.open(url, '_blank');
  }

  // Function to go to a specific location on the map and show popup
  function goToLocation(lat, lng, index) {
    // Find the marker by coordinates since index might have changed
    const marker = userMarkers.find(m =>
      Math.abs(m.lat - lat) < 0.0001 && Math.abs(m.lng - lng) < 0.0001
    );

    if (marker) {
      map.setView([marker.lat, marker.lng], 16);
      if (marker.marker) {
        // If it's in a cluster, make sure it's visible
        if (marker.isPOI) {
          // Find the marker in the cluster group
          const poiLayer = markerCluster.getLayers().find(layer => {
            const latLng = layer.getLatLng();
            return Math.abs(latLng.lat - marker.lat) < 0.0001 &&
                   Math.abs(latLng.lng - marker.lng) < 0.0001;
          });

          if (poiLayer) {
            // Check if the marker is currently visible or hidden in a cluster
            if (markerCluster.getVisibleParent(poiLayer) !== poiLayer) {
              // If it's in a cluster, zoom to show the individual marker
              markerCluster.zoomToShowLayer(poiLayer, () => {
                poiLayer.openPopup();
              });
            } else {
              // If it's already visible, just open the popup
              poiLayer.openPopup();
            }
          }
        } else {
          // For non-POI markers that are added directly to the map
          marker.marker.openPopup();
        }
      }
    }
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  // Edit marker name
  function editMarker(index) {
    const marker = userMarkers[index]; // Changed from markers to userMarkers
    const newName = prompt("Enter a new name for this location:", marker.name);
    if (!newName) return;

    marker.name = newName;
    // Always show the delete button, even for POI markers
    const deleteButtonHtml = `<button onclick="removeUserMarker(${marker.lat}, ${marker.lng})" style="background-color: #262626; color: #ffd700; border: none; cursor: pointer;">Delete</button>`;

    marker.marker.getPopup().setContent(`
      <div style="text-align: center;">
        <b style="color: #ffd700;">${newName}</b><br />
        <b style="color: #ffd700;">${marker.placeName || marker.description}</b><br />
        ${deleteButtonHtml}
      </div>
    `);
    saveUserMarkers(); // Changed from saveMarkers
    updatePlacesList();
  }

  // Update the setUserLocation function
  function setUserLocation(position) {
    const lat = position.coords.latitude;
    const lon = position.coords.longitude;

    if (userMarker) {
      map.removeLayer(userMarker);
    }

    const icon = L.icon({
      iconUrl: 'images/marker.svg',
      iconSize: [20, 32],
      iconAnchor: [10, 32],
      popupAnchor: POPUP_OFFSET
    });

    userMarker = L.marker([lat, lon], { icon: icon })
      .addTo(map)
      .bindPopup('<b style="color: #ffd700;">Current Location</b>', { className: 'custom-popup', offset: POPUP_OFFSET })
      .openPopup();

    // Disable center maintenance during geolocation operations
    if (window.disableCenterMaintenance) {
      window.disableCenterMaintenance(true);
    }

    // Animate pan and zoom to the location
    map.flyTo([lat, lon], 15, {
      duration: 1.5,
      easeLinearity: 0.25
    });

    // Re-enable center maintenance after the flyTo animation completes
    setTimeout(function() {
      if (window.disableCenterMaintenance) {
        window.disableCenterMaintenance(false);
      }
    }, 1600); // Slightly longer than the flyTo duration
  }

  // Update the location button control
  const locationButton = L.control({ position: 'topright' });
  locationButton.onAdd = function () {
    const btn = L.DomUtil.create('button', 'location-button');
    btn.innerHTML = '<i class="fa fa-location-arrow"></i>';
    btn.title = "Go to my location";
    btn.style.padding = "10px";
    btn.style.background = "#861818";
    btn.style.color = "#ffd700";
    btn.style.cursor = "pointer";
    btn.style.borderRadius = "5px";
    btn.style.border = "none"; // Add this to remove border

    // Add click handler for location
    btn.onclick = function(e) {
      L.DomEvent.stopPropagation(e);
      
      if (!navigator.geolocation) {
        showToast('Geolocation is not supported by your browser', 'error');
        return;
      }

      // Show loading state
      btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
      btn.title = 'Locating...';
      btn.style.opacity = '0.8';

      // Disable center maintenance before getting location
      if (window.disableCenterMaintenance) {
        window.disableCenterMaintenance(true);
      }

      navigator.geolocation.getCurrentPosition(
        function(position) {
          setUserLocation(position);
          // Restore button state
          btn.innerHTML = '<i class="fa fa-location-arrow"></i>';
          btn.title = 'Find my location';
          btn.style.opacity = '1';
        },
        function(error) {
          // Handle errors gracefully
          let errorMessage = 'Unable to retrieve your location';
          switch(error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'Location permission was denied. Please enable it in your browser settings.';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Location information is unavailable.';
              break;
            case error.TIMEOUT:
              errorMessage = 'The request to get your location timed out.';
              break;
          }
          
          console.error('Geolocation error:', errorMessage);
          
          // Restore button state
          btn.innerHTML = '<i class="fa fa-location-arrow"></i>';
          btn.title = 'Find my location';
          btn.style.opacity = '1';
          
          // Show error message
          showToast(errorMessage, 'error');
          
          // Re-enable center maintenance in case of error
          if (window.disableCenterMaintenance) {
            window.disableCenterMaintenance(false);
          }
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 0
        }
      );
      
      return false;
    };

    return btn;
  };
  locationButton.addTo(map);

  // Variable to store search marker
  let searchMarker = null;

  // Create a custom search control positioned to the right of zoom controls but on the left side
  const searchControl = L.control({ position: 'topleft' });

  // Debug control removed
  searchControl.onAdd = function () {
    const container = L.DomUtil.create('div', 'leaflet-control-search');
    container.innerHTML = `
      <div style="width: 200px; position: relative;">
        <input type="text" id="mapLocationSearch" placeholder="Search locations..."
               style="padding: 8px; width: 100%; border: 2px solid #861818; border-radius: 4px; color: black; box-sizing: border-box;" />
        <div id="mapSuggestions"
             style="width: 100%; background: #262626; border: 2px solid #861818; border-top: none; border-radius: 0 0 5px 5px;
                    max-height: 200px; overflow-y: auto; z-index: 1000; display: none; margin-top: -2px; box-sizing: border-box;"></div>
      </div>
    `;

    // Prevent map drag when using the search box
    const input = container.querySelector('#mapLocationSearch');
    L.DomEvent.disableClickPropagation(container);
    L.DomEvent.disableScrollPropagation(container);

    // Add input event listener with debounce
    input.addEventListener('input', function(e) {
      const query = e.target.value.trim();
      if (query) {
        // Use debounce to prevent too many API calls
        clearTimeout(typingTimer);
        typingTimer = setTimeout(() => {
          fetchSuggestions(query);
        }, debounceDelay);
      } else {
        const suggestionsContainer = document.getElementById("mapSuggestions");
        suggestionsContainer.innerHTML = '';
        suggestionsContainer.style.display = 'none';
      }
    });

    // Add keypress event listener for Enter key
    input.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        const query = e.target.value.trim();
        if (query) {
          // Get the first suggestion if available
          const suggestionsContainer = document.getElementById("mapSuggestions");
          const firstSuggestion = suggestionsContainer.querySelector('.suggestion-item');

          if (firstSuggestion) {
            // Simulate click on the first suggestion
            firstSuggestion.click();
          } else {
            // Before searching with Nominatim, check if there's a matching POI
            // Search for POIs that contain the query string anywhere in the name
            const matchingPoi = poiMarkers.find(poi =>
              poi.name.toLowerCase().includes(query.toLowerCase())
            );

            if (matchingPoi) {
              // If there's a matching POI, create a temporary marker with the same popup content
              // Zoom to the location
              map.setView([matchingPoi.lat, matchingPoi.lng], 16);

              // Create a temporary marker with the same popup content as the original
              const escapedName = matchingPoi.name.replace(/'/g, "\\'");
              const escapedDesc = matchingPoi.description.replace(/'/g, "\\'");
              const photosHtml = matchingPoi.photos ? matchingPoi.photos.map(photo =>
                `<img src="${photo}" alt="${matchingPoi.name}" style="width: 100px; height: auto; margin: 5px;">`).join('') : '';

              // Create a temporary marker that will be removed when its popup is closed
              const tempIcon = L.icon({
                iconUrl: 'images/poiMarker.svg',
                iconSize: [20, 32],
                iconAnchor: [10, 32],
                popupAnchor: POPUP_OFFSET  // Use the constant
              });

              const tempMarker = L.marker([matchingPoi.lat, matchingPoi.lng], { icon: tempIcon }).addTo(map);

              // Check if this POI is already in the user's list
              const isInUserList = userMarkers.some(marker =>
                Math.abs(marker.lat - matchingPoi.lat) < 0.0001 &&
                Math.abs(marker.lng - matchingPoi.lng) < 0.0001 &&
                marker.name === matchingPoi.name
              );

              // Create appropriate button based on whether it's already in the list
              // For POI markers, we don't show a delete button even if it's in the user list
              const buttonHtml = isInUserList ?
                `<div style="color: #ffd700;">Added to your list</div>` :
                `<button onclick="window.addToUserListDirect(${parseFloat(matchingPoi.lat)}, ${parseFloat(matchingPoi.lng)}, '${escapedName}', '${escapedDesc}')"
                         style="background-color: #262626; color: #ffd700; border: none; cursor: pointer;">
                   Add to My List
                 </button>`;

              tempMarker.bindPopup(`
                <div style="text-align: center;">
                  <b style="color: #ffd700;">${matchingPoi.name}</b><br />
                  <span style="color: #ffd700; font-size: 0.9em;">${matchingPoi.description}</span>
                  <div style="display: flex; justify-content: space-between; margin-top: 10px;">
                    ${buttonHtml}
                    <a href="https://en.wikipedia.org/wiki/${encodeURIComponent(matchingPoi.wikiName || matchingPoi.name)}" target="_blank" style="color: #ffd700;">Wiki</a>
                  </div>
                  ${photosHtml}
                </div>
              `, { className: 'custom-popup', offset: POPUP_OFFSET });

              // Open the popup immediately
              tempMarker.openPopup();

              // Remove the temporary marker when its popup is closed
              tempMarker.on('popupclose', function() {
                map.removeLayer(tempMarker);
              });
            } else {
              // If no matching POI, search directly using Nominatim
              searchLocation(query);
            }
          }
        }
      }
    });

    return container;
  };
  searchControl.addTo(map);

  // Function to add custom user marker has been incorporated into addCustomMarker

  // Function to search for a location using Nominatim via Netlify function
  function searchLocation(query) {
    // Use the Netlify function instead of direct API call
    window.mapAPI.searchLocations(query)
      .then(data => {
        if (data.length > 0) {
          const { lat, lon, display_name } = data[0];
          const addressParts = display_name.split(', ');
          const city = addressParts[0];
          const country = addressParts[addressParts.length - 1];
          const placeName = `${city}, ${country}`;

          map.setView([lat, lon], 15);

          // Remove any existing search marker
          if (searchMarker) {
            map.removeLayer(searchMarker);
          }

          // Create a new marker
          const icon = L.icon({
            iconUrl: 'images/marker.svg',
            iconSize: [20, 32],
            iconAnchor: [10, 32],
            popupAnchor: POPUP_OFFSET  // Use the constant
          });

          searchMarker = L.marker([lat, lon], { icon }).addTo(map);
          searchMarker.bindPopup(`
            <div style="text-align: center;">
              <b style="color: #ffd700;">${placeName}</b><br>
              <button onclick="addCustomMarker(${lat}, ${lon}, this)" style="background-color: #262626; color: #ffd700; border: none; cursor: pointer;">Add to My Map</button>
            </div>
          `, { className: 'custom-popup', offset: POPUP_OFFSET });

          // Ensure the popup is opened with a slight delay to ensure the map has finished panning
          setTimeout(() => {
            searchMarker.openPopup();
          }, 100);

          // Remove marker when popup is closed if not saved
          searchMarker.on('popupclose', function() {
            map.removeLayer(searchMarker);
            searchMarker = null;
          });
        } else {
          alert("Location not found. Please try a different search term.");
        }
      })
      .catch(error => {
        console.error("Error searching for location:", error);
        alert("Error searching for location. Please try again.");
      });
  }

  // Event listeners
  // Function to get weather data for a location
  async function getWeatherData(lat, lng) {
    try {
      // Use the Netlify function instead of direct API call
      return await window.mapAPI.getWeatherData(lat, lng);
    } catch (error) {
      console.error('Error fetching weather data:', error);
      return null;
    }
  }

  // Function to get location name from coordinates
  async function getLocationName(lat, lng) {
    try {
      // Use the Netlify function instead of direct API call
      const data = await window.mapAPI.getLocationName(lat, lng);

      // Extract city and country from address
      const addressParts = data.display_name.split(', ');
      const city = addressParts[0];
      const country = addressParts[addressParts.length - 1];

      return { city, country };
    } catch (error) {
      console.error('Error getting location name:', error);
      return { city: 'Unknown', country: '' };
    }
  }

  // Function to display weather information
  async function displayWeather(weatherData) {
    const weatherContent = document.getElementById('weatherContent');

    if (!weatherData) {
      weatherContent.innerHTML = `
        <div style="text-align: center; padding: 10px;">
          <i class="fas fa-exclamation-circle" style="color: #ffd700; font-size: 24px;"></i>
          <p style="margin-top: 10px;">Weather data unavailable</p>
          <button onclick="retryWeather()" style="background-color: #861818; color: #ffd700; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-top: 5px;">
            <i class="fas fa-sync-alt"></i> Retry
          </button>
        </div>
      `;
      return;
    }

    // Get location name
    const { city, country } = await getLocationName(weatherData.coord.lat, weatherData.coord.lon);

    // Format data
    const temp = Math.round(weatherData.main.temp);
    const feelsLike = Math.round(weatherData.main.feels_like);
    const description = weatherData.weather[0].description;
    const icon = weatherData.weather[0].icon;
    const humidity = weatherData.main.humidity;
    const windSpeed = weatherData.wind.speed;
    const windDirection = getWindDirection(weatherData.wind.deg);
    const sunrise = new Date(weatherData.sys.sunrise * 1000).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    const sunset = new Date(weatherData.sys.sunset * 1000).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

    // Create enhanced weather display
    weatherContent.innerHTML = `
      <div style="text-align: center; margin-bottom: 10px;">
        <h3 style="margin: 0; font-size: 16px;">${city}, ${country}</h3>
        <p style="margin: 0; font-size: 12px; opacity: 0.8;">${new Date().toLocaleString()}</p>
      </div>

      <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 10px;">
        <img src="https://openweathermap.org/img/wn/${icon}@2x.png" alt="${description}" style="width: 60px; height: 60px;">
        <div>
          <div style="font-size: 1.5em; font-weight: bold;">${temp}°C</div>
          <div style="text-transform: capitalize;">${description}</div>
          <div style="font-size: 0.9em;">Feels like: ${feelsLike}°C</div>
        </div>
      </div>

      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 0.9em; margin-top: 10px;">
        <div>
          <i class="fas fa-tint" style="color: #4da6ff;"></i> Humidity: ${humidity}%
        </div>
        <div>
          <i class="fas fa-wind"></i> Wind: ${windSpeed} m/s ${windDirection}
        </div>
        <div>
          <i class="fas fa-sun" style="color: #ffd700;"></i> Sunrise: ${sunrise}
        </div>
        <div>
          <i class="fas fa-moon" style="color: #cccccc;"></i> Sunset: ${sunset}
        </div>
      </div>

      <div style="margin-top: 10px; text-align: center;">
        <button onclick="refreshWeather()" style="background-color: #333; color: #ffd700; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">
          <i class="fas fa-sync-alt"></i> Refresh
        </button>
      </div>
    `;
  }

  // Helper function to get wind direction from degrees
  function getWindDirection(degrees) {
    const directions = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'];
    const index = Math.round(degrees / 45) % 8;
    return directions[index];
  }

  // Function to close weather display
  function closeWeather() {
    document.getElementById('weatherInfo').style.display = 'none';
    weatherDisplayed = false;
  }

  // Function to refresh weather data
  function refreshWeather() {
    const center = map.getCenter();
    getWeatherData(center.lat, center.lng).then(data => displayWeather(data));
  }

  // Function to retry getting weather data
  function retryWeather() {
    refreshWeather();
  }

  // Function to toggle the weather info display
  function toggleWeather() {
    const weatherInfo = document.getElementById('weatherInfo');
    const weatherContent = document.getElementById('weatherContent');

    if (weatherInfo.style.display === 'none' || !weatherInfo.style.display) {
      // Show loading state
      weatherInfo.style.display = 'block';
      weatherContent.innerHTML = `
        <div style="text-align: center; padding: 15px;">
          <i class="fas fa-spinner fa-spin" style="font-size: 24px; color: #ffd700;"></i>
          <p>Loading weather data...</p>
        </div>
      `;

      // Set weather displayed flag to true
      weatherDisplayed = true;

      // Get weather for current map center
      const center = map.getCenter();
      getWeatherData(center.lat, center.lng)
        .then(data => displayWeather(data))
        .catch(error => {
          console.error('Error in toggleWeather:', error);
          weatherContent.innerHTML = `
            <div style="text-align: center;">
              <i class="fas fa-exclamation-circle" style="color: #ffd700; font-size: 24px;"></i>
              <p style="margin-top: 10px;">Failed to load weather data</p>
              <button onclick="retryWeather()" style="background-color: #861818; color: #ffd700; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-top: 5px;">
                <i class="fas fa-sync-alt"></i> Retry
              </button>
            </div>
          `;
        });

      // Close the quick actions menu - commented out
      // closeQuickActionsMenu();
    } else {
      weatherInfo.style.display = 'none';
      weatherDisplayed = false;
    }
  }

  // Function to share current location
  function shareLocation() {
    const center = map.getCenter();
    const zoom = map.getZoom();
    const url = `${window.location.origin}${window.location.pathname}?lat=${center.lat.toFixed(6)}&lng=${center.lng.toFixed(6)}&zoom=${zoom}`;

    if (navigator.share) {
      navigator.share({
        title: 'Check out this location on Vestigia',
        text: 'I found this interesting location on Vestigia!',
        url: url
      }).catch(err => {
        console.error('Error sharing:', err);
        // Fallback to clipboard copy
        copyToClipboard(url);
      });
    } else {
      // Fallback for browsers that don't support Web Share API
      copyToClipboard(url);
    }
  }

  // Helper function to copy text to clipboard
  function copyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    alert('Location URL copied to clipboard!');
  }

  // Variable to store heatmap layer
  let heatmapLayer = null;

  // Function to toggle heatmap
  function toggleHeatmap() {
    if (heatmapLayer) {
      // If heatmap exists, remove it
      map.removeLayer(heatmapLayer);
      heatmapLayer = null;
    } else {
      // Create heatmap from POI markers
      const heatData = poiMarkers.map(poi => [poi.lat, poi.lng, 0.5]); // lat, lng, intensity

      heatmapLayer = L.heatLayer(heatData, {
        radius: 25,
        blur: 15,
        maxZoom: 17,
        gradient: {
          0.4: '#4CAF50',
          0.6: '#FF8C00',
          0.8: '#861818'
        }
      }).addTo(map);
    }
  }

  // Function to find nearby POIs
  function findNearby() {
    const center = map.getCenter();
    const radius = 80; // km

    // Find POIs within radius
    const nearbyPOIs = poiMarkers.filter(poi => {
      const distance = map.distance([center.lat, center.lng], [poi.lat, poi.lng]) / 1000; // Convert to km
      return distance <= radius;
    });

    if (nearbyPOIs.length === 0) {
      alert('No points of interest found within 80km/50miles of this location.');
      return;
    }

    // Clear existing temporary markers
    clearTempMarkers();

    // Add markers for nearby POIs with pulse effect
    nearbyPOIs.forEach(poi => {
      const icon = L.divIcon({
        html: `<div class="pulse-marker"><img src="images/poiMarker.svg" style="width: 20px; height: 32px;"></div>`,
        className: '',
        iconSize: [20, 32],
        iconAnchor: [10, 32]
      });

      const marker = L.marker([poi.lat, poi.lng], { icon }).addTo(map);
      marker.bindPopup(`
        <div style="text-align: center;">
          <b style="color: #ffd700;">${poi.name}</b><br />
          <span style="color: #ffd700;">${poi.description}</span><br />
          <button onclick="addToUserList(${poi.lat}, ${poi.lng}, '${poi.name.replace(/'/g, "\\'")}', '${poi.description.replace(/'/g, "\\'")}')"
                  style="background-color: #262626; color: #ffd700; border: none; cursor: pointer; margin-top: 10px;">
            Add to My List
          </button>
        </div>
      `, { className: 'custom-popup', offset: POPUP_OFFSET });

      tempMarkers.push(marker);
    });

    // Create a bounds object and fit the map to it
    const bounds = L.latLngBounds(nearbyPOIs.map(poi => [poi.lat, poi.lng]));
    map.fitBounds(bounds, { padding: [50, 50] });

    // Show a message with the number of POIs found
    alert(`Found ${nearbyPOIs.length} points of interest within 5km.`);

    // Close the quick actions menu - commented out
    // closeQuickActionsMenu();
  }

  // Function to clear temporary markers
  function clearTempMarkers() {
    tempMarkers.forEach(marker => map.removeLayer(marker));
    tempMarkers = [];

    // Also remove search marker if it exists
    if (searchMarker) {
      map.removeLayer(searchMarker);
      searchMarker = null;
    }

    // Close the quick actions menu - commented out
    // closeQuickActionsMenu();
  }

  // No temporary pin function for zoom controls

  // Function to add a custom marker at the center of the map
  function addCustomMarkerAtCenter() {
    const center = map.getCenter();

    // Create a new marker
    const icon = L.icon({
      iconUrl: 'images/marker.svg',
      iconSize: [20, 32],
      iconAnchor: [10, 32],
      popupAnchor: POPUP_OFFSET
    });

    // Remove any existing search marker
    if (searchMarker) {
      map.removeLayer(searchMarker);
    }

    // Create a new marker at the center
    searchMarker = L.marker([center.lat, center.lng], { icon }).addTo(map);

    // Create a popup for the marker
    searchMarker.bindPopup(`
      <div style="text-align: center;">
        <b style="color: #ffd700;">New Marker</b><br>
        <input type="text" id="markerName" placeholder="Enter name..." style="margin: 5px 0; padding: 5px; width: 100%; box-sizing: border-box;">
        <button onclick="saveCustomMarker(${center.lat}, ${center.lng})"
                style="background-color: #262626; color: #ffd700; border: none; cursor: pointer; margin-top: 5px; padding: 5px 10px;">
          Save
        </button>
      </div>
    `, { className: 'custom-popup', offset: POPUP_OFFSET });

    // Open the popup
    searchMarker.openPopup();

    // Close the quick actions menu - commented out
    // closeQuickActionsMenu();
  }

  // Function to save a custom marker with the name from the input field
  function saveCustomMarker(lat, lng) {
    const nameInput = document.getElementById('markerName');
    const name = nameInput ? nameInput.value.trim() : 'Custom Marker';

    if (!name) {
      alert('Please enter a name for this marker');
      return;
    }

    // Add the marker to user markers
    addCustomMarker(lat, lng, null, name);

    // Remove the temporary search marker
    if (searchMarker) {
      map.removeLayer(searchMarker);
      searchMarker = null;
    }
  }

  // Helper function to close the quick actions menu - commented out
  /*function closeQuickActionsMenu() {
    const quickActions = document.getElementById('quickActions');
    const floatingActionBtn = document.getElementById('floatingActionBtn');

    if (quickActions && floatingActionBtn) {
      quickActions.style.display = 'none';
      floatingActionBtn.innerHTML = '<i class="fas fa-plus"></i>';
    }
  }*/

  // Variable to track if weather is currently displayed
  let weatherDisplayed = false;

  // Function to update weather if it's currently displayed
  function updateWeatherOnMapMove() {
    if (weatherDisplayed) {
      const weatherInfo = document.getElementById('weatherInfo');
      if (weatherInfo.style.display === 'block') {
        const center = map.getCenter();
        getWeatherData(center.lat, center.lng).then(data => displayWeather(data));
      }
    }
  }

  // Function to check if a location exists in the itinerary
  function checkLocationInItinerary() {
    // Get all events from local storage
    const events = JSON.parse(localStorage.getItem('events')) || [];

    // Create a set of all location names in the events
    const locationNames = new Set();
    events.forEach(event => {
      if (event.location) {
        locationNames.add(event.location);
      }
    });

    // Update the addedToItinerary flag for all markers
    userMarkers.forEach(marker => {
      if (locationNames.has(marker.name)) {
        marker.addedToItinerary = true;
      }
    });

    // Save the updated markers
    saveUserMarkers();

    // Update the UI
    updatePlacesList();
  }

  document.addEventListener("DOMContentLoaded", function () {
    // First, try to add missing columns to the user_markers table
    if (window.mapAPI && window.mapAPI.addMissingColumns) {
      console.log('Adding missing columns to user_markers table...');
      window.mapAPI.addMissingColumns()
        .then(result => {
          console.log('Add missing columns result:', result);
        })
        .catch(error => {
          console.error('Error adding missing columns:', error);
          // Continue with initialization even if this fails
        })
        .finally(() => {
          // Initialize POI markers
          addPOIMarkers().catch(error => console.error('Error loading POI markers:', error));

          // Load user markers from Supabase
          console.log('Loading user markers from Supabase');
          loadUserMarkers()
            .then(() => {
              // After loading markers, check which ones are in the itinerary
              checkLocationInItinerary();
            })
            .catch(error => console.error('Error loading markers:', error));
        });
    } else {
      // Initialize POI markers
      addPOIMarkers().catch(error => console.error('Error loading POI markers:', error));

      // Load user markers from Supabase
      console.log('Loading user markers from Supabase');
      loadUserMarkers()
        .then(() => {
          // After loading markers, check which ones are in the itinerary
          checkLocationInItinerary();
        })
        .catch(error => console.error('Error loading markers:', error));
    }
    document.getElementById("switchButton").addEventListener("click", switchLocations);

    // Set up floating action button and quick actions - commented out
    /*const floatingActionBtn = document.getElementById('floatingActionBtn');
    const quickActions = document.getElementById('quickActions');

    floatingActionBtn.addEventListener('click', function() {
      if (quickActions.style.display === 'none' || !quickActions.style.display) {
        quickActions.style.display = 'flex';
        floatingActionBtn.innerHTML = '<i class="fas fa-times"></i>';
      } else {
        quickActions.style.display = 'none';
        floatingActionBtn.innerHTML = '<i class="fas fa-plus"></i>';
      }
    });

    // Set up quick action buttons
    document.getElementById('toggleWeatherBtn').addEventListener('click', toggleWeather);
    document.getElementById('findNearbyBtn').addEventListener('click', findNearby);
    document.getElementById('shareLocationBtn').addEventListener('click', shareLocation);*/

    // Add map event listeners for weather updates
    map.on('moveend', function() {
      // Only update weather if it's currently displayed
      updateWeatherOnMapMove();
    });

    // Check for location parameters in URL
    const urlParams = new URLSearchParams(window.location.search);
    const lat = urlParams.get('lat');
    const lng = urlParams.get('lng');
    const zoom = urlParams.get('zoom');

    if (lat && lng) {
      map.setView([parseFloat(lat), parseFloat(lng)], zoom ? parseInt(zoom) : 13);
      // Add a temporary marker at the shared location
      const icon = L.icon({
        iconUrl: 'images/marker.svg',
        iconSize: [20, 32],
        iconAnchor: [10, 32],
        popupAnchor: POPUP_OFFSET
      });

      const sharedMarker = L.marker([parseFloat(lat), parseFloat(lng)], { icon }).addTo(map);
      sharedMarker.bindPopup(`
        <div style="text-align: center;">
          <b style="color: #ffd700;">Shared Location</b><br>
          <button onclick="addCustomMarker(${parseFloat(lat)}, ${parseFloat(lng)}, this)"
                  style="background-color: #262626; color: #ffd700; border: none; cursor: pointer; margin-top: 10px;">
            Add to My Map
          </button>
        </div>
      `, { className: 'custom-popup', offset: POPUP_OFFSET }).openPopup();

      tempMarkers.push(sharedMarker);
    }

    // Add event listener to close suggestions when clicking outside the search box
    document.addEventListener("click", function (e) {
      const searchInput = document.getElementById("mapLocationSearch");
      if (searchInput && !searchInput.contains(e.target)) {
        const suggestionsContainer = document.getElementById("mapSuggestions");
        suggestionsContainer.innerHTML = '';
        suggestionsContainer.style.display = 'none';

        // Restore the search input's full rounded corners when suggestions are hidden
        searchInput.style.borderRadius = '4px';
      }
    });

    const location = new URLSearchParams(window.location.search).get('location');
    if (location) {
      // First try to find the location in saved user markers
      // Search for markers that contain the location string anywhere in the name
      const matchedUserMarker = userMarkers.find(marker =>
        marker.name.toLowerCase().includes(location.toLowerCase())
      );

      // Then try to find in POI markers if not found in user markers
      // Search for POIs that contain the location string anywhere in the name
      const matchedPOIMarker = poiMarkers.find(poi =>
        poi.name.toLowerCase().includes(location.toLowerCase())
      );

      if (matchedUserMarker) {
        // If it's a user marker, use the existing marker reference
        map.setView([matchedUserMarker.lat, matchedUserMarker.lng], 13);
        if (matchedUserMarker.marker) {
          matchedUserMarker.marker.openPopup();
        }
      } else if (matchedPOIMarker) {
        // If it's a POI marker, create a temporary marker at that location
        map.setView([matchedPOIMarker.lat, matchedPOIMarker.lng], 13);

        // Create a temporary marker with the POI information
        const tempIcon = L.icon({
          iconUrl: 'images/poiMarker.svg',
          iconSize: [20, 32],
          iconAnchor: [10, 32],
          popupAnchor: POPUP_OFFSET  // Use the constant
        });

        const tempMarker = L.marker([matchedPOIMarker.lat, matchedPOIMarker.lng], { icon: tempIcon }).addTo(map);
        const escapedName = matchedPOIMarker.name.replace(/'/g, "\\'");
        const escapedDesc = matchedPOIMarker.description.replace(/'/g, "\\'");

        tempMarker.bindPopup(`
          <div style="text-align: center;">
            <b style="color: #ffd700;">${matchedPOIMarker.name}</b><br />
            <span style="color: #ffd700; font-size: 0.9em;">${matchedPOIMarker.description}</span>
            <div style="display: flex; justify-content: space-between; margin-top: 10px;">
              <button onclick="window.addToUserListDirect(${parseFloat(matchedPOIMarker.lat)}, ${parseFloat(matchedPOIMarker.lng)}, '${escapedName}', '${escapedDesc}')"
                      style="background-color: #262626; color: #ffd700; border: none; cursor: pointer;">
                Add to My List
              </button>
              <a href="https://en.wikipedia.org/wiki/${encodeURIComponent(matchedPOIMarker.wikiName || matchedPOIMarker.name)}" target="_blank" style="color: #ffd700;">Wiki</a>
            </div>
          </div>
        `, { className: 'custom-popup', offset: POPUP_OFFSET });

        tempMarker.openPopup();

        // Remove the marker when its popup is closed
        tempMarker.on('popupclose', function() {
          map.removeLayer(tempMarker);
        });
      } else {
        // If not found in either, try geocoding via Netlify function
        window.mapAPI.searchLocations(location)
          .then(data => {
            if (data.length > 0) {
              const { lat, lon, display_name } = data[0];
              const addressParts = display_name.split(', ');
              const city = addressParts[0];
              const country = addressParts[addressParts.length - 1];
              const placeName = `${city}, ${country}`;

              map.setView([lat, lon], 13);

              if (searchMarker) {
                map.removeLayer(searchMarker);
              }

              searchMarker = L.marker([lat, lon], {
                icon: L.icon({
                  iconUrl: 'images/marker.svg',
                  iconSize: [20, 32],
                  iconAnchor: [10, 32],
                  popupAnchor: POPUP_OFFSET  // Use the constant
                })
              }).addTo(map);

              searchMarker.bindPopup(`
                <b style="color: #ffd700; text-align: center;">${placeName}</b><br />
                <div style="text-align: center;">
                  <button onclick="addCustomMarker(${lat}, ${lon}, this)" style="background-color: #262626; color: #ffd700; border: none; cursor: pointer;">Add to My Map</button>
                </div>
              `, { className: 'custom-popup', offset: POPUP_OFFSET });

              // Ensure the popup is opened with a slight delay to ensure the map has finished panning
              setTimeout(() => {
                searchMarker.openPopup();
              }, 100);

              // Remove marker when popup is closed if not saved
              searchMarker.on('popupclose', function() {
                map.removeLayer(searchMarker);
                searchMarker = null;
              });
            } else {
              alert("Location not found.");
            }
          })
          .catch(error => console.error("Error searching location:", error));
      }
    }

    document.getElementById("directionsButton").addEventListener("click", getDirections);
    document.getElementById("endLocation").addEventListener("keypress", function (e) {
      if (e.key === "Enter") getDirections();
    });

    // Sortable functionality removed

    // Removed event listeners for updateDropdowns
  });

  map.on('click', function(e) {
    // Prevent placing a marker when clicking on the map
    if (e.originalEvent.target.classList.contains('leaflet-interactive')) {
      return;
    }

    // Prevent placing a marker when clicking on zoom controls or near them
    const customControlsContainer = document.querySelector('div[style*="position: absolute"][style*="top: 10px"][style*="left: 10px"]');
    if (customControlsContainer) {
      const rect = customControlsContainer.getBoundingClientRect();
      // Add a buffer zone around the controls
      const buffer = 10; // pixels
      if (e.originalEvent.clientX >= rect.left - buffer &&
          e.originalEvent.clientX <= rect.right + buffer &&
          e.originalEvent.clientY >= rect.top - buffer &&
          e.originalEvent.clientY <= rect.bottom + buffer) {
        return; // Don't place a marker if click is on or near the controls
      }
    }

    const { lat, lng } = e.latlng;

    // Remove any existing search marker when placing a new one
    if (searchMarker) {
      map.removeLayer(searchMarker);
      searchMarker = null;
    }

    const icon = L.icon({
      iconUrl: 'images/marker.svg',
      iconSize: [20, 32],
      iconAnchor: [10, 32],
      popupAnchor: POPUP_OFFSET  // Use the constant
    });

    // Create marker and add directly to map
    const marker = L.marker([lat, lng], { icon: icon }).addTo(map);

    // Create popup content
    const popupContent = '<div style="text-align: center;">' +
      '<b style="color: #ffd700;">Pinned Location</b><br />' +
      'Coordinates: ' + lat.toFixed(5) + ', ' + lng.toFixed(5) + '<br />' +
      '<button onclick="addCustomMarker(' + lat + ', ' + lng + ', this)" style="background-color: #262626; color: #ffd700; border: none; cursor: pointer;">Add to My Map</button>' +
      '</div>';

    // Bind popup directly to marker
    marker.bindPopup(popupContent, {
      className: 'custom-popup',
      offset: POPUP_OFFSET  // Offset to position popup above the marker
    }).openPopup();

    // Remove marker when popup is closed
    marker.on('popupclose', function() {
      map.removeLayer(marker);
    });
  });

  function addCustomMarker(lat, lng, button) {
  const customName = prompt("Enter a name for this location:");
  if (!customName) return;

  // Store the temporary marker reference if it exists
  let tempMarker = null;
  if (button && button.closest('.leaflet-popup')) {
    tempMarker = button.closest('.leaflet-popup')._source;
  }

  // Immediately update the popup with the custom name and delete button
  if (tempMarker && map.hasLayer(tempMarker)) {
    tempMarker.setPopupContent(`
      <div style="text-align: center;">
        <b style="color: #ffd700;">${customName}</b><br>
        <span style="color: #ffd700;">Loading location details...</span><br>
        <button onclick="removeUserMarker(${lat}, ${lng})" style="background-color: #262626; color: #ffd700; border: none; cursor: pointer; margin-top: 10px;">Delete</button>
      </div>
    `);
  }

  // Create a new marker with the custom name
  const icon = L.icon({
    iconUrl: 'images/marker.svg',
    iconSize: [20, 32],
    iconAnchor: [10, 32],
    popupAnchor: POPUP_OFFSET  // Use the constant
  });

  // Create marker and add directly to map
  console.log('Creating marker at:', lat, lng);
  const marker = L.marker([lat, lng], { icon }).addTo(map);
  console.log('Marker created:', marker);

  // Create popup content
  const popupContent = '<div style="text-align: center;">' +
    '<b style="color: #ffd700;">' + customName + '</b><br>' +
    '<span style="color: #ffd700;">Loading location details...</span><br>' +
    '<button onclick="removeUserMarker(' + lat + ', ' + lng + ')" style="background-color: #262626; color: #ffd700; border: none; cursor: pointer; margin-top: 10px;">Delete</button>' +
    '</div>';

  // Bind popup directly to marker
  marker.bindPopup(popupContent, {
    className: 'custom-popup',
    offset: POPUP_OFFSET  // Offset to position popup above the marker
  }).openPopup();

  // Add to userMarkers array immediately
  const markerData = {
    lat,
    lng,
    name: customName,
    placeName: "Loading location details...",
    description: "Custom location",
    marker,
    isPOI: false,
    isCustom: true  // Flag to identify custom markers
  };

  console.log('Adding custom marker to userMarkers:', markerData);
  userMarkers.push(markerData);
  console.log('userMarkers after adding custom marker:', userMarkers);

  // Save to local storage
  // We're using the saveUserMarkers function which will handle the separation
  // between custom markers (local storage) and POI markers (Supabase)
  saveUserMarkers();

  console.log('Calling updatePlacesList after adding custom marker');
  updatePlacesList();

  // Remove the temporary marker if it exists
  if (tempMarker && map.hasLayer(tempMarker)) {
    tempMarker.closePopup();
    map.removeLayer(tempMarker);
  }

  // Open the popup of the new marker
  marker.openPopup();

  // Fetch location details in the background using Netlify function
  window.mapAPI.getLocationName(lat, lng)
    .then(data => {
      const displayName = data.display_name;
      const addressParts = displayName.split(', ');
      const city = addressParts[0];
      const country = addressParts[addressParts.length - 1];
      const placeName = `${city}, ${country}`;

      // Update the marker's popup content with the place name
      marker.setPopupContent(`
        <div style="text-align: center;">
          <b style="color: #ffd700;">${customName}</b><br>
          <span style="color: #ffd700;">${placeName}</span><br>
          <button onclick="removeUserMarker(${lat}, ${lng})" style="background-color: #262626; color: #ffd700; border: none; cursor: pointer; margin-top: 10px;">Delete</button>
        </div>
      `);

      // Update the marker data
      const markerIndex = userMarkers.findIndex(m =>
        Math.abs(m.lat - lat) < 0.0001 && Math.abs(m.lng - lng) < 0.0001 && m.name === customName
      );

      if (markerIndex !== -1) {
        userMarkers[markerIndex].placeName = placeName;
        saveUserMarkers();
        updatePlacesList();
      }
    })
    .catch(error => {
      console.error("Error fetching location details:", error);
      // Update with a default place name on error
      marker.setPopupContent(`
        <div style="text-align: center;">
          <b style="color: #ffd700;">${customName}</b><br>
          <span style="color: #ffd700;">Custom Location</span><br>
          <button onclick="removeUserMarker(${lat}, ${lng})" style="background-color: #262626; color: #ffd700; border: none; cursor: pointer; margin-top: 10px;">Delete</button>
        </div>
      `);

      // Update the marker data
      const markerIndex = userMarkers.findIndex(m =>
        Math.abs(m.lat - lat) < 0.0001 && Math.abs(m.lng - lng) < 0.0001 && m.name === customName
      );

      if (markerIndex !== -1) {
        userMarkers[markerIndex].placeName = "Custom Location";
        saveUserMarkers();
        updatePlacesList();
      }
    });
}

  // Function to remove marker from the list below the map without deleting it from the map
  function removePOIMarker(lat, lng) {
  const marker = userMarkers.find(marker => marker.lat === lat && marker.lng === lng);
  if (marker) {
    marker.marker.bindPopup(`
      <div style="text-align: center;">
        <b style="color: #ffd700;">${marker.name}</b><br />
        <b style="color: #ffd700;">${marker.description}</b>
        <div style="display: flex; justify-content: space-between; margin-top: 10px;">
          <button onclick="addPOIToUserList(${lat}, ${lng}, '${marker.name}', '${marker.description}')" style="background-color: #262626; color: #ffd700; border: none; cursor: pointer;">Add to My List</button>
          <a href="https://en.wikipedia.org/wiki/${encodeURIComponent(marker.name)}" target="_blank" style="color: #ffd700;">Wiki</a>
        </div>
      </div>
    `, { className: 'custom-popup', offset: POPUP_OFFSET }).openPopup();
  }
  userMarkers = userMarkers.filter(marker => marker.lat !== lat || marker.lng !== lng);
  saveUserMarkers();
  updatePlacesList();
}

  // Popup styles are now defined in the head of the document

  // Function to add a POI marker to the user list
  async function addPOIToUserList(lat, lng, name, description) {
    // This is just a wrapper around addToUserListDirect for backward compatibility
    await window.addToUserListDirect(lat, lng, name, description);
  }

  // Function to update all POI markers with the same coordinates
  function updatePOIMarkers(lat, lng, isInUserList) {
    // First check the stored POI marker objects
    for (let i = 0; i < poiMarkerObjects.length; i++) {
      const poiMarker = poiMarkerObjects[i];
      if (Math.abs(poiMarker.lat - lat) < 0.0001 && Math.abs(poiMarker.lng - lng) < 0.0001) {
        // This is a POI marker with the same coordinates, update its popup
        const poi = poiMarkers.find(p =>
          Math.abs(p.lat - lat) < 0.0001 &&
          Math.abs(p.lng - lng) < 0.0001 &&
          p.name === poiMarker.name
        );

        if (poi) {
          // For POI markers, show "Add to My List" button if not in user list, or "Delete" button if in user list
          const buttonHtml = isInUserList ?
            `<button onclick="removeUserMarker(${poi.lat}, ${poi.lng})" style="background-color: #262626; color: #ffd700; border: none; cursor: pointer;">Delete</button>` :
            `<button onclick="addToUserList(${poi.lat}, ${poi.lng}, '${poi.name.replace(/'/g, "\\'")}'', '${poi.description.replace(/'/g, "\\'")}'')" style="background-color: #262626; color: #ffd700; border: none; cursor: pointer;">Add to My List</button>`;

          const popupContent = `
            <div style="text-align: center;">
              <b style="color: #ffd700;">${poi.name}</b><br />
              <span style="color: #ffd700;">${poi.description}</span><br />
              <div style="display: flex; justify-content: space-between; margin-top: 10px;">
                ${buttonHtml}
                <a href="https://en.wikipedia.org/wiki/${encodeURIComponent(poi.wikiName || poi.name)}" target="_blank" style="color: #ffd700;">Wiki</a>
              </div>
            </div>
          `;

          poiMarker.marker.bindPopup(popupContent, { className: 'custom-popup', offset: POPUP_OFFSET });

          // If the popup is open, update it
          if (poiMarker.marker._popup && poiMarker.marker._popup.isOpen()) {
            poiMarker.marker.openPopup();
          }
        }
      }
    }
  }


  window.addEventListener('beforeunload', function (e) {
    const markersChanged = JSON.stringify(userMarkers) !== JSON.stringify(savedUserMarkers);
    if (markersChanged) {
      const confirmationMessage = 'You have unsaved changes. Are you sure you want to leave?';
      e.returnValue = confirmationMessage;
      return confirmationMessage;
    }
  });


  function removeRoute() {
    map.eachLayer(layer => {
      if (layer instanceof L.Polyline && !layer._popup) {
        map.removeLayer(layer);
      }
    });
    tempMarkers.forEach(marker => map.removeLayer(marker));
    tempMarkers = [];
    map.closePopup();
  }

  // Function to handle adding a location to the itinerary
  function openAddToItinerary(locationName) {
    // Store the location in localStorage - locationName is already encoded from the calling function
    const decodedName = decodeURIComponent(locationName);
    console.log('Storing location in localStorage:', decodedName);
    localStorage.setItem('pendingItineraryLocation', decodedName);

    // Find the marker in userMarkers and set the addedToItinerary flag
    const markerIndex = userMarkers.findIndex(marker => marker.name === decodedName);
    if (markerIndex !== -1) {
      userMarkers[markerIndex].addedToItinerary = true;
      saveUserMarkers(); // Save the updated marker data
      updatePlacesList(); // Update the UI to reflect the change
    }

    // Open the itinerary page in a new tab
    window.open('itinerary.html?openLocationModal=true', '_blank');
  }

  let typingTimer; // Timer identifier
  const debounceDelay = 300; // Delay in milliseconds

  /**
   * Fetch and display location suggestions based on user input
   * Searches across user markers, POI markers, and external location API
   *
   * @param {string} query - The search query entered by the user
   */
  function fetchSuggestions(query) {
    const searchQuery = query.toLowerCase();
    const suggestionsContainer = document.getElementById("mapSuggestions");
    suggestionsContainer.innerHTML = '';

    if (!searchQuery) {
      return;
    }

    // Filter user markers first - search anywhere in the name
    const userSuggestions = userMarkers
      .filter(marker => marker.name.toLowerCase().includes(searchQuery))
      .map(marker => ({
        name: marker.name,
        type: 'user',
        lat: marker.lat,
        lng: marker.lng,
        description: marker.placeName || marker.description
      }));

    // Create a set of coordinates that are already in user markers to avoid duplicates
    const userCoordinates = new Set();
    userMarkers.forEach(marker => {
      // Create a unique key for each location using lat and lng
      const key = `${marker.lat.toFixed(5)},${marker.lng.toFixed(5)}`;
      userCoordinates.add(key);
    });

    // Filter POI markers, excluding those that are already in user markers
    // Search anywhere in the POI name, not just at the beginning
    const poiSuggestions = poiMarkers
      .filter(poi => {
        // Check if the POI name contains the search query anywhere in the string
        const nameMatches = poi.name.toLowerCase().includes(searchQuery);
        // Check if this POI is already in user markers (by coordinates)
        const key = `${poi.lat.toFixed(5)},${poi.lng.toFixed(5)}`;
        const isAlreadySaved = userCoordinates.has(key);
        // Only include POIs that match the query AND are not already saved
        return nameMatches && !isAlreadySaved;
      })
      .map(poi => ({
        name: poi.name,
        type: 'poi',
        lat: poi.lat,
        lng: poi.lng,
        description: poi.description
      }));

    // Combine and limit suggestions to 4 maximum
    const combinedSuggestions = [...userSuggestions, ...poiSuggestions].slice(0, 4);

    // If we have less than 4 suggestions, fetch from Nominatim via Netlify function
    if (combinedSuggestions.length < 4) {
      window.mapAPI.searchLocations(query)
        .then(data => {
          const apiSuggestions = data
            .slice(0, 4 - combinedSuggestions.length)
            .map(item => ({
              name: item.display_name.split(',')[0],
              type: 'location',
              lat: parseFloat(item.lat),
              lng: parseFloat(item.lon),
              description: item.display_name
            }));

          showSuggestions([...combinedSuggestions, ...apiSuggestions]);
        })
        .catch(error => console.error("Error fetching suggestions:", error));
    } else {
      showSuggestions(combinedSuggestions);
    }
  }

  // Function to display search suggestions
  function showSuggestions(suggestions) {
    const suggestionsContainer = document.getElementById("mapSuggestions");
    suggestionsContainer.innerHTML = '';

    suggestions.forEach(suggestion => {
      const suggestionItem = document.createElement("div");
      suggestionItem.className = "suggestion-item";

      // Add icon based on suggestion type
      let icon = '';
      if (suggestion.type === 'user') {
        icon = '<i class="fa fa-star" style="color: #ffd700; margin-right: 5px;"></i>';
      } else if (suggestion.type === 'poi') {
        icon = '<i class="fa fa-map-marker" style="color: #861818; margin-right: 5px;"></i>';
      } else {
        icon = '<i class="fa fa-search" style="color: #861818; margin-right: 5px;"></i>';
      }

      suggestionItem.innerHTML = `${icon}${suggestion.name}`;
      suggestionItem.title = suggestion.description;

      suggestionItem.onclick = (e) => {
        e.stopPropagation();
        document.getElementById("mapLocationSearch").value = suggestion.name;
        suggestionsContainer.innerHTML = '';

        // Center map on location
        map.setView([suggestion.lat, suggestion.lng], 15);

        // Handle marker display based on type
        if (suggestion.type === 'poi') {
          // Create a temporary marker at the POI location with the same popup content
          // Create a temporary marker to show the POI information

          // Find the matching POI in the original data
          const matchingPoi = poiMarkers.find(poi =>
            Math.abs(poi.lat - suggestion.lat) < 0.0001 &&
            Math.abs(poi.lng - suggestion.lng) < 0.0001
          );

          if (matchingPoi) {
            // Zoom to the location
            map.setView([matchingPoi.lat, matchingPoi.lng], 16);

            // Create a temporary marker with the same popup content as the original
            const escapedName = matchingPoi.name.replace(/'/g, "\\'");
            const escapedDesc = matchingPoi.description.replace(/'/g, "\\'");
            const photosHtml = matchingPoi.photos ? matchingPoi.photos.map(photo =>
              `<img src="${photo}" alt="${matchingPoi.name}" style="width: 100px; height: auto; margin: 5px;">`).join('') : '';

            // Create a temporary marker that will be removed when its popup is closed
            const tempIcon = L.icon({
              iconUrl: 'images/poiMarker.svg',
              iconSize: [20, 32],
              iconAnchor: [10, 32],
              popupAnchor: POPUP_OFFSET  // Use the constant
            });

            const tempMarker = L.marker([matchingPoi.lat, matchingPoi.lng], { icon: tempIcon }).addTo(map);

            // Check if this POI is already in the user's list
            const isInUserList = userMarkers.some(marker =>
              Math.abs(marker.lat - matchingPoi.lat) < 0.0001 &&
              Math.abs(marker.lng - matchingPoi.lng) < 0.0001 &&
              marker.name === matchingPoi.name
            );

            // Create appropriate button based on whether it's already in the list
            // For POI markers, we don't show a delete button even if it's in the user list
            const buttonHtml = isInUserList ?
              `<div style="color: #ffd700;">Added to your list</div>` :
              `<button onclick="window.addToUserListDirect(${parseFloat(matchingPoi.lat)}, ${parseFloat(matchingPoi.lng)}, '${escapedName}', '${escapedDesc}')"
                       style="background-color: #262626; color: #ffd700; border: none; cursor: pointer;">
                 Add to My List
               </button>`;

            tempMarker.bindPopup(`
              <div style="text-align: center;">
                <b style="color: #ffd700;">${matchingPoi.name}</b><br />
                <span style="color: #ffd700; font-size: 0.9em;">${matchingPoi.description}</span>
                <div style="display: flex; justify-content: space-between; margin-top: 10px;">
                  ${buttonHtml}
                  <a href="https://en.wikipedia.org/wiki/${encodeURIComponent(matchingPoi.wikiName || matchingPoi.name)}" target="_blank" style="color: #ffd700;">Wiki</a>
                </div>
                ${photosHtml}
              </div>
            `, { className: 'custom-popup', offset: POPUP_OFFSET });

            // Open the popup immediately
            tempMarker.openPopup();

            // Remove the temporary marker when its popup is closed
            tempMarker.on('popupclose', function() {
              map.removeLayer(tempMarker);
            });
          }
        } else if (suggestion.type === 'user') {
          const userMarker = userMarkers.find(m =>
            Math.abs(m.lat - suggestion.lat) < 0.0001 &&
            Math.abs(m.lng - suggestion.lng) < 0.0001
          );
          if (userMarker && userMarker.marker) {
            // Ensure the popup is opened with a slight delay
            setTimeout(() => {
              userMarker.marker.openPopup();
            }, 100);
          }
        } else {
          // For locations from the API
          if (searchMarker) {
            map.removeLayer(searchMarker);
          }

          const icon = L.icon({
            iconUrl: 'images/marker.svg',
            iconSize: [20, 32],
            iconAnchor: [10, 32],
            popupAnchor: POPUP_OFFSET  // Use the constant
          });

          searchMarker = L.marker([suggestion.lat, suggestion.lng], { icon }).addTo(map);
          searchMarker.bindPopup(`
            <div style="text-align: center;">
              <b style="color: #ffd700;">${suggestion.name}</b><br>
              <button onclick="addCustomMarker(${suggestion.lat}, ${suggestion.lng}, this)" style="background-color: #262626; color: #ffd700; border: none; cursor: pointer;">Add to My Map</button>
            </div>
          `, { className: 'custom-popup', offset: POPUP_OFFSET });

          // Ensure the popup is opened
          setTimeout(() => {
            searchMarker.openPopup();
          }, 100);

          // Remove marker when popup is closed if not saved
          searchMarker.on('popupclose', function() {
            map.removeLayer(searchMarker);
            searchMarker = null;
          });
        }
      };

      suggestionsContainer.appendChild(suggestionItem);
    });

    // Add styling to suggestions container
    if (suggestions.length > 0) {
      // Show the suggestions container
      suggestionsContainer.style.display = 'block';

      // Update the search input to have rounded top corners only when suggestions are shown
      document.getElementById('mapLocationSearch').style.borderRadius = '4px 4px 0 0';
    } else {
      // Hide the suggestions container
      suggestionsContainer.style.display = 'none';

      // Restore the search input's full rounded corners when suggestions are hidden
      document.getElementById('mapLocationSearch').style.borderRadius = '4px';
    }
  }

  // Make the addToUserList function accessible globally
  window.addToUserListDirect = async function(lat, lng, name, description) {
    console.log('Adding to user list (direct):', lat, lng, name, description);
    console.log('Types:', typeof lat, typeof lng, typeof name, typeof description);

    try {
      // Validate inputs
      if (typeof lat !== 'number' || isNaN(lat)) {
        console.error('Invalid latitude:', lat);
        lat = parseFloat(lat);
        console.log('Parsed latitude:', lat, typeof lat);
        if (isNaN(lat)) {
          throw new Error('Invalid latitude value');
        }
      }

      if (typeof lng !== 'number' || isNaN(lng)) {
        console.error('Invalid longitude:', lng);
        lng = parseFloat(lng);
        console.log('Parsed longitude:', lng, typeof lng);
        if (isNaN(lng)) {
          throw new Error('Invalid longitude value');
        }
      }

      if (!name || typeof name !== 'string') {
        console.error('Invalid name:', name);
        throw new Error('Invalid name value');
      }

      if (!description || typeof description !== 'string') {
        console.error('Invalid description:', description);
        description = description || 'No description available';
      }

      // Check if this POI is already in the user's list - more thorough check
      const existingMarker = userMarkers.find(marker =>
        Math.abs(marker.lat - lat) < 0.0001 &&
        Math.abs(marker.lng - lng) < 0.0001 &&
        marker.name === name
      );

      if (existingMarker) {
        console.log(`Marker already exists in user list: ${name} at ${lat},${lng}`);
        // If the marker already exists, just open its popup and return
        if (existingMarker.marker) {
          map.setView([existingMarker.lat, existingMarker.lng], 16);
          existingMarker.marker.openPopup();
        }
        return;
      }

      // Also check for any marker at the same coordinates with a different name
      const markerAtSameLocation = userMarkers.find(marker =>
        Math.abs(marker.lat - lat) < 0.0001 &&
        Math.abs(marker.lng - lng) < 0.0001
      );

      if (markerAtSameLocation) {
        console.log(`Found marker at same location with different name: ${markerAtSameLocation.name} vs ${name}`);
        // We'll continue adding, but log this for debugging
      }

      // Find the matching POI to get the wikiName if available
      const matchingPoi = poiMarkers.find(poi =>
        Math.abs(poi.lat - lat) < 0.0001 &&
        Math.abs(poi.lng - lng) < 0.0001 &&
        poi.name === name
      );
      const wikiName = matchingPoi ? matchingPoi.wikiName : null;

      // Check if this POI is already in the user's list
      const existingUserMarker = userMarkers.find(m =>
        Math.abs(m.lat - lat) < 0.0001 &&
        Math.abs(m.lng - lng) < 0.0001 &&
        m.name === name
      );

      if (existingUserMarker) {
        // If it's already in the user's list, just open its popup
        if (existingUserMarker.marker) {
          map.setView([existingUserMarker.lat, existingUserMarker.lng], 16);
          existingUserMarker.marker.openPopup();
        }
        return true; // Already added, no need to continue
      }

      // Check if there's already a marker at this location on the map
      let existingMapMarker = null;

      // First check regular map layers
      map.eachLayer(layer => {
        if (layer instanceof L.Marker) {
          const markerLatLng = layer.getLatLng();
          if (Math.abs(markerLatLng.lat - lat) < 0.0001 && Math.abs(markerLatLng.lng - lng) < 0.0001) {
            existingMapMarker = layer;
          }
        }
      });

      // Then check marker cluster if it exists
      if (!existingMapMarker && markerCluster) {
        const clusterLayers = markerCluster.getLayers();
        for (let i = 0; i < clusterLayers.length; i++) {
          const layer = clusterLayers[i];
          const markerLatLng = layer.getLatLng();
          if (Math.abs(markerLatLng.lat - lat) < 0.0001 && Math.abs(markerLatLng.lng - lng) < 0.0001) {
            existingMapMarker = layer;
            break;
          }
        }
      }

      let marker;

      if (existingMapMarker) {
        // Use the existing marker instead of creating a new one
        marker = existingMapMarker;
      } else {
        // Create icon for the marker
        const icon = L.icon({
          iconUrl: 'images/poiMarker.svg',
          iconSize: [20, 32],
          iconAnchor: [10, 32],
          popupAnchor: POPUP_OFFSET  // Use the constant
        });

        // Create a marker object but don't add it to the map yet
        marker = L.marker([lat, lng], { icon });

        // If it's a POI, add it to the cluster group
        if (matchingPoi) {
          markerCluster.addLayer(marker);
        } else {
          // Otherwise add directly to the map
          marker.addTo(map);
        }
      }

      // Create popup content - for POI markers, we now show a delete button
      const popupContent = '<div style="text-align: center;">' +
        '<b style="color: #ffd700;">' + name + '</b><br />' +
        '<span style="color: #ffd700;">' + description + '</span><br />' +
        '<div style="display: flex; justify-content: space-between; margin-top: 10px;">' +
        '<button onclick="removeUserMarker(' + lat + ', ' + lng + ')" style="background-color: #262626; color: #ffd700; border: none; cursor: pointer;">Delete</button>' +
        '<a href="https://en.wikipedia.org/wiki/' + encodeURIComponent(wikiName || name) + '" target="_blank" style="color: #ffd700;">Wiki</a>' +
        '</div>' +
        '</div>';

      // Bind popup directly to marker
      marker.bindPopup(popupContent, {
        className: 'custom-popup',
        offset: POPUP_OFFSET  // Offset to position popup above the marker
      }).openPopup();

      // Add to userMarkers array
      userMarkers.push({
        lat,
        lng,
        name,
        description,
        marker,
        isPOI: true,       // This is a POI marker
        isCustom: false,   // This is not a custom marker
        wikiName: wikiName
      });

      // Save to Supabase
      await saveUserMarkers();

      // Update UI
      updatePlacesList();

      // Update the popup to show a delete button instead of add button
      if (marker._popup && marker._popup.isOpen()) {
        marker.setPopupContent(`
          <div style="text-align: center;">
            <b style="color: #ffd700;">${name}</b><br />
            <span style="color: #ffd700;">${description}</span><br />
            <div style="display: flex; justify-content: space-between; margin-top: 10px;">
              <button onclick="removeUserMarker(${lat}, ${lng})" style="background-color: #262626; color: #ffd700; border: none; cursor: pointer;">Delete</button>
              <a href="https://en.wikipedia.org/wiki/${encodeURIComponent(wikiName || name)}" target="_blank" style="color: #ffd700;">Wiki</a>
            </div>
          </div>
        `);
      }

      // Update all POI markers with the same coordinates
      updatePOIMarkers(lat, lng, true);

      // No need to update other markers as they're now directly on the map
      return true;
    } catch (error) {
      console.error('Error in addToUserListDirect:', error, '\nInputs:', { lat, lng, name, description });
      alert(`Error: ${error.message || 'Unknown error adding location to your list'}. Please try again.`);
      return false;
    }
  };

  // Keep the original function for backward compatibility
  async function addToUserList(lat, lng, name, description) {
    await window.addToUserListDirect(lat, lng, name, description);
  }

  // Completely rewritten function to remove a user marker
  async function removeUserMarker(lat, lng) {
    console.log(`Removing marker at ${lat}, ${lng}`);

    try {
      // First, find the marker in the userMarkers array
      const markerIndex = userMarkers.findIndex(marker =>
        Math.abs(marker.lat - lat) < 0.0001 && Math.abs(marker.lng - lng) < 0.0001
      );

      if (markerIndex === -1) {
        console.error(`No marker found at ${lat}, ${lng}`);
        return;
      }


      const marker = userMarkers[markerIndex];
      console.log(`Found marker to remove:`, marker);

      // 1. Remove from userMarkers array
      userMarkers.splice(markerIndex, 1);
      console.log(`Removed marker from userMarkers array. New length: ${userMarkers.length}`);

      // 2. If it's a POI marker, update its popup but keep it on the map
      if (marker.isPOI) {
        console.log(`This is a POI marker: ${marker.name}`);

        // Find the matching POI in the original data
        const matchingPoi = poiMarkers.find(poi =>
          Math.abs(poi.lat - lat) < 0.0001 &&
          Math.abs(poi.lng - lng) < 0.0001
        );

        if (matchingPoi) {
          console.log(`Found matching POI in original data: ${matchingPoi.name}`);

          // Update all POI markers with the same coordinates
          updatePOIMarkers(lat, lng, false);

          // Find the marker in the cluster group
          if (markerCluster) {
            markerCluster.getLayers().forEach(layer => {
              const layerLatLng = layer.getLatLng();
              if (Math.abs(layerLatLng.lat - lat) < 0.0001 && Math.abs(layerLatLng.lng - lng) < 0.0001) {
                // Update popup to show "Add to My List" button
                const popupContent = `
                  <div style="text-align: center;">
                    <b style="color: #ffd700;">${matchingPoi.name}</b><br />
                    <span style="color: #ffd700;">${matchingPoi.description}</span><br />
                    <div style="display: flex; justify-content: space-between; margin-top: 10px;">
                      <button onclick="addToUserList(${lat}, ${lng}, '${matchingPoi.name.replace(/'/g, "\\'")}', '${matchingPoi.description.replace(/'/g, "\\'")}')"
                              style="background-color: #262626; color: #ffd700; border: none; cursor: pointer;">Add to My List</button>
                      <a href="https://en.wikipedia.org/wiki/${encodeURIComponent(matchingPoi.wikiName || matchingPoi.name)}" target="_blank" style="color: #ffd700;">Wiki</a>
                    </div>
                  </div>
                `;
                layer.bindPopup(popupContent, { className: 'custom-popup', offset: POPUP_OFFSET });
                layer.openPopup();
              }
            });
          }
        }
      }
      // 3. If it's a custom marker, remove it from the map
      else if (marker.marker) {
        console.log(`Removing custom marker from map: ${marker.name}`);
        map.removeLayer(marker.marker);
      }

      // 4. If the marker was added to the itinerary, also delete the corresponding events
      if (marker.addedToItinerary) {
        console.log(`Marker was added to itinerary, removing related events: ${marker.name}`);
        const events = JSON.parse(localStorage.getItem('events')) || [];
        const filteredEvents = events.filter(event => event.location !== marker.name);
        localStorage.setItem('events', JSON.stringify(filteredEvents));
      }

      // 5. If user is logged in, delete from Supabase
      if (auth && auth.isLoggedIn() && auth.user) {
        try {
          console.log(`Attempting to delete marker from Supabase for user ${auth.user.id}: ${marker.name}`);
          
          // Try to find the marker in Supabase by coordinates and name
          try {
            const response = await window.mapAPI.getUserMarkers();
            if (Array.isArray(response)) {
              // Find a marker with matching coordinates and name (case insensitive)
              const matchingMarker = response.find(m => {
                const coordsMatch = Math.abs(m.lat - lat) < 0.0001 && Math.abs(m.lng - lng) < 0.0001;
                const nameMatch = m.name.toLowerCase() === marker.name.toLowerCase();
                return coordsMatch && nameMatch;
              });
              
              if (matchingMarker) {
                console.log('Found matching marker in database:', matchingMarker);
                // Try to delete using the marker's ID
                if (matchingMarker.id) {
                  console.log(`Deleting marker with ID: ${matchingMarker.id}`);
                  const deleteResult = await window.mapAPI.deleteUserMarker(auth.user.id, matchingMarker.id);
                  console.log('Marker deleted from Supabase:', deleteResult);
                } else if (matchingMarker.marker_id) {
                  // Try with marker_id if id is not available
                  console.log(`Deleting marker with marker_id: ${matchingMarker.marker_id}`);
                  const deleteResult = await window.mapAPI.deleteUserMarker(auth.user.id, matchingMarker.marker_id);
                  console.log('Marker deleted from Supabase using marker_id:', deleteResult);
                }
              } else {
                console.warn('No matching marker found in database, nothing to delete from Supabase');
              }
            }
          } catch (searchError) {
            console.error('Error searching for marker in Supabase:', searchError);
          }
        } catch (error) {
          console.error('Error deleting marker from Supabase:', error);
          // Continue with local cleanup even if Supabase deletion fails
        }
      }

      // 6. Clean up local storage and track deleted markers
      try {
        console.log('Cleaning up local storage directly...');
        const storedMarkers = JSON.parse(localStorage.getItem('userMarkers') || '[]');
        let deletedMarkers = JSON.parse(localStorage.getItem('deletedMarkers') || '[]');

        // Create a marker info object for tracking deletions
        const markerInfo = {
          lat: marker.lat,
          lng: marker.lng,
          name: marker.name,
          deletedAt: new Date().toISOString()
        };

        // Add to deleted markers if not already there
        const isAlreadyDeleted = deletedMarkers.some(deleted => 
          Math.abs(deleted.lat - marker.lat) < 0.0001 && 
          Math.abs(deleted.lng - marker.lng) < 0.0001 &&
          deleted.name.toLowerCase() === marker.name.toLowerCase()
        );

        if (!isAlreadyDeleted) {
          deletedMarkers.push(markerInfo);
          // Keep only the most recent 100 deleted markers to prevent localStorage from growing too large
          if (deletedMarkers.length > 100) {
            deletedMarkers = deletedMarkers.slice(-100);
          }
          localStorage.setItem('deletedMarkers', JSON.stringify(deletedMarkers));
        }

        // Remove the marker from user markers
        const filteredMarkers = storedMarkers.filter(m => {
          // Check if this is the marker we want to remove
          const isSameLocation = Math.abs(m.lat - lat) < 0.0001 && Math.abs(m.lng - lng) < 0.0001;
          const isSameName = m.name === marker.name;

          // Keep the marker if it's not the one we're removing
          return !(isSameLocation && isSameName);
        });

        console.log(`Local storage direct cleanup: ${storedMarkers.length} -> ${filteredMarkers.length} markers`);
        localStorage.setItem('userMarkers', JSON.stringify(filteredMarkers));
      } catch (error) {
        console.error('Error cleaning up local storage directly:', error);
      }

      // 7. Save changes and update UI
      await saveUserMarkers();

      // 8. Run deduplication again to ensure clean state
      deduplicateUserMarkers();

      // 9. Update UI
      updatePlacesList();

      console.log('Marker removal complete');
    } catch (error) {
      console.error('Error in removeUserMarker:', error);
      alert('An error occurred while removing the marker. Please try again.');
    }
  }

  // filterLocations function removed as it's no longer needed

  // Removed event listeners for updateDropdowns


</script>

  </section>

  <!-- List of Places -->
  <section id="places">
    <h2 style="color: #861818; text-shadow: 1px 1px 3px rgba(0,0,0,0.2);">Saved Places</h2>
    <div style="position: relative;">
      <ul id="placesList" style="max-height: 500px; overflow-y: auto; padding: 10px; border-radius: 10px; box-shadow: inset 0 0 10px rgba(0,0,0,0.1); -webkit-overflow-scrolling: touch; scrollbar-width: thin; scrollbar-color: #861818 #f5f5f5;"></ul>
      <!-- Scroll indicator for mobile -->
      <div id="scrollIndicator" style="position: absolute; bottom: 10px; right: 10px; background-color: rgba(134, 24, 24, 0.7); color: #ffd700; padding: 5px 10px; border-radius: 20px; font-size: 12px; display: none; z-index: 100; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">
        <i class="fas fa-arrow-down" style="margin-right: 5px;"></i>Scroll for more
      </div>
    </div>
    <div style="margin-top: 15px; margin-bottom: 20px; text-align: left;">
      <button id="suggestPoiBtn" style="white-space: nowrap; padding: 10px 15px; background-color: #861818; color: #ffd700; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; box-shadow: 0 4px 8px rgba(0,0,0,0.2); display: inline-flex; align-items: center;">
        <i class="fas fa-lightbulb" style="margin-right: 8px;"></i> Suggest a Place of Interest
      </button>
    </div>
  </section>

  <!-- Footer -->
  <footer id="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-logo">
          <img src="images/vestigiaLogoBig.png" alt="Vestigia Logo">
          <p>Discover iconic cultural landmarks with ease</p>
        </div>
        <div class="footer-links">
          <div class="footer-column">
            <h4>Navigation</h4>
            <ul>
              <li><a href="index.html">Home</a></li>
              <li><a href="map.html">Map</a></li>
              <li><a href="itinerary.html">Itinerary</a></li>
              <li><a href="transportation.html">Transportation</a></li>
            </ul>
          </div>
          <div class="footer-column">
            <h4>Connect</h4>
            <div class="social-icons">
              <!-- <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a> -->
              <!-- <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a> -->
              <a href="https://www.instagram.com/vestigiapp/" class="social-icon" target="_blank"><i class="fab fa-instagram"></i></a>
              <a href="https://www.linkedin.com/in/daniel-romanov-32bb05200/" class="social-icon" target="_blank"><i class="fab fa-linkedin-in"></i></a>
            </div>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2025 Vestigia | All rights reserved.</p>
        <a href="#" id="back-to-top">Back to Top <i class="fa fa-chevron-up"></i></a>
      </div>
    </div>
  </footer>

  <script>
    // Back to top button functionality
    document.addEventListener('DOMContentLoaded', function() {
      const backToTopButton = document.getElementById('back-to-top');
      if (backToTopButton) {
        backToTopButton.addEventListener('click', (e) => {
          e.preventDefault();
          window.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        });
      }

      // Mobile scroll indicator functionality
      const placesList = document.getElementById('placesList');
      const scrollIndicator = document.getElementById('scrollIndicator');

      // Function to check if we're on a mobile device
      function isMobileDevice() {
        return (window.innerWidth <= 768) || ('ontouchstart' in window) || (navigator.maxTouchPoints > 0);
      }

      // Function to check if the list needs scrolling
      function checkIfScrollable() {
        if (placesList && scrollIndicator) {
          // Only show on mobile devices
          if (isMobileDevice()) {
            // Check if content is taller than container
            if (placesList.scrollHeight > placesList.clientHeight) {
              // Only show if not already scrolled to bottom
              if (placesList.scrollHeight - placesList.scrollTop > placesList.clientHeight + 20) {
                scrollIndicator.style.display = 'block';
              } else {
                scrollIndicator.style.display = 'none';
              }
            } else {
              scrollIndicator.style.display = 'none';
            }
          } else {
            scrollIndicator.style.display = 'none';
          }
        }
      }

      // Add scroll event listener to the places list
      if (placesList) {
        placesList.addEventListener('scroll', function() {
          // Hide indicator when user scrolls near the bottom
          if (placesList.scrollHeight - placesList.scrollTop <= placesList.clientHeight + 20) {
            if (scrollIndicator) scrollIndicator.style.display = 'none';
          }
        });

        // Check on window resize
        window.addEventListener('resize', checkIfScrollable);

        // Initial check after places list is populated
        // We need to wait for the updatePlacesList function to complete
        setTimeout(checkIfScrollable, 500);

        // Also check when the observer detects changes to the list
        const placesListObserver = new MutationObserver(function(mutations) {
          checkIfScrollable();
        });

        placesListObserver.observe(placesList, { childList: true, subtree: true });
      }

      // Show/hide back to top button based on scroll position
      window.addEventListener('scroll', () => {
        if (backToTopButton) {
          if (window.scrollY > 300) {
            backToTopButton.style.opacity = '1';
          } else {
            backToTopButton.style.opacity = '0';
          }
        }
      });

      // Suggest POI button functionality
      const suggestPoiBtn = document.getElementById('suggestPoiBtn');
      if (suggestPoiBtn) {
        suggestPoiBtn.addEventListener('click', function() {
          // Create a modal for the user to input POI details
          const modalHtml = `
            <div id="poiSuggestionModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.7); z-index: 10000; display: flex; justify-content: center; align-items: center; padding-top: 50px;">
              <div style="background-color: #262626; padding: 20px; border-radius: 10px; width: 85%; max-width: 350px; border: 2px solid #861818;">
                <h3 style="color: #ffd700; text-align: center; margin-bottom: 20px;">Suggest a Place of Interest</h3>
                <form id="poiSuggestionForm" style="display: flex; flex-direction: column; gap: 15px;">
                  <div style="display: flex; flex-direction: column;">
                    <label for="userName" style="color: #ffd700; margin-bottom: 5px;">Your Name:</label>
                    <input type="text" id="userName" required style="padding: 8px; border-radius: 5px; border: 1px solid #861818;">
                  </div>
                  <div style="display: flex; flex-direction: column;">
                    <label for="userEmail" style="color: #ffd700; margin-bottom: 5px;">Your Email:</label>
                    <input type="email" id="userEmail" required style="padding: 8px; border-radius: 5px; border: 1px solid #861818;">
                  </div>
                  <div style="display: flex; flex-direction: column;">
                    <label for="poiName" style="color: #ffd700; margin-bottom: 5px;">Name of Place:</label>
                    <input type="text" id="poiName" required style="padding: 8px; border-radius: 5px; border: 1px solid #861818;">
                  </div>
                  <div style="display: flex; flex-direction: column;">
                    <label for="poiLocation" style="color: #ffd700; margin-bottom: 5px;">City, Country:</label>
                    <input type="text" id="poiLocation" required placeholder="e.g., Paris, France" style="padding: 8px; border-radius: 5px; border: 1px solid #861818;">
                  </div>
                  <div style="display: flex; justify-content: space-between; margin-top: 10px;">
                    <button type="button" id="cancelPoiSuggestion" style="padding: 10px 15px; background-color: #444; color: white; border: none; border-radius: 5px; cursor: pointer;">Cancel</button>
                    <button type="submit" style="padding: 10px 15px; background-color: #861818; color: #ffd700; border: none; border-radius: 5px; cursor: pointer; display: flex; align-items: center; justify-content: center;">
                      <span>Submit Suggestion</span>
                    </button>
                  </div>
                </form>
              </div>
            </div>
          `;

          // Add the modal to the body
          document.body.insertAdjacentHTML('beforeend', modalHtml);

          // Get references to the modal elements
          const modal = document.getElementById('poiSuggestionModal');
          const form = document.getElementById('poiSuggestionForm');
          const cancelBtn = document.getElementById('cancelPoiSuggestion');

          // Function to close the modal
          function closeModal() {
            if (modal && modal.parentNode) {
              modal.parentNode.removeChild(modal);
            }
          }

          // Close modal when cancel button is clicked
          cancelBtn.addEventListener('click', closeModal);

          // Handle form submission
          form.addEventListener('submit', async function(e) {
            e.preventDefault();

            // Get form values
            const userName = document.getElementById('userName').value;
            const userEmail = document.getElementById('userEmail').value;
            const poiName = document.getElementById('poiName').value;
            const poiLocation = document.getElementById('poiLocation').value;

            // Validate email format
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(userEmail)) {
              alert('Please enter a valid email address.');
              return;
            }

            // Create the suggestion data
            const suggestion = {
              user_name: userName,
              user_email: userEmail,
              poi_name: poiName,
              poi_location: poiLocation
              // submission_date will be set by default on the server
            };

            try {
              // Show loading state
              const submitBtn = form.querySelector('button[type="submit"]');
              const originalBtnText = submitBtn.innerHTML;
              submitBtn.innerHTML = 'Submitting...';
              submitBtn.disabled = true;

              // Send the suggestion to Netlify Function instead of direct Supabase access
              try {
                // Use the Netlify Function to save the suggestion
                const data = await window.mapAPI.savePOISuggestion(suggestion);
                console.log('Suggestion saved via Netlify Function:', data);
              } catch (error) {
                console.error('Error saving suggestion via Netlify Function:', error);
                alert('There was an error submitting your suggestion. Please try again later.');
                submitBtn.innerHTML = originalBtnText;
                submitBtn.disabled = false;
                return;
              }

              // Also store in local storage as a backup
              const localSuggestion = {
                ...suggestion,
                date: new Date().toISOString()
              };
              const suggestions = JSON.parse(localStorage.getItem('poiSuggestions') || '[]');
              suggestions.push(localSuggestion);
              localStorage.setItem('poiSuggestions', JSON.stringify(suggestions));

              // Show success message and close modal
              alert('Thank you for your suggestion! It will be reviewed by our team.');
              closeModal();
            } catch (err) {
              console.error('Error in suggestion submission:', err);
              alert('There was an error submitting your suggestion. Please try again later.');
            }
          });
        });
      }
    });
  </script>

  <script src="js/mobile-menu.js"></script>
  <script src="js/map.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.14.0/Sortable.min.js"></script>

  <!-- Authentication initialization -->
  <script>
    // Function to update auth UI based on login state
    function updateMapAuthUI() {
      console.log('Updating map auth UI');

      // Check if auth object is available and use its methods
      let isLoggedIn = false;
      let previousLoginState = window.lastLoginState || false;
      let authToken = null;

      if (typeof auth !== 'undefined' && auth.user) {
        // Use auth object to determine login state
        isLoggedIn = true;
        console.log('User is logged in via auth object:', auth.user.email);
        
        // Get the session from the auth object and sync token to localStorage
        if (auth.session && auth.session.access_token) {
          authToken = auth.session.access_token;
          localStorage.setItem('sb-access-token', authToken);
          console.log('Synced access token to localStorage');
        }
      } else {
        // Fallback to localStorage check
        const sessionStr = localStorage.getItem('vestigia_session');
        const userStr = localStorage.getItem('vestigia_user');
        authToken = localStorage.getItem('sb-access-token');
        isLoggedIn = !!(sessionStr && userStr);
        console.log('User login state from localStorage:', isLoggedIn);
      }

      // Check if login state has changed
      if (isLoggedIn !== previousLoginState) {
        console.log('Login state changed from', previousLoginState, 'to', isLoggedIn);
        window.lastLoginState = isLoggedIn;

        // If login state changed, sync markers
        if (isLoggedIn) {
          console.log('User logged in, syncing markers with Supabase');
          // Reload markers to sync with Supabase
          loadUserMarkers();
        } else {
          console.log('User logged out, loading markers from local storage');
          // User logged out, just reload from local storage
          loadUserMarkers();
        }
      }

      // Get auth elements
      const loginItems = document.querySelectorAll('.auth-item .login-btn, .auth-item .signup-btn');
      const profileItems = document.querySelectorAll('.auth-item .profile-btn, .auth-item .logout-btn');

      // Get mobile menu items
      const mobileLoginItems = document.querySelectorAll('.mobile-links .login-btn, .mobile-links .signup-btn');
      const mobileProfileItems = document.querySelectorAll('.mobile-links .profile-btn, .mobile-links .logout-btn');

      if (isLoggedIn) {
        // Hide login/signup, show profile/logout
        loginItems.forEach(item => {
          const parent = item.closest('.auth-item');
          if (parent) parent.style.display = 'none';
        });

        profileItems.forEach(item => {
          const parent = item.closest('.auth-item');
          if (parent) parent.style.display = 'flex';
        });

        // Update mobile menu
        mobileLoginItems.forEach(item => {
          const parent = item.closest('li');
          if (parent) parent.style.display = 'none';
        });

        mobileProfileItems.forEach(item => {
          const parent = item.closest('li');
          if (parent) parent.style.display = 'block';
        });
      } else {
        // Show login/signup, hide profile/logout
        loginItems.forEach(item => {
          const parent = item.closest('.auth-item');
          if (parent) parent.style.display = 'flex';
        });

        profileItems.forEach(item => {
          const parent = item.closest('.auth-item');
          if (parent) parent.style.display = 'none';
        });

        // Update mobile menu
        mobileLoginItems.forEach(item => {
          const parent = item.closest('li');
          if (parent) parent.style.display = 'block';
        });

        mobileProfileItems.forEach(item => {
          const parent = item.closest('li');
          if (parent) parent.style.display = 'none';
        });
      }
    }

    // Initialize auth when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
      // Navbar is loaded by navbar-loader.js

      // Add event listener for logout button after a delay to ensure navbar is loaded
      setTimeout(() => {
        const logoutButtons = document.querySelectorAll('.logout-btn');
        logoutButtons.forEach(btn => {
          btn.addEventListener('click', function(e) {
            e.preventDefault();
            if (typeof auth !== 'undefined') {
              auth.logout();
              // Update UI after logout
              setTimeout(updateMapAuthUI, 100);
            }
          });
        });

        // Call updateMapAuthUI after auth is initialized
        if (typeof auth !== 'undefined') {
          updateMapAuthUI();
        }
      }, 500);
    });

    // Also initialize if DOM is already loaded
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
      // First check if auth is defined
      if (typeof auth !== 'undefined') {
        auth.init();
        setTimeout(updateMapAuthUI, 100);

        // Add event listener for logout button
        setTimeout(() => {
          const logoutButtons = document.querySelectorAll('.logout-btn');
          logoutButtons.forEach(btn => {
            btn.addEventListener('click', function(e) {
              e.preventDefault();
              if (typeof auth !== 'undefined') {
                auth.logout();
                // Update UI after logout
                setTimeout(updateMapAuthUI, 100);
              }
            });
          });
        }, 500);
      } else {
        console.error('Auth module not loaded properly');
      }
    }
  </script>
</body>
</html>