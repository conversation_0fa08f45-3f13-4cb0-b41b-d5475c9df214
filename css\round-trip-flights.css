/* Styles for round-trip flights */

/* Round-trip flight card */
.flight-card.round-trip {
    border-left: 3px solid #4CAF50;
}

/* Return flight divider */
.return-flight-divider {
    display: flex;
    align-items: center;
    margin: 15px 0;
    color: #4CAF50;
    font-weight: bold;
    position: relative;
}

.return-flight-divider::before,
.return-flight-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background-color: #ddd;
}

.return-flight-divider span {
    padding: 0 10px;
    background-color: #fff;
    position: relative;
}

/* Return flight section */
.flight-times.return-flight {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px dashed #eee;
}

/* Booking modal return flight divider */
.booking-return-divider {
    margin: 15px 0;
    padding: 5px 10px;
    background-color: #f5f5f5;
    border-left: 3px solid #4CAF50;
    font-weight: bold;
    color: #333;
}

/* Progress bar for flight search */
.search-progress {
    margin-top: 15px;
    width: 100%;
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background-color: #4CAF50;
    width: 0;
    transition: width 0.3s ease;
}
