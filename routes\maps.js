const express = require('express');
const axios = require('axios');
const router = express.Router();

/**
 * Get directions from OpenRouteService
 */
router.get('/directions', async (req, res) => {
  try {
    const { start, end, profile } = req.query;
    
    if (!start || !end) {
      return res.status(400).json({ error: 'Start and end parameters are required' });
    }

    // Parse coordinates
    const startCoords = start.split(',').map(Number);
    const endCoords = end.split(',').map(Number);
    
    // Validate coordinates
    if (startCoords.length !== 2 || endCoords.length !== 2 || 
        isNaN(startCoords[0]) || isNaN(startCoords[1]) || 
        isNaN(endCoords[0]) || isNaN(endCoords[1])) {
      return res.status(400).json({ error: 'Invalid coordinates format' });
    }

    // Build the request body
    const requestBody = {
      coordinates: [startCoords, endCoords],
      format: 'geojson'
    };

    // Make the API request to OpenRouteService
    const response = await axios.post(
      `https://api.openrouteservice.org/v2/directions/${profile || 'driving-car'}`,
      requestBody,
      {
        headers: {
          'Authorization': process.env.OPENROUTE_API_KEY,
          'Content-Type': 'application/json'
        }
      }
    );

    res.json(response.data);
  } catch (error) {
    console.error('Error getting directions:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({ 
      error: 'Failed to get directions',
      details: error.response?.data || error.message
    });
  }
});

/**
 * Search for places using OpenRouteService
 */
router.get('/places', async (req, res) => {
  try {
    const { text, focus, radius } = req.query;
    
    if (!text) {
      return res.status(400).json({ error: 'Text parameter is required' });
    }

    // Build the query parameters
    const params = { text };
    
    if (focus) {
      params.focus = focus;
    }
    
    if (radius) {
      params.radius = radius;
    }

    // Make the API request to OpenRouteService
    const response = await axios.get(
      'https://api.openrouteservice.org/geocode/search',
      {
        params,
        headers: {
          'Authorization': process.env.OPENROUTE_API_KEY
        }
      }
    );

    res.json(response.data);
  } catch (error) {
    console.error('Error searching places:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({ 
      error: 'Failed to search places',
      details: error.response?.data || error.message
    });
  }
});

module.exports = router;
