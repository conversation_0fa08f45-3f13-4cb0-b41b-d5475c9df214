/* Flight Card Fixes */

.flight-details {
    display: flex;
    flex-direction: column;
    padding: 15px;
}

.flight-times {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.departure,
.arrival {
    text-align: center;
    flex: 1;
    min-width: 0;
    /* Allows flex items to shrink below content size */
    max-width: 120px;
    /* Limit maximum width */
    width: 120px;
    /* Set a fixed width */
}

.flight-duration {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 2;
    min-width: 0;
    /* Allows flex items to shrink below content size */
    flex-grow: 1;
    /* Allow this to take up remaining space */
}

.duration-line {
    position: relative;
    width: 100%;
    height: 2px;
    background-color: #555;
    margin: 10px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dot {
    width: 8px;
    height: 8px;
    background-color: var(--highlight-color);
    border-radius: 50%;
}

.line {
    flex: 1;
    height: 2px;
    background-color: #555;
    margin: 0 5px;
}

.duration-text {
    font-weight: 600;
    color: #ddd;
    font-size: 14px;
}

.stops-text {
    font-size: 12px;
    color: #aaa;
}

.time {
    font-size: 18px;
    font-weight: 700;
    color: #fff;
}

.airport {
    font-size: 14px;
    color: #aaa;
    margin-top: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.flight-date {
    font-size: 12px;
    color: #ffcc00;
    margin-top: 4px;
    font-weight: 600;
}