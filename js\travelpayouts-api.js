/**
 * Travelpayouts API Client
 * 
 * This module provides a client for interacting with the Travelpayouts (Aviasales) API
 * through Netlify Functions for secure API key handling.
 */

class TravelpayoutsAPI {
    constructor() {
        this.netlifyFunctionUrl = '/.netlify/functions/travelpayouts-search';
        this.searchTimeout = 60000; // 60 seconds
        this.pollingInterval = 2000; // 2 seconds
    }

    /**
     * Search for flights using the Travelpayouts API
     * @param {Object} params - Search parameters
     * @param {Function} progressCallback - Callback for search progress updates
     * @returns {Promise<Object>} Search results
     */
    async searchFlights(params, progressCallback) {
        try {
            // Validate required parameters
            if (!params.origin || !params.destination || !params.departureDate) {
                throw new Error('Missing required parameters: origin, destination, departureDate');
            }

            // Format search parameters
            const searchParams = this.formatSearchParams(params);

            // Start the search
            progressCallback?.({ status: 'initializing', message: 'Initializing search...', progress: 10 });
            const searchResponse = await this.startSearch(searchParams);

            if (!searchResponse.search_id) {
                throw new Error('Failed to initialize search');
            }

            // Poll for results
            progressCallback?.({ status: 'searching', message: 'Searching for flights...', progress: 30 });
            const results = await this.pollSearchResults(searchResponse.search_id, progressCallback);

            return results;
        } catch (error) {
            console.error('Flight search error:', error);
            progressCallback?.({ status: 'error', message: error.message, progress: 0 });
            throw error;
        }
    }

    /**
     * Format search parameters according to API requirements
     * @param {Object} params - Raw search parameters
     * @returns {Object} Formatted parameters
     */
    formatSearchParams(params) {
        // Extract IATA codes from full airport names
        const extractIATACode = (airportString) => {
            const match = airportString.match(/^([A-Z]{3})/);
            return match ? match[1] : null;
        };

        const originCode = extractIATACode(params.origin);
        const destinationCode = extractIATACode(params.destination);

        if (!originCode || !destinationCode) {
            throw new Error('Invalid airport codes');
        }

        // Create segments array
        const segments = [{
            origin: originCode,
            destination: destinationCode,
            date: params.departureDate
        }];

        // Add return segment for round trips
        if (params.returnDate) {
            segments.push({
                origin: destinationCode,
                destination: originCode,
                date: params.returnDate
            });
        }

        return {
            segments,
            passengers: {
                adults: parseInt(params.adults || 1, 10),
                children: parseInt(params.children || 0, 10),
                infants: parseInt(params.infants || 0, 10)
            },
            trip_class: params.tripClass || 'Y',
            locale: params.locale || 'en',
            user_ip: params.user_ip || '127.0.0.1'
        };
    }

    /**
     * Start a new flight search
     * @param {Object} params - Search parameters
     * @returns {Promise<Object>} Search initialization response
     */
    async startSearch(params) {
        const response = await fetch(this.netlifyFunctionUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'startSearch',
                params
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Search initialization failed: ${errorData.error || response.statusText}`);
        }

        return await response.json();
    }

    /**
     * Poll for search results
     * @param {string} searchId - Search ID
     * @param {Function} progressCallback - Callback for search progress updates
     * @returns {Promise<Object>} Search results
     */
    async pollSearchResults(searchId, progressCallback) {
        const startTime = Date.now();
        let pollCount = 0;

        while (Date.now() - startTime < this.searchTimeout) {
            pollCount++;

            // Calculate progress between 30% and 90%
            const elapsedPercent = Math.min(60, ((Date.now() - startTime) / this.searchTimeout) * 60);
            progressCallback?.({
                status: 'searching',
                message: `Searching for flights (attempt ${pollCount})...`,
                progress: 30 + elapsedPercent
            });

            const response = await fetch(this.netlifyFunctionUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'getResults',
                    searchId
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Failed to fetch search results: ${errorData.error || response.statusText}`);
            }

            const data = await response.json();

            // Check if we have only the search_id in the response
            if (data.length === 1 && data[0].search_id) {
                // Wait before next poll
                await new Promise(resolve => setTimeout(resolve, this.pollingInterval));
                continue;
            }

            // Process the results
            const flightData = data[0] || {};
            progressCallback?.({
                status: 'complete',
                message: `Found ${flightData.proposals?.length || 0} flights`,
                progress: 100
            });

            return {
                status: 'complete',
                search_id: searchId,
                data: flightData.proposals || [],
                currency: flightData.currency,
                currency_rates: flightData.currency_rates,
                airlines: flightData.airlines,
                airports: flightData.airports,
                gates_info: flightData.gates_info
            };
        }

        throw new Error('Search timed out');
    }

    /**
     * Get airline information
     * @returns {Promise<Object>} Airline data
     */
    async getAirlines() {
        const response = await fetch(this.netlifyFunctionUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'getAirlines'
            })
        });

        if (!response.ok) {
            throw new Error(`Failed to fetch airlines: ${response.statusText}`);
        }

        return await response.json();
    }

    /**
     * Get airport information
     * @returns {Promise<Object>} Airport data
     */
    async getAirports() {
        const response = await fetch(this.netlifyFunctionUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'getAirports'
            })
        });

        if (!response.ok) {
            throw new Error(`Failed to fetch airports: ${response.statusText}`);
        }

        return await response.json();
    }
}

// Create and export the API client instance
window.travelpayoutsAPI = new TravelpayoutsAPI(); 