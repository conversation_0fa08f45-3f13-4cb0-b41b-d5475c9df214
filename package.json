{"name": "vestigia-website", "version": "1.0.0", "description": "Vestigia travel website with Amadeus API integration", "main": "index.js", "scripts": {"build": "webpack --config webpack.config.js", "watch": "webpack --watch --config webpack.config.js", "start": "webpack serve --open"}, "dependencies": {"@supabase/supabase-js": "^2.38.4", "axios": "^1.9.0", "cors": "^2.8.5", "docker": "^1.0.0", "dotenv": "^16.5.0", "express": "^5.1.0", "netlify": "^22.1.3", "node-fetch": "^3.3.2"}, "devDependencies": {"@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "babel-loader": "^9.1.3", "dotenv-webpack": "^8.0.1", "webpack": "^5.88.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}}