<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Vestigia</title>
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Playfair+Display:wght@700;900&display=swap">
    <link rel="icon" type="image/png" sizes="96x96" href="favicon/favicon-96x96.png">
    <link rel="icon" href="favicon/favicon.ico">
    <style>
        /* Add smooth scrolling and box sizing */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        html {
            scroll-behavior: smooth;
        }
        
        body {
            overflow-x: hidden;
        }
    </style>
</head>
<body class="auth-page">
    <!-- Navbar will be injected here by navbar-loader.js -->
    <div id="navbar-container"></div>
    <div class="auth-overlay"></div>

    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header" style="background: transparent;">
                <img src="images/vestigiaLogoBig.png" alt="Vestigia Logo" class="auth-logo" style="background: #262626; padding: 10px; border-radius: 8px;">
                <h1>Create Account</h1>
                <p>Join Vestigia to explore the world's cultural heritage</p>
            </div>
            
            <form id="signupForm" class="auth-form">
                <div id="error-message" class="error-message"></div>
                <div class="auth-form-content">
                    <div class="auth-form-column">
                        <div class="form-group">
                            <label for="fullName">Full Name</label>
                            <div class="input-with-icon">
                                <i class="fas fa-user"></i>
                                <input type="text" id="fullName" name="fullName" placeholder="Enter your full name" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email</label>
                            <div class="input-with-icon">
                                <i class="fas fa-envelope"></i>
                                <input type="email" id="email" name="email" placeholder="Enter your email" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="password">Password</label>
                            <div class="input-with-icon">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="password" name="password" placeholder="Create a password" required>
                            </div>
                            <div class="password-strength">
                                <div class="strength-meter">
                                    <div class="strength-segment"></div>
                                    <div class="strength-segment"></div>
                                    <div class="strength-segment"></div>
                                    <div class="strength-segment"></div>
                                </div>
                                <div id="strength-text">Password strength</div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirmPassword">Confirm Password</label>
                            <div class="input-with-icon">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="confirmPassword" name="confirmPassword" placeholder="Confirm your password" required>
                            </div>
                            <div id="password-match" class="error-message" style="display: none;">
                                Passwords do not match
                            </div>
                        </div>
                        
                        <div class="form-group terms">
                            <input type="checkbox" id="terms" name="terms" required>
                            <label for="terms">I agree to the <a href="terms.html" target="_blank">Terms of Service</a> and <a href="privacy.html" target="_blank">Privacy Policy</a></label>
                        </div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary btn-block">Create Account</button>
                </div>
                
                <div class="divider">
                    <span>or sign up with</span>
                </div>
                
                <div class="social-login">
                    <a href="#" class="social-btn btn-google">
                        <i class="fab fa-google"></i>
                        Continue with Google
                    </a>
                    <a href="#" class="social-btn btn-facebook">
                        <i class="fab fa-facebook-f"></i>
                        Continue with Facebook
                    </a>
                </div>
                
                <p class="auth-footer">
                    Already have an account? <a href="auth-login.html">Sign in</a>
                </p>
            </form>
        </div>
    </div>

    <script src="js/navbar-loader.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Add any additional scripts here
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize social login buttons
            document.querySelectorAll('.social-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const provider = this.classList.contains('btn-google') ? 'google' : 'facebook';
                    // Handle social login
                    console.log(`Signing up with ${provider}`);
                    // You would typically redirect to your OAuth endpoint here
                    // window.location.href = `/auth/${provider}?signup=true`;
                });
            });
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const signupForm = document.getElementById('signupForm');
            const errorMessage = document.getElementById('error-message');
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirmPassword');
            const togglePassword = document.getElementById('togglePassword');
            const toggleConfirmPassword = document.getElementById('toggleConfirmPassword');
            const strengthSegments = document.querySelectorAll('.strength-segment');
            const strengthText = document.getElementById('strength-text');

            // Toggle password visibility
            function togglePasswordVisibility(input, icon) {
                if (icon) {
                    icon.addEventListener('click', function() {
                        const type = input.type === 'password' ? 'text' : 'password';
                        input.type = type;
                        this.classList.toggle('fa-eye');
                        this.classList.toggle('fa-eye-slash');
                    });
                }
            }


            // Check password strength
            function checkPasswordStrength(password) {
                let strength = 0;
                const hasLower = /[a-z]/.test(password);
                const hasUpper = /[A-Z]/.test(password);
                const hasNumber = /\d/.test(password);
                const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);
                const isLongEnough = password.length >= 8;
                
                if (hasLower) strength++;
                if (hasUpper) strength++;
                if (hasNumber) strength++;
                if (hasSpecial) strength++;
                if (isLongEnough) strength++;
                
                // Normalize to 4 levels
                strength = Math.ceil((strength / 5) * 4);
                
                return Math.min(strength, 4);
            }


            // Update password strength meter
            function updateStrengthMeter(password) {
                const strength = checkPasswordStrength(password);
                const colors = ['#ff4d4d', '#ff8c66', '#ffcc00', '#66cc66'];
                const messages = ['Very Weak', 'Weak', 'Good', 'Strong'];
                
                strengthSegments.forEach((segment, index) => {
                    if (index < strength) {
                        segment.style.backgroundColor = colors[strength - 1];
                        segment.style.flex = '1';
                    } else {
                        segment.style.backgroundColor = '#e0e0e0';
                        segment.style.flex = '0.2';
                    }
                });
                
                if (password.length === 0) {
                    strengthText.textContent = 'Password strength';
                } else {
                    strengthText.textContent = messages[strength - 1] || '';
                    strengthText.style.color = colors[strength - 1] || '';
                }
            }


            // Initialize event listeners
            if (passwordInput) {
                passwordInput.addEventListener('input', (e) => {
                    updateStrengthMeter(e.target.value);
                });
            }

            togglePasswordVisibility(passwordInput, togglePassword);
            togglePasswordVisibility(confirmPasswordInput, toggleConfirmPassword);

            // Handle form submission
            if (signupForm) {
                signupForm.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    
                    const fullName = document.getElementById('fullName').value;
                    const email = document.getElementById('email').value;
                    const password = passwordInput.value;
                    const confirmPassword = confirmPasswordInput.value;
                    
                    // Basic client-side validation
                    if (password !== confirmPassword) {
                        errorMessage.textContent = 'Passwords do not match';
                        errorMessage.style.display = 'block';
                        return;
                    }
                    
                    if (password.length < 8) {
                        errorMessage.textContent = 'Password must be at least 8 characters long';
                        errorMessage.style.display = 'block';
                        return;
                    }
                    
                    try {
                        const result = await auth.signup(email, password, fullName);
                        if (result.success) {
                            // Redirect to login page with success message
                            window.location.href = 'auth-login.html?signup=success';
                        } else {
                            errorMessage.textContent = result.message || 'Signup failed. Please try again.';
                            errorMessage.style.display = 'block';
                        }
                    } catch (error) {
                        console.error('Signup error:', error);
                        errorMessage.textContent = 'An error occurred. Please try again.';
                        errorMessage.style.display = 'block';
                    }
                });
            }
        });
    </script>
</body>
</html>
