/* Flight Search Form Responsive Layout */

/* Base form layout */
#transportForm {
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 100%;
}

/* Form rows container */
.form-rows-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 100%;
}

/* Form row styling */
.form-row {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    align-items: flex-end;
    width: 100%;
}

/* Form group base styles */
.form-group {
    flex: 1;
    min-width: 150px;
    margin-bottom: 0;
    width: 100%;
}

/* Trip type specific styles */
.trip-type {
    max-width: 100%;
    margin-bottom: 0;
    width: 100%;
    display: flex;
    justify-content: center;
}

/* Location fields container */
.locations {
    display: flex;
    gap: 15px;
    width: 100%;
    align-items: flex-end;
}

/* Individual location field */
.location-field {
    flex: 1;
    min-width: 0;
    position: relative;
}

/* Date fields container */
.dates {
    display: flex;
    gap: 15px;
    width: 100%;
}

/* Individual date field */
.date-field {
    flex: 1;
    min-width: 150px;
}

/* Search button */
.search-btn-container {
    margin-top: 10px;
    width: 100%;
}

.search-btn {
    width: 100%;
    padding: 12px;
    font-size: 16px;
    font-weight: 600;
}

/* Switch button */
.switch-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 5px;
}

/* Responsive adjustments */
@media (min-width: 992px) {

    /* Two-row layout for larger screens */
    .form-rows-container {
        flex-direction: column;
    }

    .form-row {
        display: flex;
        flex-wrap: nowrap;
    }

    .form-group.trip-type {
        flex: 0 0 auto;
        width: auto;
    }

    .form-group:not(.trip-type) {
        flex: 1;
    }

    .locations {
        flex-wrap: nowrap;
    }

    .dates {
        flex-wrap: nowrap;
    }
}

@media (max-width: 991px) {

    /* Stacked layout for tablets and mobile */
    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .form-group {
        width: 100%;
    }

    .locations {
        flex-direction: column;
        gap: 15px;
    }

    .location-field {
        width: 100%;
    }

    .dates {
        flex-direction: column;
        gap: 15px;
    }

    .date-field {
        width: 100%;
    }

    .trip-type {
        width: 100%;
        max-width: 100%;
        justify-content: center;
    }

    .search-btn {
        margin-top: 10px;
    }

    .switch-btn {
        margin: 10px 0;
    }
}

/* Adjust form controls for better mobile experience */
@media (max-width: 768px) {
    #transportForm {
        gap: 20px;
    }

    .form-row {
        gap: 15px;
    }

    /* Trip type row */
    .form-row:first-child {
        margin-bottom: 5px;
    }

    .trip-type {
        justify-content: center;
        margin-bottom: 0;
    }

    /* Locations row */
    .form-row:nth-child(2) {
        margin-bottom: 5px;
    }

    .locations {
        gap: 15px;
    }

    /* Dates row */
    .form-row:nth-child(3) {
        margin-bottom: 5px;
    }

    .dates {
        gap: 15px;
    }

    /* Passengers and search row */
    .form-row:last-child {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .form-group:last-child {
        margin-top: 5px;
    }

    .search-btn {
        margin-top: 0;
    }
}

@media (max-width: 480px) {
    #transportForm {
        gap: 15px;
    }

    .form-row {
        gap: 12px;
    }

    /* Trip type row */
    .form-row:first-child {
        margin-bottom: 3px;
    }

    /* Locations row */
    .form-row:nth-child(2) {
        margin-bottom: 3px;
    }

    .locations {
        gap: 12px;
    }

    /* Dates row */
    .form-row:nth-child(3) {
        margin-bottom: 3px;
    }

    .dates {
        gap: 12px;
    }

    /* Passengers and search row */
    .form-row:last-child {
        gap: 12px;
    }

    .form-group:last-child {
        margin-top: 3px;
    }
}

/* Adjust form controls for better mobile experience */
input,
select,
button {
    width: 100%;
    box-sizing: border-box;
    padding: 10px 12px;
    border-radius: 6px;
    border: 1px solid #444;
    background-color: #444;
    color: #fff;
    font-size: 14px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-size: 13px;
    color: #aaa;
}

/* Focus states for better accessibility */
input:focus,
select:focus,
button:focus {
    outline: 2px solid var(--highlight-color);
    outline-offset: 2px;
}

/* Disabled state styling */
input:disabled,
select:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Portrait mode specific adjustments */
@media (max-width: 768px) and (orientation: portrait) {
    #transportForm {
        gap: 15px;
    }

    .form-row {
        gap: 12px;
        margin-bottom: 0;
    }

    /* Trip type row */
    .form-row:first-child {
        margin-bottom: 0;
    }

    .trip-type {
        justify-content: center;
        margin-bottom: 0;
        padding: 5px;
    }

    /* Locations row */
    .form-row:nth-child(2) {
        margin-bottom: 0;
    }

    .locations {
        gap: 12px;
        flex-direction: column;
    }

    .location-field {
        width: 100%;
    }

    .switch-btn {
        margin: 0;
        align-self: center;
    }

    /* Dates row */
    .form-row:nth-child(3) {
        margin-bottom: 0;
    }

    .dates {
        gap: 12px;
        flex-direction: column;
    }

    .date-field {
        width: 100%;
    }

    /* Passengers and search row */
    .form-row:last-child {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-bottom: 0;
    }

    .form-group:last-child {
        margin-top: 0;
    }

    .search-btn {
        margin-top: 0;
        padding: 10px;
    }

    /* Adjust input fields for better touch targets */
    input[type="text"],
    input[type="time"],
    select,
    .passenger-selector {
        padding: 12px;
        font-size: 16px;
        /* Prevents iOS zoom on focus */
    }

    /* Adjust labels for better visibility */
    label {
        font-size: 14px;
        margin-bottom: 4px;
    }
}

@media (max-width: 480px) and (orientation: portrait) {
    #transportForm {
        gap: 12px;
    }

    .form-row {
        gap: 10px;
    }

    .trip-type {
        padding: 4px;
    }

    .locations,
    .dates {
        gap: 10px;
    }

    input[type="text"],
    input[type="time"],
    select,
    .passenger-selector {
        padding: 10px;
    }

    .search-btn {
        padding: 12px;
    }
}