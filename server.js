/**
 * Vestigia Backend Server
 * This server provides secure API endpoints for the Vestigia website,
 * protecting API keys from being exposed in the frontend code.
 */

// Load environment variables from .env file
require('dotenv').config();

const express = require('express');
const cors = require('cors');
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Import route handlers
const flightsRouter = require('./routes/flights');
const airportsRouter = require('./routes/airports');
const citiesRouter = require('./routes/cities');
const mapsRouter = require('./routes/maps');

// API routes
app.use('/api/flights', flightsRouter);
app.use('/api/airports', airportsRouter);
app.use('/api/cities', citiesRouter);
app.use('/api/maps', mapsRouter);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: 'Server is running' });
});

// Start the server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`API available at http://localhost:${PORT}/api`);
});
