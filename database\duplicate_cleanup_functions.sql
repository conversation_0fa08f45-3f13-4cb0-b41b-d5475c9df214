-- Function to find duplicate markers for a user
CREATE OR REPLACE FUNCTION find_duplicate_markers(p_user_id TEXT)
RETURNS TABLE (
  id INTEGER,
  lat FLOAT,
  lng FLOAT,
  name TEXT,
  created_at TIMESTAMPTZ,
  duplicate_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  WITH duplicates AS (
    SELECT 
      id,
      lat,
      lng,
      name,
      created_at,
      ROW_NUMBER() OVER (
        PARTITION BY 
          user_id, 
          ROUND(lat::numeric, 6), 
          ROUND(lng::numeric, 6),
          TRIM(LOWER(name))
        ORDER BY created_at DESC, id DESC
      ) as rn,
      COUNT(*) OVER (
        PARTITION BY 
          user_id, 
          ROUND(lat::numeric, 6), 
          ROUND(lng::numeric, 6),
          TRIM(LOWER(name))
      ) as count
    FROM user_markers
    WHERE user_id = p_user_id
  )
  SELECT 
    id,
    lat,
    lng,
    name,
    created_at,
    count - 1 as duplicate_count
  FROM duplicates
  WHERE rn > 1
  ORDER BY count DESC, created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to delete duplicate markers, keeping the most recent one
CREATE OR REPLACE FUNCTION delete_duplicate_markers(p_user_id TEXT)
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  WITH duplicates_to_delete AS (
    SELECT id
    FROM (
      SELECT 
        id,
        ROW_NUMBER() OVER (
          PARTITION BY 
            user_id, 
            ROUND(lat::numeric, 6), 
            ROUND(lng::numeric, 6),
            TRIM(LOWER(name))
          ORDER BY created_at DESC, id DESC
        ) as rn
      FROM user_markers
      WHERE user_id = p_user_id
    ) t
    WHERE rn > 1
  )
  DELETE FROM user_markers
  WHERE id IN (SELECT id FROM duplicates_to_delete)
  RETURNING 1 INTO deleted_count;
  
  RETURN COALESCE(deleted_count, 0);
END;
$$ LANGUAGE plpgsql;

-- Function to clean up all duplicates for all users (admin only)
CREATE OR REPLACE FUNCTION cleanup_all_duplicate_markers()
RETURNS JSONB AS $$
DECLARE
  user_record RECORD;
  total_deleted INTEGER := 0;
  result JSONB := '[]'::JSONB;
  user_result JSONB;
BEGIN
  -- Process each user's markers separately
  FOR user_record IN SELECT DISTINCT user_id FROM user_markers LOOP
    -- Delete duplicates for this user
    user_result := (
      SELECT jsonb_build_object(
        'user_id', user_record.user_id,
        'deleted', delete_duplicate_markers(user_record.user_id)
      )
    );
    
    -- Add to results
    result := result || user_result;
    total_deleted := total_deleted + (user_result->>'deleted')::INTEGER;
  END LOOP;
  
  RETURN jsonb_build_object(
    'total_duplicates_removed', total_deleted,
    'by_user', result
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
