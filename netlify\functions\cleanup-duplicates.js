const { createClient } = require('@supabase/supabase-js');

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS'
  };

  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  try {
    // Get user_id from query parameters
    const { user_id } = JSON.parse(event.body);
    
    if (!user_id) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing user_id parameter' })
      };
    }

    // Initialize Supabase client
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ error: 'Server configuration error' })
      };
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    // First, identify duplicate markers
    const { data: duplicates, error: findError } = await supabase.rpc('find_duplicate_markers', {
      p_user_id: user_id
    });

    if (findError) {
      console.error('Error finding duplicates:', findError);
      throw findError;
    }

    // If no duplicates found, return early
    if (!duplicates || duplicates.length === 0) {
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          message: 'No duplicates found',
          duplicates_removed: 0,
          duplicates: []
        })
      };
    }

    // Delete the duplicate markers, keeping the most recent one
    const { data: deleted, error: deleteError } = await supabase.rpc('delete_duplicate_markers', {
      p_user_id: user_id
    });

    if (deleteError) {
      console.error('Error deleting duplicates:', deleteError);
      throw deleteError;
    }

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        message: 'Duplicates cleaned up successfully',
        duplicates_removed: deleted,
        duplicates: duplicates
      })
    };

  } catch (error) {
    console.error('Error in cleanup-duplicates function:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Failed to clean up duplicates',
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      })
    };
  }
};
