-- SQL script to fix user_markers table
-- Run this directly in the Supabase SQL Editor

-- Create the table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_markers (
  id SERIAL PRIMARY KEY,
  user_id TEXT NOT NULL,
  lat FLOAT NOT NULL,
  lng FLOAT NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  placeName TEXT,
  isPOI BOOLEAN DEFAULT FALSE,
  isCustom BOOLEAN DEFAULT FALSE,
  wikiName TEXT,
  addedToItinerary BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add missing columns if they don't exist
DO $$
BEGIN
    -- Check if isPOI column exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'user_markers'
        AND column_name = 'ispoi'
    ) THEN
        -- Add the column
        ALTER TABLE user_markers ADD COLUMN "isPOI" BOOLEAN DEFAULT FALSE;
        RAISE NOTICE 'Added isPOI column';
    ELSE
        RAISE NOTICE 'isPOI column already exists';
    END IF;
    
    -- Check if isCustom column exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'user_markers'
        AND column_name = 'iscustom'
    ) THEN
        -- Add the column
        ALTER TABLE user_markers ADD COLUMN "isCustom" BOOLEAN DEFAULT FALSE;
        RAISE NOTICE 'Added isCustom column';
    ELSE
        RAISE NOTICE 'isCustom column already exists';
    END IF;
    
    -- Check if addedToItinerary column exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'user_markers'
        AND column_name = 'addedtoitinerary'
    ) THEN
        -- Add the column
        ALTER TABLE user_markers ADD COLUMN "addedToItinerary" BOOLEAN DEFAULT FALSE;
        RAISE NOTICE 'Added addedToItinerary column';
    ELSE
        RAISE NOTICE 'addedToItinerary column already exists';
    END IF;
    
    -- Check if wikiName column exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'user_markers'
        AND column_name = 'wikiname'
    ) THEN
        -- Add the column
        ALTER TABLE user_markers ADD COLUMN "wikiName" TEXT;
        RAISE NOTICE 'Added wikiName column';
    ELSE
        RAISE NOTICE 'wikiName column already exists';
    END IF;
END
$$;

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_user_markers_user_id ON user_markers(user_id);
CREATE INDEX IF NOT EXISTS idx_user_markers_location ON user_markers(lat, lng);

-- Enable Row Level Security
ALTER TABLE user_markers ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to select only their own markers
DROP POLICY IF EXISTS select_own_markers ON user_markers;
CREATE POLICY select_own_markers ON user_markers
  FOR SELECT USING (auth.uid()::text = user_id);

-- Create policy to allow users to insert only their own markers
DROP POLICY IF EXISTS insert_own_markers ON user_markers;
CREATE POLICY insert_own_markers ON user_markers
  FOR INSERT WITH CHECK (auth.uid()::text = user_id);

-- Create policy to allow users to update only their own markers
DROP POLICY IF EXISTS update_own_markers ON user_markers;
CREATE POLICY update_own_markers ON user_markers
  FOR UPDATE USING (auth.uid()::text = user_id);

-- Create policy to allow users to delete only their own markers
DROP POLICY IF EXISTS delete_own_markers ON user_markers;
CREATE POLICY delete_own_markers ON user_markers
  FOR DELETE USING (auth.uid()::text = user_id);

-- Create policy to allow service role to access all markers
DROP POLICY IF EXISTS service_role_access ON user_markers;
CREATE POLICY service_role_access ON user_markers
  FOR ALL USING (auth.role() = 'service_role');
