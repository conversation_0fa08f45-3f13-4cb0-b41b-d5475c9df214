/* Base Styles */
:root {
    --dark-color: #1a1a1a;
    --main-color: #861818;
    --highlight-color: #ffd700;
    --light-color: #f5f5f5;
    --text-light: #cccccc;
    --border-color: #444444;
    --error-color: #ff4d4d;
    --success-color: #4caf50;
    --bg-color: #121212;
    --card-bg: rgba(38, 38, 38, 0.9);
    --shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Auth Page Overlay */
.auth-page {
    position: relative;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.auth-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(26, 26, 26, 0.9) 0%, rgba(38, 38, 38, 0.8) 100%);
    z-index: -1;
}

body {
    font-family: 'Open Sans', sans-serif;
    background: url('../images/signupBackground.jpg') no-repeat center center fixed;
    background-size: cover;
    color: var(--light-color);
    line-height: 1.6;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    position: relative;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    text-decoration: underline;
}

/* Auth Container */
.auth-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: calc(100vh - 60px);
    padding: 0.5rem 1rem 1rem; /* Reduced top padding */
    position: relative;
    z-index: 1;
    margin-top: 40px; /* Reduced from 60px */
    box-sizing: border-box;
    overflow: hidden; /* Prevent double scrollbar */
}

.auth-card {
    background: rgba(38, 38, 38, 0.95);
    border-radius: 8px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid rgba(255, 215, 0, 0.1);
    backdrop-filter: blur(10px);
    display: flex;
    height: calc(100vh - 80px);
    max-height: 800px;
}

.auth-header {
    padding: 2rem 2.5rem;
    text-align: center;
    border-bottom: 1px solid rgba(255, 215, 0, 0.1);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    background: rgba(0, 0, 0, 0.2);
}

.auth-logo {
    width: 180px;
    height: auto;
    margin-bottom: 1rem;
    opacity: 0.9;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.auth-logo:hover {
    transform: scale(1.03);
    opacity: 1;
}

.auth-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, transparent, var(--highlight-color), transparent);
}

.auth-header h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--highlight-color);
    font-family: 'Playfair Display', serif;
    text-shadow: var(--text-shadow);
}

.auth-header p {
    color: var(--light-color);
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Auth Form */
.auth-form {
    padding: 2.5rem;
    display: flex;
    flex-direction: column;
    flex: 2;
    min-width: 0;
    overflow-y: auto;
    height: 100%;
    box-sizing: border-box;
    scrollbar-width: thin;
    scrollbar-color: var(--highlight-color) transparent;
}

/* Two-column layout for larger screens */
.auth-form-content {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.auth-form-column {
    flex: 1;
    min-width: 280px;
}

/* Forgot password link */
.forgot-password-container {
    text-align: right;
    margin-top: 0.5rem;
    margin-bottom: 1rem;
}

.forgot-password {
    font-size: 0.9rem;
    color: var(--highlight-color);
    text-decoration: none;
    transition: color 0.2s ease;
}

.forgot-password:hover {
    color: #ffea00;
    text-decoration: underline;
}

/* Custom scrollbar for WebKit browsers */
.auth-form::-webkit-scrollbar {
    width: 6px;
}

.auth-form::-webkit-scrollbar-track {
    background: transparent;
}

.auth-form::-webkit-scrollbar-thumb {
    background-color: var(--highlight-color);
    border-radius: 3px;
}

/* Make form content take available space */
.auth-form-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Form actions at the bottom */
.form-actions {
    margin-top: auto;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.form-actions .btn-primary {
    margin-bottom: 1rem;
}

.auth-footer {
    text-align: center;
    margin-top: 1rem;
    color: var(--text-light);
    font-size: 0.9375rem;
}

.auth-footer a {
    color: var(--highlight-color);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.2s ease;
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* Adjust form elements for better spacing */
.form-group {
    margin-bottom: 1.5rem; /* Slightly increased for better spacing */
}

.input-with-icon input {
    padding: 0.75rem 1rem 0.75rem 2.75rem; /* Slightly reduced padding */
    height: 44px; /* Slightly reduced height */
}

.input-with-icon i {
    left: 0.9rem; /* Adjusted icon position */
}

.toggle-password,
.toggle-confirm-password {
    right: 0.9rem; /* Adjusted icon position */
}

/* Adjust buttons */
.btn {
    padding: 0.75rem; /* Slightly reduced padding */
    margin-bottom: 0.75rem; /* Reduced margin */
}

/* Adjust social login buttons */
.social-login {
    margin-top: 1.25rem; /* Slightly reduced margin */
}

.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.input-with-icon {
    position: relative;
    display: flex;
    align-items: center;
}

.input-with-icon i {
    position: absolute;
    left: 1rem;
    color: var(--text-light);
    font-size: 1.1rem;
}

.input-with-icon input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 3rem;
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
    transition: var(--transition);
    height: 48px;
    color: var(--light-color);
}

.input-with-icon input:focus {
    border-color: var(--highlight-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.3);
    background-color: rgba(0, 0, 0, 0.5);
}

/* Password toggle buttons removed */

/* Password Strength */
.password-strength {
    margin-top: 0.5rem;
}

.strength-meter {
    display: flex;
    gap: 4px;
    margin-bottom: 0.25rem;
    height: 4px;
}

.strength-segment {
    flex: 1;
    background-color: #e0e0e0;
    border-radius: 2px;
    transition: var(--transition);
}

#strength-text {
    font-size: 0.75rem;
    color: var(--text-light);
    transition: var(--transition);
}

/* Error Message */
.error-message {
    background-color: rgba(255, 77, 77, 0.15);
    color: #ff9999;
    padding: 0.75rem 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    display: none;
    border-left: 3px solid var(--error-color);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    width: 100%;
    margin-bottom: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--main-color) 0%, #5a0f0f 100%);
    color: white;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    border: none;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--highlight-color) 0%, #ffed4a 100%);
    transition: all 0.6s ease;
    z-index: -1;
}

.btn-primary:hover {
    color: var(--dark-color);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.btn-primary:hover::before {
    left: 0;
}

.btn-google {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--light-color);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.btn-google:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: var(--highlight-color);
    color: var(--highlight-color);
}

.btn-google img {
    width: 18px;
    height: 18px;
    margin-right: 10px;
}

/* Divider */
.auth-divider {
    display: flex;
    align-items: center;
    text-align: center;
    margin: 1.5rem 0;
    color: var(--text-light);
    font-size: 0.875rem;
}

.auth-divider::before,
.auth-divider::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid var(--border-color);
}

.auth-divider:not(:empty)::before {
    margin-right: 1rem;
}

.auth-divider:not(:empty)::after {
    margin-left: 1rem;
}

/* Forgot Password */
.forgot-password-container {
    text-align: right;
    margin: 0.5rem 0 1rem;
}

.forgot-password {
    color: var(--main-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.forgot-password:hover {
    color: var(--highlight-color);
    text-decoration: underline;
}

/* Footer */
.auth-footer {
    text-align: center;
    margin-top: 1.5rem;
    color: var(--text-light);
    font-size: 0.9rem;
}

.auth-footer a {
    color: var(--highlight-color);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
}

.auth-footer a:hover {
    text-decoration: underline;
    color: #fff;
}

/* Checkbox Styles */
.remember-me,
.terms {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.remember-me input,
.terms input {
    margin-right: 0.75rem;
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.remember-me label,
.terms label {
    margin-bottom: 0;
    cursor: pointer;
    font-size: 0.9375rem;
}

.terms label a {
    text-decoration: underline;
}

/* Password Header */
.password-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.forgot-password {
    font-size: 0.875rem;
    color: var(--primary-color);
    text-decoration: none;
}

.forgot-password:hover {
    text-decoration: underline;
}

/* Social Login */
.social-login {
    margin-top: 1.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 12px;
}

.social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 16px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 0.9375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.social-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    text-decoration: none;
}

.social-btn:active {
    transform: translateY(0);
}

.social-btn i {
    font-size: 1.25rem;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
}

.social-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.social-btn i {
    position: relative;
    z-index: 1;
    transition: transform 0.3s ease;
}

.social-btn span {
    position: relative;
    z-index: 1;
}

.social-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.4s cubic-bezier(0.5, 1, 0.89, 1);
}

.social-btn:hover::before {
    transform: scaleX(1);
    transform-origin: left;
}

.btn-google {
    background-color: #fff;
    color: #3c4043;
    border: 1px solid #dadce0;
    box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.1);
}

.btn-google:hover {
    background-color: #f8f9fa;
    border-color: #d2e3fc;
    box-shadow: 0 1px 3px 1px rgba(66, 133, 244, 0.15);
}

.btn-google i {
    color: #4285f4;
}

.btn-facebook {
    background-color: #1877f2;
    color: #fff;
    border: 1px solid #166fe5;
    box-shadow: 0 1px 2px 0 rgba(24, 119, 242, 0.1);
}

.btn-facebook:hover {
    background-color: #166fe5;
    border-color: #1462c0;
    box-shadow: 0 1px 3px 1px rgba(24, 119, 242, 0.25);
}

.btn-facebook i {
    color: #fff;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .auth-card {
        max-width: 95%;
        margin: 1rem auto;
        animation: none;
        flex-direction: column;
        min-height: auto;
    }
    
    .auth-header {
        padding: 2rem 1.5rem;
        flex: none;
    }
    
    .auth-form {
        padding: 2rem 1.5rem;
        max-height: none;
    }
    
    .auth-logo {
        width: 150px;
    }
}

@media (max-width: 768px) {
    .auth-card {
        max-width: 100%;
        margin: 0;
        border-radius: 0;
        min-height: 100vh;
    }
    
    .auth-header {
        padding: 1.5rem 1.5rem 1rem;
    }
    
    .auth-form {
        padding: 1.5rem;
    }
    
    .btn {
        padding: 0.875rem 1rem;
    }
    
    .auth-container {
        padding: 1rem;
        margin-top: 60px;
    }
    
    .social-login {
        grid-template-columns: 1fr;
    }
    
    .terms {
        margin-top: 1.5rem;
        padding-top: 1.25rem;
    }
    
    .terms label {
        font-size: 0.9rem;
    }
}

/* Animation for form elements */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-form .form-group {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

/* Add delay to each form group */
.auth-form .form-group:nth-child(1) { animation-delay: 0.1s; }
.auth-form .form-group:nth-child(2) { animation-delay: 0.2s; }
.auth-form .form-group:nth-child(3) { animation-delay: 0.3s; }
.auth-form .form-group:nth-child(4) { animation-delay: 0.4s; }
.auth-form .form-group:nth-child(5) { animation-delay: 0.5s; }
