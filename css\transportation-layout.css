/* Transportation Page Layout */

/* Base Layout */
.transport-layout {
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    min-height: calc(100vh - 160px);
    gap: 20px;
}

/* Main content area */
.transport-main {
    flex: 1;
    max-width: 800px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    justify-content: space-between;
    /* Align content to match the bottom */
    padding-bottom: 20px;
    /* Ensure padding for alignment */
}

/* Search box */
.search-box {
    background-color: #333333;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--main-color);
    margin-bottom: 0;
}

/* Form layout */
#transportForm {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-rows-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 15px;
}

.form-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

/* Trip type buttons */
.trip-type {
    display: flex;
    gap: 5px;
    background-color: #444;
    border-radius: 20px;
    padding: 5px;
}

/* Location fields */
.locations {
    display: flex;
    align-items: center;
    gap: 10px;
}

.location-field {
    display: flex;
    flex-direction: column;
    min-width: 200px;
}

/* Date fields */
.dates {
    display: flex;
    gap: 10px;
}

.date-field {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

/* Search button */
.search-btn-container {
    grid-column: 1 / -1;
    margin-top: 10px;
}

.search-btn {
    width: 100%;
    padding: 12px;
    font-size: 16px;
    font-weight: 600;
    background-color: var(--main-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.search-btn:hover {
    background-color: #a51d1d;
}

/* Globe container */
.globe-container {
    float: left;
    width: calc(100% - 40px);
    /* Ensure consistent distance from screen edge */
    max-width: 800px;
    height: 400px;
    background-color: #1a1a1a;
    border-radius: 12px;
    overflow: hidden;
    margin-top: 10px;
    margin-left: 20px;
    /* Match margin with reservations column */
}

/* Results container */
.results-container {
    grid-column: 1 / -1;
    width: 100%;
    margin-top: 20px;
}

#switchButton {
    background-color: #444;
    color: #aaa;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
}

#switchButton:hover {
    background-color: var(--secondary-hover-color);
    color: var(--text-dark);
    border-color: var(--secondary-hover-color);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

/* Reservations column */
.reservations-column {
    position: static;
    width: 320px;
    flex-shrink: 0;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .transport-layout {
        grid-template-columns: 1fr;
    }

    .transport-main {
        grid-column: 1;
        grid-row: 1;
    }

    .reservations-column {
        grid-column: 1;
        grid-row: 2;
        position: static;
        height: auto;
        margin-top: 20px;
        float: right;
        align-items: right;
    }

    .globe-container {
        height: 350px;
    }
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        align-items: stretch;
    }

    .form-group {
        width: 100%;
    }

    .locations,
    .dates {
        flex-direction: column;
        gap: 15px;
    }

    .location-field,
    .date-field {
        width: 100%;
    }

    .trip-type {
        justify-content: center;
        margin-bottom: 10px;
    }

    .globe-container {
        height: 300px;
    }
}