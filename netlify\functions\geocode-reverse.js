// Netlify function for reverse geocoding using Nominatim API
const axios = require('axios');

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  try {
    // Parse query parameters
    const params = event.queryStringParameters || {};
    const { lat, lng } = params;

    // Validate required parameters
    if (!lat || !lng) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing required parameters: lat and lng' })
      };
    }

    // Call Nominatim API with proper User-Agent header
    const response = await axios({
      method: 'get',
      url: 'https://nominatim.openstreetmap.org/reverse',
      params: {
        format: 'json',
        lat,
        lon: lng
      },
      headers: {
        'User-Agent': 'Vestigia-Travel-App/1.0'
      }
    });

    // Return successful response
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify(response.data)
    };
  } catch (error) {
    console.error('Error reverse geocoding:', error.response?.data || error.message);

    // Return error response
    return {
      statusCode: error.response?.status || 500,
      headers,
      body: JSON.stringify({
        error: 'Failed to reverse geocode',
        message: error.message
      })
    };
  }
};
