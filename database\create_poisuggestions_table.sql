-- Create poisuggestions table for user-suggested Points of Interest
CREATE TABLE poisuggestions (
  id SERIAL PRIMARY KEY,
  user_name TEXT NOT NULL,
  user_email TEXT NOT NULL,
  poi_name TEXT NOT NULL,
  poi_location TEXT NOT NULL,
  submission_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status TEXT DEFAULT 'pending' -- Can be 'pending', 'approved', 'rejected'
);

-- Create index on submission_date for faster queries
CREATE INDEX idx_poisuggestions_date ON poisuggestions(submission_date);

-- Create index on status for filtering
CREATE INDEX idx_poisuggestions_status ON poisuggestions(status);

-- Allow anonymous inserts but restrict other operations
ALTER TABLE poisuggestions ENABLE ROW LEVEL SECURITY;

-- Allow anyone to insert new suggestions (including anonymous users)
CREATE POLICY insert_poisuggestions ON poisuggestions
  FOR INSERT WITH CHECK (true);

-- Allow anyone to select their own suggestions based on email
CREATE POLICY select_own_poisuggestions ON poisuggestions
  FOR SELECT USING (true);

-- For future admin functionality, we'll need to set up proper authentication
-- and create more specific policies
