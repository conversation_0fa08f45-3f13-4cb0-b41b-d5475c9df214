/**
 * Airport Search Functionality
 * Handles airport search for the transportation page using Supabase
 */

document.addEventListener('DOMContentLoaded', function () {
    // Check if API client is loaded
    console.log('Airport search script loaded');
    console.log('API client loaded:', !!window.secureAPI);
    if (window.secureAPI) {
        console.log('API client airport module:', !!window.secureAPI.airport);
    } else {
        console.error('API client not loaded! Make sure api-client.js is included before airport-search.js');
    }
    // GLOBAL SUGGESTION BOX CONTROLLER
    function closeAllSuggestionBoxes() {
        const allSuggestionBoxes = document.querySelectorAll('.suggestions');
        allSuggestionBoxes.forEach(box => {
            // Hide with transition
            box.style.opacity = '0';
            box.style.transform = 'translateY(-5px)';
            box.classList.add('hidden-suggestions');

            // After transition, hide the container
            setTimeout(() => {
                box.style.display = 'none';
            }, 200);
        });
    }

    // Add mousedown event listener to document to handle clicks outside
    document.addEventListener('mousedown', function (e) {
        if (!e.target.closest('.suggestions') && !e.target.closest('input')) {
            closeAllSuggestionBoxes();
        }
    }, true);

    // Use the secure API client instead of direct Supabase calls
    // This prevents API keys from being exposed in the frontend

    // DOM Elements - Main Search Form
    const departureInput = document.getElementById('departureInput');
    const destinationInput = document.getElementById('destinationInput');
    const departureSuggestions = document.getElementById('departureSuggestions');
    const destinationSuggestions = document.getElementById('destinationSuggestions');

    // DOM Elements - External Reservation Modal
    const externalFrom = document.getElementById('externalFrom');
    const externalTo = document.getElementById('externalTo');
    const externalFromSuggestions = document.getElementById('externalFromSuggestions');
    const externalToSuggestions = document.getElementById('externalToSuggestions');

    // Debounce function to limit API calls
    function debounce(func, wait) {
        let timeout;
        return function (...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), wait);
        };
    }

    // Function to search airports using Supabase directly
    async function searchAirports(query) {
        if (!query || query.length < 2) return [];

        try {
            console.log('Searching airports with query:', query);

            // Use the airportAPI from supabase-client.js
            if (window.airportAPI && typeof window.airportAPI.searchAirports === 'function') {
                const airports = await window.airportAPI.searchAirports(query);
                console.log('Supabase search results:', airports);
                return airports;
            } else {
                console.error('airportAPI not available or searchAirports method not found');
                return [];
            }
        } catch (error) {
            console.error('Error searching airports:', error);
            return [];
        }
    }

    // Function to display airport suggestions
    function displaySuggestions(airports, container, inputElement) {
        container.innerHTML = '';
        if (airports.length === 0) {
            container.style.display = 'none';
            return;
        }

        // Create a fragment to improve performance
        const fragment = document.createDocumentFragment();

        airports.forEach((airport, index) => {
            const suggestionItem = document.createElement('div');
            suggestionItem.className = 'suggestion-item';

            // Add a small animation delay based on index
            suggestionItem.style.animationDelay = `${index * 30}ms`;

            // Add hover effect listeners
            suggestionItem.addEventListener('mouseenter', function () {
                this.style.borderBottom = '1px solid var(--highlight-color)';
            });

            suggestionItem.addEventListener('mouseleave', function () {
                this.style.borderBottom = '1px solid #3a3a3a';
            });

            suggestionItem.innerHTML = `
                <div class="suggestion-code">${airport.code || '???'}</div>
                <div class="suggestion-details">
                    <div class="suggestion-name">${airport.name}</div>
                    <div class="suggestion-location">${airport.city || ''}${airport.country ? ', ' + airport.country : ''}</div>
                </div>
            `;

            // Replace click with mousedown event
            suggestionItem.addEventListener('mousedown', function (e) {
                e.preventDefault(); // Prevent focus loss
                e.stopPropagation();

                // Use displayName if available, otherwise construct it
                const displayValue = airport.displayName || `${airport.code} - ${airport.name}, ${airport.city || ''}`;
                inputElement.value = displayValue;
                inputElement.dataset.selectionMade = 'true';
                inputElement.dataset.lastValue = displayValue;

                // Hide suggestions with transition
                container.style.opacity = '0';
                container.style.transform = 'translateY(-5px)';
                container.classList.add('hidden-suggestions');

                // After transition, hide the container
                setTimeout(() => {
                    container.style.display = 'none';
                }, 200);

                requestAnimationFrame(() => {
                    inputElement.dispatchEvent(new Event('change', { bubbles: true }));
                });
            });

            fragment.appendChild(suggestionItem);
        });

        // Append all items at once for better performance
        container.appendChild(fragment);
        container.style.display = 'block';
    }

    // Function to handle input changes with debounce
    const handleInputChange = debounce(async function (event) {
        const input = event.target;
        const query = input.value.trim();
        const suggestionsContainer = input.nextElementSibling;

        // Clear the selection flag if the user modifies the input
        if (input.dataset.selectionMade === 'true' && input.dataset.lastValue !== query) {
            input.dataset.selectionMade = 'false';
        }

        // Store the current value for future comparison
        input.dataset.lastValue = query;

        // Don't show suggestions if selection was made and not modified
        if (input.dataset.selectionMade === 'true') {
            return;
        }

        if (query.length < 2) {
            suggestionsContainer.innerHTML = '';
            suggestionsContainer.style.display = 'none';
            return;
        }

        // Show loading indicator
        suggestionsContainer.innerHTML = '<div class="suggestion-loading">Searching airports</div>';
        suggestionsContainer.style.display = 'block';
        suggestionsContainer.style.opacity = '1';
        suggestionsContainer.style.transform = 'translateY(0)';
        suggestionsContainer.classList.remove('hidden-suggestions');

        // Search for airports
        const airports = await searchAirports(query);

        // Display results
        displaySuggestions(airports, suggestionsContainer, input);
    }, 300);

    // Function to handle keyboard navigation in suggestions
    function handleKeyboardNavigation(event) {
        // Only handle keyboard navigation if suggestions are visible
        const input = event.target;
        const suggestionsContainer = input.nextElementSibling;

        if (suggestionsContainer.style.display !== 'block') return;

        const suggestions = suggestionsContainer.querySelectorAll('.suggestion-item');
        if (suggestions.length === 0) return;

        // Find currently focused suggestion
        const focusedSuggestion = suggestionsContainer.querySelector('.suggestion-item.focused');
        let focusedIndex = -1;

        if (focusedSuggestion) {
            focusedSuggestion.classList.remove('focused');
            focusedIndex = Array.from(suggestions).indexOf(focusedSuggestion);
        }

        // Handle arrow keys
        if (event.key === 'ArrowDown') {
            event.preventDefault();
            focusedIndex = (focusedIndex + 1) % suggestions.length;
            suggestions[focusedIndex].classList.add('focused');
            suggestions[focusedIndex].scrollIntoView({ block: 'nearest' });
        } else if (event.key === 'ArrowUp') {
            event.preventDefault();
            focusedIndex = (focusedIndex - 1 + suggestions.length) % suggestions.length;
            suggestions[focusedIndex].classList.add('focused');
            suggestions[focusedIndex].scrollIntoView({ block: 'nearest' });
        } else if (event.key === 'Enter' && focusedIndex >= 0) {
            event.preventDefault();
            suggestions[focusedIndex].click();
        } else if (event.key === 'Escape') {
            // Hide suggestions with transition
            suggestionsContainer.style.opacity = '0';
            suggestionsContainer.style.transform = 'translateY(-5px)';
            suggestionsContainer.classList.add('hidden-suggestions');

            // After transition, hide the container
            setTimeout(() => {
                suggestionsContainer.style.display = 'none';
            }, 200);
        }
    }

    // Add event listeners to input fields
    if (departureInput) {
        departureInput.addEventListener('input', handleInputChange);
        departureInput.addEventListener('keydown', handleKeyboardNavigation);
        departureInput.addEventListener('focus', function () {
            // Only show suggestions if no selection was made or empty
            if (this.value.trim().length >= 2 && this.dataset.selectionMade !== 'true') {
                handleInputChange({ target: this });
            }
        });
    }

    if (destinationInput) {
        destinationInput.addEventListener('input', handleInputChange);
        destinationInput.addEventListener('keydown', handleKeyboardNavigation);
        destinationInput.addEventListener('focus', function () {
            // Only show suggestions if no selection was made or empty
            if (this.value.trim().length >= 2 && this.dataset.selectionMade !== 'true') {
                handleInputChange({ target: this });
            }
        });
    }

    if (externalFrom) {
        externalFrom.addEventListener('input', handleInputChange);
        externalFrom.addEventListener('keydown', handleKeyboardNavigation);
        externalFrom.addEventListener('focus', function () {
            // Only show suggestions if no selection was made or empty
            if (this.value.trim().length >= 2 && this.dataset.selectionMade !== 'true') {
                handleInputChange({ target: this });
            }
        });
    }

    if (externalTo) {
        externalTo.addEventListener('input', handleInputChange);
        externalTo.addEventListener('keydown', handleKeyboardNavigation);
        externalTo.addEventListener('focus', function () {
            // Only show suggestions if no selection was made or empty
            if (this.value.trim().length >= 2 && this.dataset.selectionMade !== 'true') {
                handleInputChange({ target: this });
            }
        });
    }

    // Add CSS for focused suggestion and to hide suggestions when selection is made
    const style = document.createElement('style');
    style.textContent = `
        /* Hide suggestions when selection is made */
        input[data-selection-made="true"] + .suggestions,
        input[data-selection-in-progress="true"] + .suggestions {
            display: none !important;
            opacity: 0 !important;
            transform: translateY(-5px) !important;
            z-index: -1 !important;
        }

        /* Override any other CSS that might show suggestions */
        .suggestions {
            display: none !important;
            opacity: 0 !important;
            transform: translateY(-5px) !important;
        }

        /* Only show suggestions when explicitly set by JavaScript */
        .suggestions[style*="display: block"] {
            display: block !important;
            opacity: 1 !important;
            transform: translateY(0) !important;
            z-index: 9999 !important;
        }

        /* Ensure suggestions are hidden when selection is made */
        .suggestion-item:active + .suggestions,
        .suggestion-item:focus + .suggestions {
            display: none !important;
            opacity: 0 !important;
            z-index: -1 !important;
        }
    `;
    document.head.appendChild(style);

    // Popular airports functionality removed

    // Empty function that does nothing - popular airports feature disabled
    async function showPopularAirports(/* input, suggestionsContainer */) {
        // Do nothing - popular airports feature is disabled
        return;
    }

    // Add focus event listeners to show popular airports
    if (departureInput) {
        departureInput.addEventListener('focus', function () {
            if (this.value.trim().length === 0) {
                showPopularAirports(this, departureSuggestions);
            } else if (this.value.trim().length >= 2 && this.dataset.selectionMade !== 'true') {
                handleInputChange({ target: this });
            }
        });
    }

    if (destinationInput) {
        destinationInput.addEventListener('focus', function () {
            if (this.value.trim().length === 0) {
                showPopularAirports(this, destinationSuggestions);
            } else if (this.value.trim().length >= 2 && this.dataset.selectionMade !== 'true') {
                handleInputChange({ target: this });
            }
        });
    }

    if (externalFrom) {
        externalFrom.addEventListener('focus', function () {
            if (this.value.trim().length === 0) {
                showPopularAirports(this, externalFromSuggestions);
            } else if (this.value.trim().length >= 2 && this.dataset.selectionMade !== 'true') {
                handleInputChange({ target: this });
            }
        });
    }

    if (externalTo) {
        externalTo.addEventListener('focus', function () {
            if (this.value.trim().length === 0) {
                showPopularAirports(this, externalToSuggestions);
            } else if (this.value.trim().length >= 2 && this.dataset.selectionMade !== 'true') {
                handleInputChange({ target: this });
            }
        });
    }

    // Handle the switch button to swap departure and destination
    const switchButton = document.getElementById('switchButton');
    if (switchButton && departureInput && destinationInput) {
        switchButton.addEventListener('click', function () {
            // Swap values
            const tempValue = departureInput.value;
            departureInput.value = destinationInput.value;
            destinationInput.value = tempValue;

            // Swap selection flags
            const tempFlag = departureInput.dataset.selectionMade;
            departureInput.dataset.selectionMade = destinationInput.dataset.selectionMade;
            destinationInput.dataset.selectionMade = tempFlag;

            // Swap last values
            const tempLastValue = departureInput.dataset.lastValue;
            departureInput.dataset.lastValue = destinationInput.dataset.lastValue;
            destinationInput.dataset.lastValue = tempLastValue;

            // Trigger change events
            const event = new Event('change', { bubbles: true });
            departureInput.dispatchEvent(event);
            destinationInput.dispatchEvent(event);
        });
    }
});
