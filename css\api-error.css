/* API Error and Loading Styling */

/* Loading Spinner and Details */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px;
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-left-color: var(--main-color);
    border-radius: 50%;
    margin-bottom: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading p {
    margin: 5px 0;
    font-size: 16px;
    color: #fff;
}

.loading-details {
    font-size: 14px;
    color: #aaa;
    margin-top: 5px;
}

.api-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px;
    text-align: center;
    color: #f44336;
    background-color: rgba(244, 67, 54, 0.1);
    border-radius: 8px;
    margin: 20px 0;
}

.api-error i {
    font-size: 48px;
    margin-bottom: 15px;
}

.api-error p {
    margin: 5px 0;
    font-size: 16px;
}

.api-error .error-hint {
    font-size: 14px;
    color: #aaa;
    margin-top: 10px;
}

.api-error .retry-btn {
    margin-top: 15px;
    padding: 8px 20px;
    background-color: var(--main-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.api-error .retry-btn:hover {
    background-color: #a52020;
}

/* Server Error Banner */
.server-error-banner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: #861818;
    color: white;
    z-index: 9999;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.server-error-banner .error-content {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.server-error-banner i {
    font-size: 24px;
    margin-right: 15px;
}

.server-error-banner .error-message {
    flex: 1;
}

.server-error-banner h4 {
    margin: 0 0 5px;
    font-size: 16px;
}

.server-error-banner p {
    margin: 0;
    font-size: 14px;
}

.server-error-banner code {
    background-color: rgba(0, 0, 0, 0.3);
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 5px;
    font-family: monospace;
}

.server-error-banner .close-error {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s;
}

.server-error-banner .close-error:hover {
    opacity: 1;
}