/**
 * Travelpayouts API Client
 * 
 * This client provides methods to interact with the Travelpayouts (Aviasales) Flight Search API.
 * It handles the two-step process of flight search:
 * 1. Initialize a search to get a search_id
 * 2. Poll for results using that search_id
 * 
 * Usage:
 * const results = await window.travelpayoutsAPI.searchFlights(
 *   searchParams,
 *   progressCallback,
 *   timeoutMs,
 *   pollIntervalMs
 * );
 */

// API endpoint for Travelpayouts search
const SEARCH_API_URL = '/.netlify/functions/travelpayouts-search';
const BOOKING_API_URL = '/.netlify/functions/travelpayouts-booking';

// Create a global travelpayoutsAPI object
window.travelpayoutsAPI = {
    /**
     * Search for flights using Travelpayouts API
     * @param {Object} params - Search parameters
     * @param {Function} progressCallback - Callback for search progress updates
     * @param {number} timeoutMs - Timeout in milliseconds (default: 60000)
     * @param {number} pollIntervalMs - Polling interval in milliseconds (default: 2000)
     * @returns {Promise<Object>} Flight search results
     */
    searchFlights: async function(params, progressCallback = null, timeoutMs = 60000, pollIntervalMs = 2000) {
        try {
            // Validate required parameters
            if (!params.origin || !params.destination || !params.departureDate) {
                throw new Error('Missing required parameters: origin, destination, departureDate');
            }

            // Initialize search
            const searchId = await this.initializeSearch(params);
            
            if (progressCallback) {
                progressCallback({ status: 'initialized', message: 'Search initialized', progress: 10 });
            }
            
            // Poll for results
            const startTime = Date.now();
            let pollCount = 0;
            let results = null;
            
            while (Date.now() - startTime < timeoutMs) {
                pollCount++;
                
                if (progressCallback) {
                    // Calculate progress between 10% and 90%
                    const elapsedPercent = Math.min(80, ((Date.now() - startTime) / timeoutMs) * 80);
                    progressCallback({ 
                        status: 'searching', 
                        message: `Polling for results (attempt ${pollCount})...`, 
                        progress: 10 + elapsedPercent 
                    });
                }
                
                results = await this.pollResults(searchId);
                
                if (results.status === 'complete') {
                    if (progressCallback) {
                        progressCallback({ 
                            status: 'complete', 
                            message: `Found ${results.data.length} flights`, 
                            progress: 100 
                        });
                    }
                    return results;
                }
                
                // Wait before polling again
                await new Promise(resolve => setTimeout(resolve, pollIntervalMs));
            }
            
            throw new Error('Search timeout exceeded');
        } catch (error) {
            console.error('Error searching flights:', error);
            
            if (progressCallback) {
                progressCallback({ 
                    status: 'error', 
                    message: error.message, 
                    progress: 0 
                });
            }
            
            throw error;
        }
    },
    
    /**
     * Initialize a flight search
     * @param {Object} params - Search parameters
     * @returns {Promise<string>} Search ID
     */
    initializeSearch: async function(params) {
        try {
            console.log('Initializing flight search with parameters:', params);
            
            const response = await fetch(SEARCH_API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(params)
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to initialize search');
            }
            
            const data = await response.json();
            console.log('Search initialized with ID:', data.search_id);
            
            return data.search_id;
        } catch (error) {
            console.error('Error initializing search:', error);
            throw error;
        }
    },
    
    /**
     * Poll for search results
     * @param {string} searchId - Search ID
     * @returns {Promise<Object>} Search results
     */
    pollResults: async function(searchId) {
        try {
            console.log(`Polling for results with search ID: ${searchId}`);
            
            const response = await fetch(`${SEARCH_API_URL}?search_id=${searchId}`);
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to poll for results');
            }
            
            const data = await response.json();
            
            if (data.status === 'searching') {
                console.log('Search still in progress, continue polling');
                return { status: 'searching' };
            }
            
            console.log('Search complete, found', data.data?.length || 0, 'flights');
            return { 
                status: 'complete', 
                data: data.data || [],
                currency: data.currency,
                currency_rates: data.currency_rates
            };
        } catch (error) {
            console.error('Error polling for results:', error);
            throw error;
        }
    },
    
    /**
     * Generate a booking link for a flight
     * @param {string} searchId - Search ID
     * @param {string} urlKey - URL key from the flight proposal
     * @param {string} marker - Affiliate marker
     * @returns {Promise<Object>} Booking link data
     */
    generateBookingLink: async function(searchId, urlKey, marker) {
        try {
            console.log(`Generating booking link for search_id: ${searchId}, url_key: ${urlKey}`);
            
            const response = await fetch(BOOKING_API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    search_id: searchId,
                    url_key: urlKey,
                    marker: marker
                })
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to generate booking link');
            }
            
            return await response.json();
        } catch (error) {
            console.error('Error generating booking link:', error);
            throw error;
        }
    }
};

console.log('Travelpayouts API client loaded');
