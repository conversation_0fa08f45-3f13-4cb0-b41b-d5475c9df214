-- SQL file to add missing columns to user_markers table

-- Add addedToItinerary column if it doesn't exist
DO $$
BEGIN
    -- Check if the column exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'user_markers'
        AND column_name = 'addedToItinerary'
    ) THEN
        -- Add the column
        ALTER TABLE user_markers ADD COLUMN "addedToItinerary" BOOLEAN DEFAULT FALSE;
    END IF;
    
    -- Check if isCustom column exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'user_markers'
        AND column_name = 'isCustom'
    ) THEN
        -- Add the column
        ALTER TABLE user_markers ADD COLUMN "isCustom" BOOLEAN DEFAULT FALSE;
    END IF;
END
$$;
