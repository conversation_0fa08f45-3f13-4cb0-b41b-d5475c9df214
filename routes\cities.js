const express = require('express');
const axios = require('axios');
const router = express.Router();

/**
 * Search for cities by name prefix
 */
router.get('/search', async (req, res) => {
  try {
    const { namePrefix, limit } = req.query;
    
    if (!namePrefix) {
      return res.status(400).json({ error: 'namePrefix parameter is required' });
    }

    // Make the API request to GeoDB Cities API
    const response = await axios.get(`https://wft-geo-db.p.rapidapi.com/v1/geo/cities`, {
      params: {
        namePrefix,
        limit: limit || 5
      },
      headers: {
        'X-RapidAPI-Key': process.env.RAPID_API_KEY,
        'X-RapidAPI-Host': 'wft-geo-db.p.rapidapi.com'
      }
    });

    res.json(response.data);
  } catch (error) {
    console.error('Error searching cities:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({ 
      error: 'Failed to search cities',
      details: error.response?.data || error.message
    });
  }
});

module.exports = router;
