const axios = require('axios');
const crypto = require('crypto');

// CORS headers
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
};

// Environment variables
const TRAVELPAYOUTS_MARKER = process.env.AVIASALES_USER_ID;
const TRAVELPAYOUTS_TOKEN = process.env.AVIASALES_API_TOKEN;

exports.handler = async (event, context) => {
  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ message: 'Preflight call successful' })
    };
  }

  try {
    console.log('Testing Travelpayouts API...');

    // Log environment variables (safely)
    console.log('Environment check:', {
      hasToken: !!TRAVELPAYOUTS_TOKEN,
      hasMarker: !!TRAVELPAYOUTS_MARKER,
      tokenLength: TRAVELPAYOUTS_TOKEN ? TRAVELPAYOUTS_TOKEN.length : 0,
      marker: TRAVELPAYOUTS_MARKER
    });

    // Test simple API call first - try to get airport data
    const testUrl = 'https://api.travelpayouts.com/data/en/airports.json';

    console.log('Making test API call to:', testUrl);

    const response = await axios.get(testUrl, {
      timeout: 10000,
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'Vestigia/1.0'
      }
    });

    console.log('Test API call successful');
    console.log('Response status:', response.status);
    console.log('Response data length:', response.data ? response.data.length : 0);

    // Now test a simple flight search
    const searchUrl = 'https://api.travelpayouts.com/v1/flight_search';

    const searchParams = {
      marker: TRAVELPAYOUTS_MARKER,
      host: 'vestigia.app',
      user_ip: '127.0.0.1',
      locale: 'en',
      trip_class: 'Y',
      passengers: {
        adults: 1,
        children: 0,
        infants: 0
      },
      segments: [
        {
          origin: 'JFK',
          destination: 'LHR',
          date: '2025-12-25'
        }
      ]
    };

    // Create signature according to official documentation
    // Parameters must be sorted alphabetically: host, locale, marker, adults, children, infants, date, destination, origin, trip_class, user_ip
    const signatureValues = [
      'vestigia.app',       // host
      'en',                 // locale
      TRAVELPAYOUTS_MARKER, // marker
      '1',                  // adults
      '0',                  // children
      '0',                  // infants
      '2025-12-25',         // date
      'LHR',                // destination
      'JFK',                // origin
      'Y',                  // trip_class
      '127.0.0.1'           // user_ip
    ];

    const valuesString = signatureValues.join(':');
    const signatureString = TRAVELPAYOUTS_TOKEN + ':' + valuesString;
    const signature = crypto.createHash('md5').update(signatureString).digest('hex');
    searchParams.signature = signature;

    console.log('Signature string:', signatureString);
    console.log('Generated signature:', signature);
    console.log('Search params:', JSON.stringify(searchParams, null, 2));

    console.log('Making flight search API call to:', searchUrl);

    const searchResponse = await axios.post(searchUrl, searchParams, {
      timeout: 15000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'Vestigia/1.0'
      }
    });

    console.log('Flight search API call successful');
    console.log('Search response status:', searchResponse.status);
    console.log('Search response data:', JSON.stringify(searchResponse.data, null, 2));

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        message: 'Travelpayouts API test successful',
        environment: {
          hasToken: !!TRAVELPAYOUTS_TOKEN,
          hasMarker: !!TRAVELPAYOUTS_MARKER,
          tokenLength: TRAVELPAYOUTS_TOKEN ? TRAVELPAYOUTS_TOKEN.length : 0,
          marker: TRAVELPAYOUTS_MARKER
        },
        airportsTest: {
          status: response.status,
          dataLength: response.data ? response.data.length : 0
        },
        flightSearchTest: {
          status: searchResponse.status,
          data: searchResponse.data
        }
      })
    };

  } catch (error) {
    console.error('Error in test function:', error);
    console.error('Error details:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      stack: error.stack
    });

    return {
      statusCode: error.response?.status || 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: error.message,
        details: error.response?.data || 'No additional details available',
        environment: {
          hasToken: !!TRAVELPAYOUTS_TOKEN,
          hasMarker: !!TRAVELPAYOUTS_MARKER,
          tokenLength: TRAVELPAYOUTS_TOKEN ? TRAVELPAYOUTS_TOKEN.length : 0,
          marker: TRAVELPAYOUTS_MARKER
        }
      })
    };
  }
};
