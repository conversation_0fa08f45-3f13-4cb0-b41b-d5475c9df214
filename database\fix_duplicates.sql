-- First, let's create a function to clean up existing duplicates
CREATE OR <PERSON><PERSON>LACE FUNCTION cleanup_duplicate_markers()
RETURNS JSONB AS $$
DECLARE
  deleted_count INTEGER := 0;
  user_record RECORD;
BEGIN
  -- Process each user's markers separately
  FOR user_record IN SELECT DISTINCT user_id FROM user_markers LOOP
    WITH duplicates AS (
      SELECT id,
             ROW_NUMBER() OVER (
               PARTITION BY 
                 user_id, 
                 ROUND(lat::numeric, 6), 
                 ROUND(lng::numeric, 6),
                 TRIM(LOWER(name))
               ORDER BY updated_at DESC, id DESC
             ) as rn
      FROM user_markers
      WHERE user_id = user_record.user_id
    )
    DELETE FROM user_markers
    WHERE id IN (
      SELECT id FROM duplicates WHERE rn > 1
    )
    RETURNING 1 INTO deleted_count;
    
    deleted_count := deleted_count + COALESCE(deleted_count, 0);
  END LOOP;
  
  RETURN jsonb_build_object('deleted_count', deleted_count);
END;
$$ LANGUAGE plpgsql;

-- Update the sync_user_marker function to be more strict with duplicates
CREATE OR REPLACE FUNCTION sync_user_marker(
  p_user_id TEXT,
  p_lat FLOAT,
  p_lng FLOAT,
  p_name TEXT,
  p_description TEXT,
  p_placeName TEXT,
  p_isPOI BOOLEAN,
  p_isCustom BOOLEAN,
  p_wikiName TEXT,
  p_addedToItinerary BOOLEAN
) RETURNS JSONB AS $$
DECLARE
  marker_record RECORD;
  result JSONB;
  normalized_name TEXT := TRIM(LOWER(p_name));
  rounded_lat NUMERIC := ROUND(p_lat::numeric, 6);
  rounded_lng NUMERIC := ROUND(p_lng::numeric, 6);
BEGIN
  -- First try to find an existing marker with the same location and name (case-insensitive)
  SELECT * INTO marker_record
  FROM user_markers
  WHERE user_id = p_user_id
  AND ROUND(lat::numeric, 6) = rounded_lat
  AND ROUND(lng::numeric, 6) = rounded_lng
  AND TRIM(LOWER(name)) = normalized_name
  LIMIT 1;
  
  -- If marker exists, update it
  IF FOUND THEN
    UPDATE user_markers
    SET 
      description = COALESCE(p_description, description),
      placeName = COALESCE(p_placeName, placeName),
      isPOI = COALESCE(p_isPOI, isPOI),
      isCustom = COALESCE(p_isCustom, isCustom),
      wikiName = COALESCE(p_wikiName, wikiName),
      addedToItinerary = COALESCE(p_addedToItinerary, addedToItinerary),
      updated_at = NOW()
    WHERE id = marker_record.id
    RETURNING to_jsonb(user_markers.*) INTO result;
  ELSE
    -- Otherwise, insert new marker
    INSERT INTO user_markers (
      user_id, lat, lng, name, description, placeName, 
      isPOI, isCustom, wikiName, addedToItinerary
    ) VALUES (
      p_user_id, p_lat, p_lng, p_name, 
      COALESCE(p_description, ''),
      COALESCE(p_placeName, ''),
      COALESCE(p_isPOI, false),
      COALESCE(p_isCustom, false),
      COALESCE(p_wikiName, ''),
      COALESCE(p_addedToItinerary, false)
    )
    RETURNING to_jsonb(user_markers.*) INTO result;
  END IF;
  
  -- Clean up any potential duplicates that might have been created
  PERFORM cleanup_duplicate_markers();
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;
