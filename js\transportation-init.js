/**
 * Transportation Page Initialization
 * 
 * This file handles the initialization of the transportation page,
 * including auth setup and UI updates.
 */

// Wait for all required modules to be loaded
function waitForModules() {
    return new Promise((resolve) => {
        const checkModules = () => {
            if (window.supabase && window.auth) {
                console.log('All required modules loaded successfully');
                resolve();
            } else {
                console.log('Waiting for modules... supabase:', !!window.supabase, 'auth:', !!window.auth);
                setTimeout(checkModules, 100);
            }
        };
        checkModules();
    });
}

// Initialize the page
async function initializePage() {
    try {
        // Wait for modules to be loaded
        await waitForModules();
        console.log('Initializing transportation page...');

        // Load navbar using the navbar-loader function
        if (typeof loadNavbar === 'function') {
            console.log('Loading navbar using navbar-loader...');
            loadNavbar();
        } else {
            console.error('Navbar loader function not found!');
        }

        // Initialize auth after navbar is loaded
        if (typeof auth !== 'undefined') {
            console.log('Initializing auth after navbar is loaded');
            await auth.init();
            // Force a second initialization after a short delay to ensure UI is updated
            setTimeout(() => {
                if (typeof auth !== 'undefined') {
                    console.log('Forcing auth UI update after navbar load');
                    auth.updateUI(auth.isLoggedIn());
                }
            }, 200);
        }

        // Load footer
        const footerResponse = await fetch('footer-template.html');
        const footerData = await footerResponse.text();
        document.getElementById('footer-placeholder').innerHTML = footerData;

        // Initialize back to top button
        const backToTopButton = document.getElementById('back-to-top');
        if (backToTopButton) {
            backToTopButton.addEventListener('click', (e) => {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });

            // Show/hide back to top button based on scroll position
            window.addEventListener('scroll', () => {
                if (window.scrollY > 300) {
                    backToTopButton.style.opacity = '1';
                } else {
                    backToTopButton.style.opacity = '0';
                }
            });
        }
    } catch (error) {
        console.error('Error initializing transportation page:', error);
    }
}

// Start initialization when DOM is loaded
document.addEventListener('DOMContentLoaded', initializePage);

// Also initialize if DOM is already loaded
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    initializePage();
} 