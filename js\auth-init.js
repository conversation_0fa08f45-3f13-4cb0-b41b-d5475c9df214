// Initialize Supabase auth state and handle session persistence

// Function to initialize auth state with enhanced logging
async function initAuth() {
  console.log('Initializing auth state...');
  
  // Verify supabase is available
  if (!window.supabase) {
    console.error('Supabase client not available');
    return null;
  }
  
  try {
    console.log('Checking for existing session...');
    // First check for existing session with detailed logging
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('Error getting session:', sessionError);
      return null;
    }
    
    const { session } = sessionData || {};
    if (session) {
      console.log('Found existing session for user:', session.user?.email);
      console.log('Session expires at:', session.expires_at);
      console.log('Session expires in (minutes):', Math.floor((session.expires_at * 1000 - Date.now()) / 60000));
      return session.user;
    }
    
    console.log('No active session found, checking for user...');
    // If no session, try to get the user
    const { data: userData, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      console.log('No authenticated user found:', userError.message);
      return null;
    }
    
    const { user } = userData || {};
    if (user) {
      console.log('Found user without active session, attempting to refresh...');
      console.log('User email:', user.email);
      
      try {
        // Try to refresh the session
        console.log('Refreshing session...');
        const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
        
        if (refreshError) {
          console.error('Error refreshing session:', refreshError);
          return null;
        }
        
        if (refreshData?.session) {
          console.log('Session refreshed successfully for user:', refreshData.user?.email);
          return refreshData.user;
        }
      } catch (refreshError) {
        console.error('Exception while refreshing session:', refreshError);
      }
    }
    
    console.log('No valid session or user found');
    return null;
  } catch (error) {
    console.error('Error initializing auth state:', error);
    return null;
  }
}

// Export the init function
window.initAuth = initAuth;

// Initialize auth when the script loads
initAuth().then(user => {
  if (user) {
    console.log('Auth initialized successfully for user:', user.email);
  } else {
    console.log('No authenticated user found');
  }
});
