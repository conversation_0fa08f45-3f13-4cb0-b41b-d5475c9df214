/**
 * Map Module for Vestigia
 *
 * This module handles the interactive map functionality including:
 * - Map initialization with Google Maps API
 * - Route calculation and display
 * - Travel mode selection and handling
 * - Integration with the transportation page for flight/train routes
 */

// Global variables for map components
// Note: 'map' is already defined in map.html
// let map;               // Google Maps instance
let directionsService; // Service for calculating routes
let directionsRenderer; // Renderer for displaying routes on the map
// markers is already defined in map.html
// let markers = [];      // Array to store map markers

/**
 * Initialize the Google Maps instance and related components
 * Called when the page loads via the Google Maps callback
 */
function initMap() {
    // Create the map centered on Paris, France
    map = new google.maps.Map(document.getElementById('map'), {
        center: { lat: 48.8566, lng: 2.3522 }, // Paris coordinates
        zoom: 6,                               // Initial zoom level
        styles: [
            {
                // Hide points of interest labels to reduce clutter
                featureType: "poi",
                elementType: "labels",
                stylers: [{ visibility: "off" }]
            }
        ]
    });

    // Initialize directions services for route calculation
    directionsService = new google.maps.DirectionsService();
    directionsRenderer = new google.maps.DirectionsRenderer({
        map: map,
        suppressMarkers: true // We'll add custom markers instead
    });

    // Create a panel to display route directions
    const directionsPanel = document.createElement('div');
    directionsPanel.id = 'directionsPanel';
    directionsPanel.className = 'directions-panel';
    document.getElementById('map').appendChild(directionsPanel);

    // Add travel mode selection controls to the map
    const travelModeControl = document.createElement('div');
    travelModeControl.className = 'travel-mode-control';
    travelModeControl.innerHTML = `
        <button class="mode-btn active" data-mode="DRIVING"><i class="fas fa-car"></i></button>
        <button class="mode-btn" data-mode="WALKING"><i class="fas fa-walking"></i></button>
        <button class="mode-btn" data-mode="BICYCLING"><i class="fas fa-bicycle"></i></button>
        <button class="mode-btn" data-mode="TRANSIT"><i class="fas fa-bus"></i></button>
        <button class="mode-btn" data-mode="FLIGHT"><i class="fas fa-plane"></i></button>
    `;

    // Position the travel mode controls at the top right of the map
    map.controls[google.maps.ControlPosition.TOP_RIGHT].push(travelModeControl);

    // Set up event handling for travel mode selection
    let selectedMode = 'DRIVING'; // Default travel mode
    travelModeControl.addEventListener('click', (e) => {
        if (e.target.closest('.mode-btn')) {
            const button = e.target.closest('.mode-btn');
            const mode = button.dataset.mode;

            // For transit or flight modes, redirect to the transportation page
            // which has specialized handling for these modes
            if (mode === 'TRANSIT' || mode === 'FLIGHT') {
                const origin = document.getElementById('origin').value;
                const destination = document.getElementById('destination').value;

                if (origin && destination) {
                    // Pass the route information to the transportation page
                    const params = new URLSearchParams({
                        departure: origin,
                        destination: destination,
                        transportMode: mode === 'TRANSIT' ? 'train' : 'flight'
                    });
                    window.location.href = `transportation.html?${params.toString()}`;
                    return;
                }
            }

            // Update the selected travel mode
            selectedMode = mode;

            // Update the UI to show the active mode
            document.querySelectorAll('.mode-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            button.classList.add('active');

            // Recalculate the route if origin and destination are already set
            const origin = document.getElementById('origin').value;
            const destination = document.getElementById('destination').value;
            if (origin && destination) {
                calculateAndDisplayRoute();
            }
        }
    });

    // Set up form submission handler for route calculation
    document.getElementById('directionsForm').addEventListener('submit', (e) => {
        e.preventDefault(); // Prevent page reload on form submission
        calculateAndDisplayRoute();
    });
}

/**
 * Calculate and display a route between origin and destination
 * Uses the Google Maps Directions API
 */
function calculateAndDisplayRoute() {
    const origin = document.getElementById('origin').value;
    const destination = document.getElementById('destination').value;
    const selectedMode = document.querySelector('.mode-btn.active').dataset.mode;

    // For transit or flight modes, redirect to the transportation page
    if (selectedMode === 'TRANSIT' || selectedMode === 'FLIGHT') {
        const params = new URLSearchParams({
            departure: origin,
            destination: destination,
            transportMode: selectedMode === 'TRANSIT' ? 'train' : 'flight'
        });
        window.location.href = `transportation.html?${params.toString()}`;
        return;
    }

    // Prepare the route request for Google Maps Directions API
    const request = {
        origin: origin,
        destination: destination,
        travelMode: selectedMode // DRIVING, WALKING, or BICYCLING
    };

    // Call the Directions service to calculate the route
    directionsService.route(request, (result, status) => {
        if (status === 'OK') {
            // Display the route on the map
            directionsRenderer.setDirections(result);
            // Show detailed route information in the directions panel
            displayRouteDetails(result);
        } else {
            // Handle route calculation errors
            alert('Directions request failed due to ' + status);
        }
    });
}

/**
 * Display detailed route information in the directions panel
 *
 * @param {Object} result - The route result from Google Maps Directions API
 */
function displayRouteDetails(result) {
    const route = result.routes[0]; // Get the first (primary) route
    const panel = document.getElementById('directionsPanel');

    // Build the HTML for the route summary
    let html = '<div class="route-summary">';
    html += `<h3>Route Summary</h3>`;
    html += `<p><strong>Distance:</strong> ${route.legs[0].distance.text}</p>`;
    html += `<p><strong>Duration:</strong> ${route.legs[0].duration.text}</p>`;
    html += '</div><div class="route-steps">';

    // Add each step of the route with instructions
    route.legs[0].steps.forEach((step, i) => {
        html += `
            <div class="route-step">
                <div class="step-number">${i + 1}</div>
                <div class="step-instruction">${step.instructions}</div>
                <div class="step-distance">${step.distance.text}</div>
            </div>
        `;
    });

    html += '</div>';
    panel.innerHTML = html;
}

// Export the initMap function for the Google Maps API callback
window.initMap = initMap;