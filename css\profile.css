header {
    background-color: #1A1A1A;
    padding: 0px;
}

h1 {
    margin: 0;
    color: #B49E65;
}

main {
    padding: 20px;
}

section {
    margin-bottom: 20px;
}

h2 {
    margin-top: 0;
}

ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
}

table {
    border-collapse: collapse;
    width: 100%;
}

.headerTitle {
    color: #B49E65;
    font-weight: normal;
    padding-top: 0px;
}

body {
    color: white;
}

a {
    text-decoration: none;
    color: white;
}

a:hover {
    text-decoration: underline;
}

.banner {
    width: 100%;
    height: 200px;
    background-color: #333;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ffd700;
    font-size: 2em;
    position: relative;
}

.banner input[type="file"] {
    display: none;
}

.banner label {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: #FFD700; /* Changed to a more visible color */
    color: #000000; /* Changed to a more visible color */
    padding: 5px 10px;
    cursor: pointer;
    border-radius: 5px;
}

.banner .customize-banner-btn {
    position: absolute;
    bottom: 10px;
    right: 80px; /* Adjusted to avoid overlap with the existing label */
    background-color: #FFD700;
    color: #000000;
    padding: 5px 10px;
    cursor: pointer;
    border-radius: 5px;
    border: none;
    font-size: 14px;
}

.banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    opacity: 1; /* Adjusted transparency */
}

.profileDetails {
    float: left;
    padding-left: 100px;
}

.profilePic {
    height: 200px;
    width: 200px;
    margin-top: -150px;
    z-index: 1;
    position: relative;
    border-radius: 50%; /* Added to make the profile photo circular */
    border: 5px solid #ffd700; /* Optional: Add a border around the profile photo */
    background-color: gray; /* Added gray background */
}

.card {
    display: inline-block;
    width: 200px;
    padding: 10px;
    margin: 10px;
    text-align: center;
    flex: 10%;
}

.card img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    margin-bottom: 10px;
}

.card p {
    margin-bottom: 0;
    color: #ffd700;
}

.card-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
}

.moreContent {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.moreContentTitle {
    color: #ffd700;
    text-decoration: none;
}

.moreContentPhoto {
    height: 100px;
    width: 100px;
}

.moreContentGeneral {
    margin-left: 70px;
    align-items: center;
    margin-bottom: 20px;
}

* {
    box-sizing: border-box;
}

.row {
    display: flex;
    margin-left: -5px;
    margin-right: -5px;
    text-decoration: none;
}

.column {
    flex: 33%;
    padding: 5px;
    padding-bottom: 50px;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
    border: 1px solid #ffd700;
}

th, td {
    text-align: left;
    padding: 12.5px;
    text-decoration: none;
}

.profileBody{
  margin-left: 400px;
}

.edit-icon {
    cursor: pointer;
    margin-left: 10px;
    color: #ffd700;
}

[contenteditable="true"] {
    outline: none;
    border: 1px dashed #ffd700;
}

#save-btn, #cancel-btn, #exit-btn {
    background-color: #FFD700;
    color: #000;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    border-radius: 5px;
    margin: 10px;
}

.milestone-img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
    vertical-align: middle;
}