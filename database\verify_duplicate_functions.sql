-- First, check if the functions exist
DO $$
BEGIN
    -- Check if find_duplicate_markers exists
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_proc p 
        JOIN pg_namespace n ON p.pronamespace = n.oid 
        WHERE n.nspname = 'public' 
        AND p.proname = 'find_duplicate_markers'
    ) THEN
        RAISE NOTICE 'Creating function find_duplicate_markers';
        
        CREATE OR REPLACE FUNCTION find_duplicate_markers(p_user_id TEXT)
        RETURNS TABLE (
          id INTEGER,
          lat FLOAT,
          lng FLOAT,
          name TEXT,
          created_at TIMESTAMPTZ,
          duplicate_count BIGINT
        ) AS $$
        BEGIN
          RETURN QUERY
          WITH duplicates AS (
            SELECT 
              id,
              lat,
              lng,
              name,
              created_at,
              ROW_NUMBER() OVER (
                PARTITION BY 
                  user_id, 
                  ROUND(lat::numeric, 6), 
                  ROUND(lng::numeric, 6),
                  TRIM(LOWER(name))
                ORDER BY created_at DESC, id DESC
              ) as rn,
              COUNT(*) OVER (
                PARTITION BY 
                  user_id, 
                  ROUND(lat::numeric, 6), 
                  ROUND(lng::numeric, 6),
                  TRIM(LOWER(name))
              ) as count
            FROM user_markers
            WHERE user_id = p_user_id
          )
          SELECT 
            id,
            lat,
            lng,
            name,
            created_at,
            count - 1 as duplicate_count
          FROM duplicates
          WHERE rn > 1
          ORDER BY count DESC, created_at DESC;
        END;
        $$ LANGUAGE plpgsql;
        
        COMMENT ON FUNCTION find_duplicate_markers IS 'Finds duplicate markers for a user based on location and name, returning details about duplicates';
        
        GRANTANT EXECUTE ON FUNCTION find_duplicate_markers(TEXT) TO anon, authenticated, service_role;
    END IF;

    -- Check if delete_duplicate_markers exists
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_proc p 
        JOIN pg_namespace n ON p.pronamespace = n.oid 
        WHERE n.nspname = 'public' 
        AND p.proname = 'delete_duplicate_markers'
    ) THEN
        RAISE NOTICE 'Creating function delete_duplicate_markers';
        
        CREATE OR REPLACE FUNCTION delete_duplicate_markers(p_user_id TEXT)
        RETURNS INTEGER AS $$
        DECLARE
          deleted_count INTEGER;
        BEGIN
          WITH duplicates_to_delete AS (
            SELECT id
            FROM (
              SELECT 
                id,
                ROW_NUMBER() OVER (
                  PARTITION BY 
                    user_id, 
                    ROUND(lat::numeric, 6), 
                    ROUND(lng::numeric, 6),
                    TRIM(LOWER(name))
                  ORDER BY created_at DESC, id DESC
                ) as rn
              FROM user_markers
              WHERE user_id = p_user_id
            ) t
            WHERE rn > 1
          )
          DELETE FROM user_markers
          WHERE id IN (SELECT id FROM duplicates_to_delete)
          RETURNING 1 INTO deleted_count;
          
          RETURN COALESCE(deleted_count, 0);
        END;
        $$ LANGUAGE plpgsql;
        
        COMMENT ON FUNCTION delete_duplicate_markers IS 'Deletes duplicate markers for a user, keeping the most recent one';
        
        GRANTANT EXECUTE ON FUNCTION delete_duplicate_markers(TEXT) TO anon, authenticated, service_role;
    END IF;
    
    -- Check if cleanup_all_duplicate_markers exists
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_proc p 
        JOIN pg_namespace n ON p.pronamespace = n.oid 
        WHERE n.nspname = 'public' 
        AND p.proname = 'cleanup_all_duplicate_markers'
    ) THEN
        RAISE NOTICE 'Creating function cleanup_all_duplicate_markers';
        
        CREATE OR REPLACE FUNCTION cleanup_all_duplicate_markers()
        RETURNS JSONB AS $$
        DECLARE
          user_record RECORD;
          total_deleted INTEGER := 0;
          result JSONB := '[]'::JSONB;
          user_result JSONB;
        BEGIN
          -- Process each user's markers separately
          FOR user_record IN SELECT DISTINCT user_id FROM user_markers LOOP
            -- Delete duplicates for this user
            SELECT jsonb_build_object(
              'user_id', user_record.user_id,
              'deleted', delete_duplicate_markers(user_record.user_id)
            ) INTO user_result;
            
            -- Add to results
            result := result || user_result::jsonb;
            total_deleted := total_deleted + (user_result->>'deleted')::int;
          END LOOP;
          
          -- Return summary
          RETURN jsonb_build_object(
            'status', 'success',
            'total_duplicates_removed', total_deleted,
            'users_processed', jsonb_array_length(result),
            'details', result
          );
        END;
        $$ LANGUAGE plpgsql;
        
        COMMENT ON FUNCTION cleanup_all_duplicate_markers IS 'Cleans up duplicate markers for all users (admin only)';
        
        GRANTANT EXECUTE ON FUNCTION cleanup_all_duplicate_markers() TO service_role;
    END IF;
    
    RAISE NOTICE 'All duplicate marker functions verified and created if needed';
END $$;
