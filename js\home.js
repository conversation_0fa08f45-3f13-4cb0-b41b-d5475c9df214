// Initialize AOS (Animate On Scroll)
document.addEventListener('DOMContentLoaded', function () {
  // Initialize AOS
  AOS.init({
    duration: 800,
    easing: 'ease',
    once: true,
    offset: 100
  });



  // Smooth scroll for navigation
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();

      const targetId = this.getAttribute('href');
      if (targetId === '#') return;

      const targetElement = document.querySelector(targetId);
      if (targetElement) {
        window.scrollTo({
          top: targetElement.offsetTop - 70, // Adjust for navbar height
          behavior: 'smooth'
        });
      }
    });
  });

  // Scroll indicator functionality
  const scrollIndicator = document.querySelector('.scroll-indicator');
  if (scrollIndicator) {
    scrollIndicator.addEventListener('click', () => {
      const welcomeSection = document.getElementById('welcome');
      if (welcomeSection) {
        window.scrollTo({
          top: welcomeSection.offsetTop - 70,
          behavior: 'smooth'
        });
      }
    });
  }

  // Back to top button functionality
  const backToTopButton = document.getElementById('back-to-top');
  if (backToTopButton) {
    backToTopButton.addEventListener('click', (e) => {
      e.preventDefault();
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });
  }

  // Show/hide back to top button based on scroll position
  window.addEventListener('scroll', () => {
    if (backToTopButton) {
      if (window.scrollY > 300) {
        backToTopButton.style.opacity = '1';
      } else {
        backToTopButton.style.opacity = '0';
      }
    }
  });

  // Newsletter form submission (prevent default for now)
  const newsletterForm = document.querySelector('.newsletter-form');
  if (newsletterForm) {
    newsletterForm.addEventListener('submit', (e) => {
      e.preventDefault();
      const emailInput = newsletterForm.querySelector('input[type="email"]');
      if (emailInput && emailInput.value) {
        // In a real application, you would send this to a server
        alert(`Thank you for subscribing with ${emailInput.value}!`);
        emailInput.value = '';
      }
    });
  }

});




// Add parallax effect to hero section
window.addEventListener('scroll', function () {
  const parallaxSection = document.querySelector('.parallax-section');
  if (parallaxSection) {
    const scrollPosition = window.pageYOffset;
    parallaxSection.style.backgroundPositionY = scrollPosition * 0.5 + 'px';
  }
});

// Add animation to heading text
const animateHeading = () => {
  const heading = document.querySelector('.animated-heading');
  if (heading) {
    heading.style.opacity = '1';
    heading.style.transform = 'translateY(0)';
  }

  const subheading = document.querySelector('.animated-subheading');
  if (subheading) {
    setTimeout(() => {
      subheading.style.opacity = '1';
      subheading.style.transform = 'translateY(0)';
    }, 500);
  }

  const heroButtons = document.querySelector('.hero-buttons');
  if (heroButtons) {
    setTimeout(() => {
      heroButtons.style.opacity = '1';
      heroButtons.style.transform = 'translateY(0)';
    }, 1000);
  }
};

// Run heading animation after a short delay
setTimeout(animateHeading, 300);
