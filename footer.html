  <!-- Footer -->
  <footer id="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-logo">
          <img src="images/vestigiaLogoBig.png" alt="Vestigia Logo">
          <p>Discover iconic cultural landmarks with ease</p>
        </div>
        <div class="footer-links">
          <div class="footer-column">
            <h4>Navigation</h4>
            <ul>
              <li><a href="index.html">Home</a></li>
              <li><a href="map.html">Map</a></li>
              <li><a href="itinerary.html">Itinerary</a></li>
              <li><a href="transportation.html">Transportation</a></li>
            </ul>
          </div>
          <div class="footer-column">
            <h4>Connect</h4>
            <div class="social-icons">
              <a href="#" class="social-icon"><i class="fa fa-facebook"></i></a>
              <a href="#" class="social-icon"><i class="fa fa-twitter"></i></a>
              <a href="#" class="social-icon"><i class="fa fa-instagram"></i></a>
              <a href="#" class="social-icon"><i class="fa fa-linkedin"></i></a>
            </div>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2025 Vestigia | All rights reserved.</p>
        <a href="#" id="back-to-top">Back to Top <i class="fa fa-chevron-up"></i></a>
      </div>
    </div>
  </footer>

  <script>
    // Back to top button functionality
    document.addEventListener('DOMContentLoaded', function() {
      const backToTopButton = document.getElementById('back-to-top');
      if (backToTopButton) {
        backToTopButton.addEventListener('click', (e) => {
          e.preventDefault();
          window.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        });
      }

      // Show/hide back to top button based on scroll position
      window.addEventListener('scroll', () => {
        if (backToTopButton) {
          if (window.scrollY > 300) {
            backToTopButton.style.opacity = '1';
          } else {
            backToTopButton.style.opacity = '0';
          }
        }
      });
    });
  </script>
