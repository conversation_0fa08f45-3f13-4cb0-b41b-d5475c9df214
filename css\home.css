/* Home Page Specific Styles */
:root {
  --dark-color: #262626;
  --main-color: #861818;
  --highlight-color: #ffd700;
  --light-color: #f5f5f5;
  --text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  --transition: all 0.3s ease;
}

/* General Styles for Home Page */
.home-page {
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.highlight {
  color: var(--highlight-color);
  text-shadow: var(--text-shadow);
}

.section-header {
  text-align: center;
  margin-bottom: 50px;
}

.section-header h2 {
  font-size: 2.8rem;
  font-family: 'Playfair Display', serif;
  margin-bottom: 15px;
  color: var(--highlight-color);
}

.section-header .subtitle {
  font-size: 1.2rem;
  color: var(--light-color);
  margin-bottom: 15px;
}

.section-header .description {
  font-size: 1.1rem;
  color: var(--light-color);
  max-width: 800px;
  margin: 0 auto;
}

.button {
  display: inline-block;
  padding: 12px 25px;
  border-radius: 30px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: var(--transition);
  cursor: pointer;
  text-decoration: none;
}

.primary-button {
  background-color: var(--highlight-color);
  color: var(--dark-color);
  box-shadow: var(--box-shadow);
}

.primary-button:hover {
  background-color: #e6c200;
  transform: translateY(-3px);
}

.secondary-button {
  background-color: transparent;
  color: var(--light-color);
  border: 2px solid var(--highlight-color);
  margin-left: 15px;
}

.secondary-button:hover {
  background-color: var(--highlight-color);
  color: var(--dark-color);
  transform: translateY(-3px);
}

.outline-button {
  background-color: transparent;
  color: var(--highlight-color);
  border: 2px solid var(--highlight-color);
}

.outline-button:hover {
  background-color: var(--highlight-color);
  color: var(--dark-color);
  transform: translateY(-3px);
}

.button.small {
  padding: 8px 15px;
  font-size: 0.9rem;
}

.button i {
  margin-left: 8px;
}

/* Hero Section */
#hero {
  height: 100vh;
  background-image: url('../images/vestigiaHomePicture.png');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  color: var(--light-color);
  text-align: center;
  padding-top: 60px;
  margin-top: 0;
}

#hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(38, 38, 38, 0.7), rgba(38, 38, 38, 0.9));
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  padding: 0 20px;
  text-align: center;
}

.animated-heading {
  font-size: 3.5rem;
  font-weight: 900;
  margin-bottom: 20px;
  text-shadow: var(--text-shadow);
  font-family: 'Playfair Display', serif;
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 1s ease, transform 1s ease;
}

.animated-subheading {
  font-size: 1.5rem;
  margin-bottom: 30px;
  text-shadow: var(--text-shadow);
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 1s ease, transform 1s ease;
}

.hero-buttons {
  margin-top: 30px;
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 1s ease, transform 1s ease;
}



.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.scroll-indicator span {
  font-size: 0.9rem;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.bounce {
  animation: bounce 2s infinite;
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-10px);
  }

  60% {
    transform: translateY(-5px);
  }
}

/* Welcome Section */
#welcome {
  background-color: var(--main-color);
  padding: 80px 0;
  text-align: center;
}

.welcome-content {
  max-width: 800px;
  margin: 0 auto;
}

.welcome-content h2 {
  font-size: 2.8rem;
  font-family: 'Playfair Display', serif;
  margin-bottom: 20px;
  color: var(--highlight-color);
}

.welcome-content p {
  font-size: 1.2rem;
  color: var(--light-color);
  margin-bottom: 30px;
  line-height: 1.6;
}

/* Features Section */
#features {
  padding: 100px 0;
  background-color: var(--dark-color);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 50px;
}

.feature-card {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--main-color), var(--highlight-color));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-icon-container {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: rgba(255, 215, 0, 0.1);
  transition: var(--transition);
}

.feature-card:hover .feature-icon-container {
  background-color: rgba(255, 215, 0, 0.2);
  transform: scale(1.1);
}

.feature-icon-container i {
  font-size: 40px;
  color: var(--highlight-color);
  transition: var(--transition);
}

.feature-card:hover .feature-icon-container i {
  transform: scale(1.1);
}

.feature-card h3 {
  font-size: 1.5rem;
  color: var(--highlight-color);
  margin-bottom: 15px;
}

.feature-card p {
  color: var(--light-color);
  margin-bottom: 20px;
  line-height: 1.6;
}

.feature-link {
  color: var(--highlight-color);
  text-decoration: none;
  font-weight: 600;
  display: inline-block;
  transition: var(--transition);
}

.feature-link i {
  margin-left: 5px;
  transition: var(--transition);
}

.feature-link:hover {
  color: #e6c200;
}

.feature-link:hover i {
  transform: translateX(5px);
}

/* Destinations Section */
#destinations {
  padding: 100px 0;
  background-color: var(--dark-color);
  position: relative;
}

.swiper-container {
  width: 100%;
  padding-bottom: 50px;
}

.destination-card {
  height: 400px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.destination-image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  position: relative;
  transition: var(--transition);
}

.destination-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 30px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: var(--light-color);
  transform: translateY(0);
  transition: var(--transition);
}

.destination-card:hover .destination-image {
  transform: scale(1.05);
}

.destination-card:hover .destination-overlay {
  transform: translateY(-10px);
}

.destination-overlay h3 {
  font-size: 1.8rem;
  margin-bottom: 5px;
  font-family: 'Playfair Display', serif;
}

.destination-overlay p {
  font-size: 1rem;
  margin-bottom: 15px;
  opacity: 0.8;
}

.swiper-button-next,
.swiper-button-prev {
  color: var(--highlight-color);
}

.swiper-pagination-bullet {
  background: var(--light-color);
  opacity: 0.5;
}

.swiper-pagination-bullet-active {
  background: var(--highlight-color);
  opacity: 1;
}

/* Testimonials Section */
#testimonials {
  padding: 100px 0;
  background-color: var(--main-color);
}

.testimonials-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
}

.testimonial-card {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 30px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.testimonial-card:hover {
  transform: translateY(-10px);
}

.testimonial-content {
  margin-bottom: 20px;
  position: relative;
}

.quote-icon {
  font-size: 2rem;
  color: var(--highlight-color);
  opacity: 0.3;
  position: absolute;
  top: -10px;
  left: -10px;
}

.testimonial-content p {
  color: var(--light-color);
  font-style: italic;
  line-height: 1.6;
  position: relative;
  z-index: 1;
}

.testimonial-author {
  display: flex;
  align-items: center;
}

.author-image {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 15px;
  border: 2px solid var(--highlight-color);
}

.author-info h4 {
  color: var(--highlight-color);
  margin-bottom: 5px;
}

.author-info p {
  color: var(--light-color);
  font-size: 0.9rem;
}

/* Newsletter Section */
#newsletter {
  padding: 80px 0;
  background-color: var(--dark-color);
}

.newsletter-content {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
}

.newsletter-content h2 {
  font-size: 2.5rem;
  color: var(--highlight-color);
  margin-bottom: 20px;
  font-family: 'Playfair Display', serif;
}

.newsletter-content p {
  color: var(--light-color);
  margin-bottom: 30px;
  font-size: 1.1rem;
}

.newsletter-form {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.newsletter-form input {
  flex: 1;
  padding: 15px 20px;
  border: none;
  border-radius: 30px 0 0 30px;
  font-size: 1rem;
  max-width: 400px;
}

.newsletter-form button {
  border-radius: 0 30px 30px 0;
  background-color: var(--highlight-color);
  color: var(--dark-color);
  border: none;
  padding: 0 25px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
}

.newsletter-form button:hover {
  background-color: #e6c200;
}

/* About Link Section */
#about-link {
  padding: 60px 0;
  background-color: var(--dark-color);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.about-link-content {
  text-align: center;
}

.about-link-content h3 {
  font-size: 1.8rem;
  color: var(--light-color);
  margin-bottom: 20px;
}

/* Footer */
#footer {
  background-color: #1a1a1a;
  color: var(--light-color);
  padding: 80px 0 20px;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 50px;
}

.footer-logo {
  flex: 1;
  min-width: 200px;
  margin-bottom: 30px;
}

.footer-logo img {
  height: 80px;
  margin-bottom: 15px;
}

.footer-logo p {
  color: #999;
  font-size: 0.9rem;
}

.footer-links {
  display: flex;
  flex-wrap: wrap;
  flex: 2;
  justify-content: space-around;
}

.footer-column {
  min-width: 150px;
  margin-bottom: 30px;
}

.footer-column h4 {
  color: var(--highlight-color);
  margin-bottom: 20px;
  font-size: 1.2rem;
}

.footer-column ul {
  list-style: none;
  padding: 0;
}

.footer-column ul li {
  margin-bottom: 10px;
}

.footer-column ul li a {
  color: #999;
  text-decoration: none;
  transition: var(--transition);
}

.footer-column ul li a:hover {
  color: var(--highlight-color);
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--light-color);
  transition: var(--transition);
}

.social-icon:hover {
  background-color: var(--highlight-color);
  color: var(--dark-color);
  transform: translateY(-3px);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
  color: #777;
  font-size: 0.9rem;
}

#back-to-top {
  color: var(--highlight-color);
  text-decoration: none;
  display: flex;
  align-items: center;
  transition: var(--transition);
}

#back-to-top i {
  margin-left: 5px;
}

#back-to-top:hover {
  transform: translateY(-3px);
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .hero-content {
    flex-direction: column;
    text-align: center;
  }

  .globe-container {
    margin-top: 50px;
  }

  .animated-heading {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .animated-heading {
    font-size: 2.5rem;
  }

  .animated-subheading {
    font-size: 1.2rem;
  }

  .hero-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .secondary-button {
    margin-left: 0;
    margin-top: 15px;
  }

  .globe-container {
    width: 300px;
    height: 300px;
  }

  .features-grid,
  .testimonials-container {
    grid-template-columns: 1fr;
  }

  .footer-content {
    flex-direction: column;
  }

  .footer-links {
    flex-direction: column;
  }

  .newsletter-form {
    flex-direction: column;
  }

  .newsletter-form input {
    border-radius: 30px;
    margin-bottom: 15px;
  }

  .newsletter-form button {
    border-radius: 30px;
    width: 100%;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }

  #back-to-top {
    margin-top: 15px;
  }
}

@media (max-width: 480px) {
  .animated-heading {
    font-size: 2rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .globe-container {
    width: 250px;
    height: 250px;
  }
}