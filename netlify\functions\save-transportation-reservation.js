// Netlify function for saving transportation reservations to Supabase
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://vwpyvjoycnrgmpatzqzk.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ3cHl2am95Y25yZ21wYXR6cXprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDExMzk0MTksImV4cCI6MjA1NjcxNTQxOX0.sG7amfnd8UTMv3zzmaa3KKyPRTzW70cDW9VVcUCSq4c';
const supabase = createClient(supabaseUrl, supabaseKey);

// CORS headers
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ message: 'Preflight request successful' })
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    // Parse request body
    const requestBody = JSON.parse(event.body);
    const {
      user_id,
      id,
      type,
      from,
      to,
      date,
      time,
      carrier,
      reference,
      notes,
      tripType
    } = requestBody;

    // Validate required parameters
    if (!user_id || !id || !type || !from || !to || !date) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing required parameters: user_id, id, type, from, to, date' })
      };
    }

    // Insert reservation into Supabase
    const { data, error } = await supabase
      .from('transportation_reservations')
      .insert([
        {
          user_id,
          reservation_id: id,
          type,
          from_location: from,
          to_location: to,
          departure_date: date,
          departure_time: time || null,
          carrier: carrier || null,
          reference: reference || null,
          notes: notes || null,
          trip_type: tripType || 'one-way'
        }
      ])
      .select();

    if (error) {
      console.error('Error saving transportation reservation to Supabase:', error);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({
          error: 'Failed to save transportation reservation',
          message: error.message,
          details: error
        })
      };
    }

    return {
      statusCode: 201,
      headers,
      body: JSON.stringify({
        success: true,
        message: 'Transportation reservation saved successfully',
        data
      })
    };
  } catch (error) {
    console.error('Error in save-transportation-reservation function:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Internal server error',
        message: error.message
      })
    };
  }
};
