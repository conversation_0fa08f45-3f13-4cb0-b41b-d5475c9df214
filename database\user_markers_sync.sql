-- SQL file for user_markers table with sync functionality

-- Create user_markers table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_markers (
  id SERIAL PRIMARY KEY,
  user_id TEXT NOT NULL,
  lat FLOAT NOT NULL,
  lng FLOAT NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  placeName TEXT,
  isPOI BOOLEAN DEFAULT FALSE,
  isCustom BOOLEAN DEFAULT FALSE,
  wikiName TEXT,
  addedToItinerary BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_user_markers_user_id ON user_markers(user_id);
CREATE INDEX IF NOT EXISTS idx_user_markers_location ON user_markers(lat, lng);

-- Enable Row Level Security
ALTER TABLE user_markers ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to select only their own markers
DROP POLICY IF EXISTS select_own_markers ON user_markers;
CREATE POLICY select_own_markers ON user_markers
  FOR SELECT USING (auth.uid()::text = user_id);

-- Create policy to allow users to insert only their own markers
DROP POLICY IF EXISTS insert_own_markers ON user_markers;
CREATE POLICY insert_own_markers ON user_markers
  FOR INSERT WITH CHECK (auth.uid()::text = user_id);

-- Create policy to allow users to update only their own markers
DROP POLICY IF EXISTS update_own_markers ON user_markers;
CREATE POLICY update_own_markers ON user_markers
  FOR UPDATE USING (auth.uid()::text = user_id);

-- Create policy to allow users to delete only their own markers
DROP POLICY IF EXISTS delete_own_markers ON user_markers;
CREATE POLICY delete_own_markers ON user_markers
  FOR DELETE USING (auth.uid()::text = user_id);

-- Create function to check for duplicate markers
CREATE OR REPLACE FUNCTION is_duplicate_marker(
  p_user_id TEXT,
  p_lat FLOAT,
  p_lng FLOAT,
  p_name TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  marker_exists BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1 FROM user_markers
    WHERE user_id = p_user_id
    AND ABS(lat - p_lat) < 0.0001
    AND ABS(lng - p_lng) < 0.0001
    AND name = p_name
  ) INTO marker_exists;
  
  RETURN marker_exists;
END;
$$ LANGUAGE plpgsql;

-- Create function to sync markers (upsert)
CREATE OR REPLACE FUNCTION sync_user_marker(
  p_user_id TEXT,
  p_lat FLOAT,
  p_lng FLOAT,
  p_name TEXT,
  p_description TEXT,
  p_placeName TEXT,
  p_isPOI BOOLEAN,
  p_isCustom BOOLEAN,
  p_wikiName TEXT,
  p_addedToItinerary BOOLEAN
) RETURNS JSONB AS $$
DECLARE
  marker_id INT;
  result JSONB;
BEGIN
  -- Check if marker exists (by approximate location and name)
  SELECT id INTO marker_id FROM user_markers
  WHERE user_id = p_user_id
  AND ABS(lat - p_lat) < 0.0001
  AND ABS(lng - p_lng) < 0.0001
  AND name = p_name;
  
  -- If marker exists, update it
  IF marker_id IS NOT NULL THEN
    UPDATE user_markers SET
      description = p_description,
      placeName = p_placeName,
      isPOI = p_isPOI,
      isCustom = p_isCustom,
      wikiName = p_wikiName,
      addedToItinerary = p_addedToItinerary,
      updated_at = NOW()
    WHERE id = marker_id
    RETURNING to_jsonb(user_markers.*) INTO result;
  -- Otherwise, insert new marker
  ELSE
    INSERT INTO user_markers (
      user_id, lat, lng, name, description, placeName, 
      isPOI, isCustom, wikiName, addedToItinerary
    ) VALUES (
      p_user_id, p_lat, p_lng, p_name, p_description, p_placeName,
      p_isPOI, p_isCustom, p_wikiName, p_addedToItinerary
    )
    RETURNING to_jsonb(user_markers.*) INTO result;
  END IF;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;
