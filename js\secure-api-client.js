/**
 * Secure API Client for Vestigia
 *
 * This file provides a client-side interface to the Netlify Functions
 * that protect our API keys.
 */

// API base URL configuration for Netlify Functions
const SECURE_API_CONFIG = {
  // Local development URL (when using Netlify CLI)
  development: 'http://localhost:8888/.netlify/functions',

  // Production URL (Netlify Functions)
  production: '/.netlify/functions'
};

// Determine which environment we're in
const secureApiIsProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';

// Select the appropriate API base URL
const SECURE_API_BASE_URL = secureApiIsProduction ? SECURE_API_CONFIG.production : SECURE_API_CONFIG.development;

console.log(`Using Secure API base URL: ${SECURE_API_BASE_URL} (${secureApiIsProduction ? 'production' : 'development'} mode)`);

/**
 * Secure API Client
 */
window.secureAPI = {
  map: {
    /**
     * Get directions from OpenRouteService
     * @param {string} start - Start coordinates (format: "lon,lat")
     * @param {string} end - End coordinates (format: "lon,lat")
     * @param {string} profile - Routing profile (e.g., "driving-car", "foot-walking")
     * @returns {Promise<Response>} Directions result
     */
    getDirections: async (start, end, profile = 'driving-car') => {
      try {
        console.log(`Getting directions from ${start} to ${end} via Netlify Function`);
        return fetch(`${SECURE_API_BASE_URL}/get-directions?start=${encodeURIComponent(start)}&end=${encodeURIComponent(end)}&profile=${profile}`);
      } catch (error) {
        console.error('Error getting directions:', error);
        throw error;
      }
    }
  }
};
