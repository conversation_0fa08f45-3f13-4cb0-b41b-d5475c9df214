/**
 * Mobile Menu Functionality
 * This script handles the mobile menu toggle functionality
 * Optimized for mobile devices
 */

// Function to initialize mobile menu
function initMobileMenu() {
  // Get the hamburger button, mobile menu, and close button
  const hamburgerBtn = document.querySelector('.hamburger-btn');
  const mobileMenu = document.querySelector('.mobile-menu');
  const closeBtn = document.querySelector('.close-btn');
  const mobileLinks = document.querySelectorAll('.mobile-links a');

  // If elements don't exist yet, try again in 100ms
  // This handles cases where the navbar is loaded dynamically
  if (!hamburgerBtn || !mobileMenu || !closeBtn) {
    console.log('Mobile menu elements not found, retrying in 100ms');
    setTimeout(initMobileMenu, 100);
    return;
  }

  console.log('Mobile menu elements found, initializing');

  // Function to open the mobile menu
  function openMobileMenu(e) {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    if (mobileMenu) {
      mobileMenu.classList.add('open');
      document.body.classList.add('overlay-open');
      console.log('Mobile menu opened');
    }
  }

  // Function to close the mobile menu
  function closeMobileMenu(e) {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    if (mobileMenu) {
      mobileMenu.classList.remove('open');
      document.body.classList.remove('overlay-open');
      console.log('Mobile menu closed');
    }
  }

  // Remove any existing event listeners to prevent duplicates
  const newHamburgerBtn = hamburgerBtn.cloneNode(true);
  hamburgerBtn.parentNode.replaceChild(newHamburgerBtn, hamburgerBtn);

  const newCloseBtn = closeBtn.cloneNode(true);
  closeBtn.parentNode.replaceChild(newCloseBtn, closeBtn);

  // Add event listeners to hamburger button
  newHamburgerBtn.addEventListener('click', openMobileMenu);
  newHamburgerBtn.addEventListener('touchstart', function (e) {
    e.preventDefault(); // Prevent default touch behavior
    e.stopPropagation();
  });
  newHamburgerBtn.addEventListener('touchend', openMobileMenu);

  // Add event listeners to close button
  newCloseBtn.addEventListener('click', closeMobileMenu);
  newCloseBtn.addEventListener('touchstart', function (e) {
    e.preventDefault(); // Prevent default touch behavior
    e.stopPropagation();
  });
  newCloseBtn.addEventListener('touchend', closeMobileMenu);

  // Close mobile menu when clicking outside
  document.addEventListener('click', function (e) {
    if (mobileMenu &&
      mobileMenu.classList.contains('open') &&
      !mobileMenu.contains(e.target) &&
      e.target !== newHamburgerBtn) {
      closeMobileMenu();
    }
  });

  // Add click event listeners to mobile menu links
  if (mobileLinks) {
    mobileLinks.forEach(function (link) {
      // Remove any existing event listeners to prevent duplicates
      const newLink = link.cloneNode(true);
      link.parentNode.replaceChild(newLink, link);

      // Add new event listeners that properly handle navigation
      newLink.addEventListener('click', function (e) {
        // Don't prevent default to allow navigation
        const href = newLink.getAttribute('href');
        if (href && href !== '#') {
          // Close the menu before navigating
          closeMobileMenu();
          // Small delay to ensure menu closes smoothly before navigation
          setTimeout(function () {
            window.location.href = href;
          }, 50);
        }
      });

      newLink.addEventListener('touchend', function (e) {
        // Don't prevent default to allow navigation
        const href = newLink.getAttribute('href');
        if (href && href !== '#') {
          // Close the menu before navigating
          closeMobileMenu();
          // Small delay to ensure menu closes smoothly before navigation
          setTimeout(function () {
            window.location.href = href;
          }, 50);
        }
      });
    });

    console.log('Mobile menu links initialized with navigation handlers');
  }

  // Prevent clicks inside the mobile menu from closing it
  if (mobileMenu) {
    mobileMenu.addEventListener('click', function (e) {
      e.stopPropagation();
    });

    mobileMenu.addEventListener('touchstart', function (e) {
      e.stopPropagation();
    });
  }

  console.log('Mobile menu initialized successfully');
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function () {
  // If navbar is loaded dynamically, wait for it
  if (document.getElementById('navbar-placeholder')) {
    // Check if navbar has been loaded
    const checkNavbar = setInterval(function () {
      if (document.querySelector('.hamburger-btn')) {
        clearInterval(checkNavbar);
        initMobileMenu();
      }
    }, 100);

    // Fallback in case navbar takes too long
    setTimeout(function () {
      clearInterval(checkNavbar);
      initMobileMenu();
    }, 3000);
  } else {
    // Navbar is directly in the page
    initMobileMenu();
  }
});
