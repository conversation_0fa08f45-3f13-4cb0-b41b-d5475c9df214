/* Additional styles for filter functionality */

/* Results count */
.results-count {
    background-color: #444;
    padding: 10px 15px;
    margin-bottom: 15px;
    border-radius: 6px;
    font-weight: 600;
    color: #ddd;
    border-left: 3px solid var(--main-color);
}

/* Time slider placeholder */
.time-slider-placeholder {
    background-color: #444;
    padding: 8px 12px;
    border-radius: 4px;
    text-align: center;
    color: #aaa;
    font-size: 13px;
    border: 1px dashed #555;
}

/* No results message */
.no-results {
    padding: 30px;
    text-align: center;
    background-color: #3a3a3a;
    border-radius: 8px;
    color: #aaa;
    font-size: 16px;
    margin: 20px 0;
}

/* Error message */
.error {
    padding: 20px;
    text-align: center;
    background-color: rgba(255, 0, 0, 0.1);
    border-radius: 8px;
    color: #ff6b6b;
    font-size: 16px;
    margin: 20px 0;
    border-left: 4px solid #ff6b6b;
}

/* Improve filter options styling */
.filter-option {
    padding: 6px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.filter-option:hover {
    background-color: #444;
}

.filter-option input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
}

/* Improve price slider */
.price-slider {
    margin: 10px 0 15px;
    padding: 0 10px;
}

.price-range {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    color: #bbb;
    font-size: 14px;
    padding: 0 2px;
}

#priceValue {
    font-weight: 600;
    color: var(--highlight-color);
}

input[type="range"] {
    -webkit-appearance: none;
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: linear-gradient(to right, var(--highlight-color) 0%, var(--highlight-color) 50%, #555 50%, #555 100%);
    outline: none;
    margin: 10px 0;
    position: relative;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--highlight-color);
    cursor: pointer;
    transition: all 0.2s;
    border: 2px solid #333;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    margin-top: -6px;
    /* Center the thumb vertically */
}

input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--highlight-color);
    cursor: pointer;
    transition: all 0.2s;
    border: 2px solid #333;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
}

input[type="range"]::-moz-range-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
}

input[type="range"]::-webkit-slider-runnable-track {
    height: 6px;
    border-radius: 3px;
    cursor: pointer;
}

input[type="range"]::-moz-range-track {
    height: 6px;
    border-radius: 3px;
    cursor: pointer;
}

/* Time filters */
.time-filter-group {
    margin-bottom: 15px;
}

.time-filter-group h6 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #bbb;
    font-weight: normal;
}

/* Time range options */
.time-ranges {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.time-range-option {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.time-range-option:hover {
    background-color: #444;
}

.time-range-option input[type="checkbox"] {
    margin-right: 8px;
    cursor: pointer;
}

.time-range-label {
    font-size: 13px;
    color: #ddd;
}