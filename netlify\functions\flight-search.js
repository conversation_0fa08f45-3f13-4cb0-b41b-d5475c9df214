/**
 * Flight Search API
 * 
 * This Netlify function handles flight search requests using the Travelpayouts API.
 */

const axios = require('axios');
const crypto = require('crypto');

// Travelpayouts API configuration
const TRAVELPAYOUTS_API_URL = 'https://api.travelpayouts.com/v1';
const TRAVELPAYOUTS_MARKER = process.env.AVIASALES_USER_ID;
const TRAVELPAYOUTS_TOKEN = process.env.AVIASALES_API_TOKEN;
const HOST = 'vestigiaweb.netlify.app';

// Headers for CORS and content type
const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Content-Type': 'application/json'
};

/**
 * Create signature for Travelpayouts API request
 * @param {Object} params - Request parameters
 * @returns {string} MD5 signature
 */
function createSignature(params) {
    // Sort parameters alphabetically
    const sortedParams = Object.keys(params)
        .sort()
        .reduce((acc, key) => {
            acc[key] = params[key];
            return acc;
        }, {});

    // Create signature string
    const valuesString = Object.values(sortedParams).join(':');
    const signatureString = TRAVELPAYOUTS_TOKEN + ':' + valuesString;

    // Generate MD5 hash
    return crypto.createHash('md5').update(signatureString).digest('hex');
}

/**
 * Initialize a flight search
 * @param {Object} params - Search parameters
 * @returns {Promise<Object>} Search response with search_id
 */
async function initializeSearch(params) {
    try {
        console.log('Initializing flight search with parameters:', params);

        // Validate required environment variables
        if (!TRAVELPAYOUTS_MARKER || !TRAVELPAYOUTS_TOKEN) {
            throw new Error('Missing required environment variables');
        }

        // Prepare request body
        const requestBody = {
            marker: TRAVELPAYOUTS_MARKER,
            host: HOST,
            user_ip: params.user_ip || '127.0.0.1',
            locale: params.locale || 'en',
            trip_class: params.trip_class || 'Y',
            passengers: {
                adults: parseInt(params.passengers?.adults || 1, 10),
                children: parseInt(params.passengers?.children || 0, 10),
                infants: parseInt(params.passengers?.infants || 0, 10)
            }
        };

        // Add segment parameters
        params.segments.forEach((segment, index) => {
            requestBody[`origin${index}`] = segment.origin;
            requestBody[`destination${index}`] = segment.destination;
            requestBody[`date${index}`] = segment.date;
        });

        // Generate signature
        requestBody.signature = createSignature(requestBody);

        console.log('Making API request to:', TRAVELPAYOUTS_API_URL + '/flight_search');
        console.log('Request body:', JSON.stringify(requestBody, null, 2));

        // Make the API request
        const response = await axios.post(TRAVELPAYOUTS_API_URL + '/flight_search', requestBody, {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'User-Agent': 'Vestigia/1.0'
            },
            timeout: 15000
        });

        console.log('Search initialized with ID:', response.data.search_id);
        return response.data;
    } catch (error) {
        console.error('Error initializing search:', error.response?.data || error.message);
        throw error;
    }
}

/**
 * Poll for search results
 * @param {string} searchId - Search ID
 * @returns {Promise<Object>} Search results
 */
async function pollResults(searchId) {
    try {
        console.log(`Polling for results with search ID: ${searchId}`);

        // Make the API request
        const response = await axios.get(`${TRAVELPAYOUTS_API_URL}/flight_search_results?uuid=${searchId}`, {
            headers: {
                'Accept-Encoding': 'gzip,deflate,sdch',
                'User-Agent': 'Vestigia/1.0'
            },
            timeout: 15000
        });

        // Check if we have only the search_id in the response
        if (response.data.length === 1 && response.data[0].search_id) {
            console.log('Search still in progress');
            return { status: 'searching', search_id: searchId };
        }

        // Process the results
        console.log('Search complete, processing results');
        const flightData = response.data[0] || {};

        return {
            status: 'complete',
            search_id: searchId,
            data: flightData.proposals || [],
            currency: flightData.currency,
            currency_rates: flightData.currency_rates,
            airlines: flightData.airlines,
            airports: flightData.airports,
            gates_info: flightData.gates_info
        };
    } catch (error) {
        console.error('Error polling for results:', error.response?.data || error.message);
        throw error;
    }
}

exports.handler = async (event, context) => {
    // Handle CORS preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        // Parse request body
        const body = JSON.parse(event.body || '{}');
        const { action, params, searchId } = body;

        // Handle different actions
        switch (action) {
            case 'startSearch':
                if (!params) {
                    throw new Error('Missing search parameters');
                }
                const searchData = await initializeSearch(params);
                return {
                    statusCode: 200,
                    headers,
                    body: JSON.stringify(searchData)
                };

            case 'getResults':
                if (!searchId) {
                    throw new Error('Missing search ID');
                }
                const results = await pollResults(searchId);
                return {
                    statusCode: 200,
                    headers,
                    body: JSON.stringify(results)
                };

            default:
                throw new Error('Invalid action');
        }
    } catch (error) {
        console.error('Error in flight-search function:', error);
        console.error('Error details:', {
            message: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            stack: error.stack
        });

        return {
            statusCode: error.response?.status || 500,
            headers,
            body: JSON.stringify({
                error: error.response?.data?.message || error.message || 'Internal server error',
                details: error.response?.data || 'No additional details available'
            })
        };
    }
}; 