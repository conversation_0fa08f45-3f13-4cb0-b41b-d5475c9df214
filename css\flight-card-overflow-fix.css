/**
 * Flight Card Overflow Fix
 * Fixes the issue with destination names leaking out from the right side of flight cards
 */

/* Ensure airport text doesn't overflow */
.airport {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

/* Set fixed width for departure and arrival containers */
.departure,
.arrival {
    min-width: 0; /* Allows flex items to shrink below content size */
    max-width: 120px; /* Limit maximum width */
    width: 120px; /* Set a fixed width */
}

/* Ensure flight duration takes remaining space */
.flight-duration {
    min-width: 0; /* Allows flex items to shrink below content size */
    flex-grow: 1; /* Allow this to take up remaining space */
}

/* Add container for flight times to ensure proper layout */
.flight-times {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .departure,
    .arrival {
        max-width: 100px;
        width: 100px;
    }
}

@media (max-width: 576px) {
    .departure,
    .arrival {
        max-width: 80px;
        width: 80px;
    }
}
