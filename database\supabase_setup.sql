-- Create user_markers table
CREATE TABLE user_markers (
  id SERIAL PRIMARY KEY,
  user_id TEXT NOT NULL,
  lat FLOAT NOT NULL,
  lng FLOAT NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  placeName TEXT,
  isPOI BOOLEAN DEFAULT FALSE,
  wikiName TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on user_id for faster queries
CREATE INDEX idx_user_markers_user_id ON user_markers(user_id);

-- Create index on lat/lng for faster spatial queries
CREATE INDEX idx_user_markers_location ON user_markers(lat, lng);

-- Create RLS (Row Level Security) policy to restrict access to user's own markers
ALTER TABLE user_markers ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to select only their own markers
CREATE POLICY select_own_markers ON user_markers
  FOR SELECT USING (auth.uid()::text = user_id);

-- Create policy to allow users to insert only their own markers
CREATE POLICY insert_own_markers ON user_markers
  FOR INSERT WITH CHECK (auth.uid()::text = user_id);

-- Create policy to allow users to update only their own markers
CREATE POLICY update_own_markers ON user_markers
  FOR UPDATE USING (auth.uid()::text = user_id);

-- Create policy to allow users to delete only their own markers
CREATE POLICY delete_own_markers ON user_markers
  FOR DELETE USING (auth.uid()::text = user_id);

-- Create poi_markers table for Points of Interest
CREATE TABLE poi_markers (
  id SERIAL PRIMARY KEY,
  lat FLOAT NOT NULL,
  lng FLOAT NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  wikiName TEXT,
  photos TEXT[], -- Array of photo URLs
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on lat/lng for faster spatial queries
CREATE INDEX idx_poi_markers_location ON poi_markers(lat, lng);

-- Insert sample POI markers
INSERT INTO poi_markers (lat, lng, name, description) VALUES
-- Italy POIs
(40.74846, 14.48305, 'Pompeii', 'Famous Roman city buried by ash and pumice after the eruption of Mt. Vesuvius.'),
(45.46418, 9.19068, 'Duomo di Milano', 'Magnificent Gothic Cathedral with ancient relics and statues.'),
(41.88825, 12.48658, 'Roman Forum', 'The Roman Empire''s ancient government ruins on Palatine Hill.'),
(41.8902, 12.4922, 'Colosseum', 'The world''s largest amphitheater, in Rome and one of the New Seven Wonders.'),
-- England POIs
(51.1789, -1.8262, 'Stonehenge', 'A prehistoric monument in Wiltshire.'),
-- USA POIs
(40.6892, -74.0445, 'Statue of Liberty', 'A colossal neoclassical sculpture on Liberty Island.'),
(29.89785, -81.31148, 'Castillo de San Marcos', 'Oldest masonry fort in the United States'),
-- Germany POIs
(52.52083, 13.29576, 'Charlottenburg Palace', 'A Baroque palace with beautiful gardens, one of the largest palaces in the world.'),
-- Brazil POIs
(-22.95192, -43.21047, 'Christ the Redeemer', 'The largest Art Deco-style sculpture in the world and one of the New Seven Wonders.'),
-- Mexican POIs
(20.68289, -88.56869, 'El Castillo', 'Ancient Mayan temple, worshipping the deity Kukulcan.'),
(20.68309, -88.57244, 'Chichén Itzá', 'Ancient Mayan city regarded as one of the New Seven Wonders of the World.');

-- Allow public access to POI markers (read-only)
ALTER TABLE poi_markers ENABLE ROW LEVEL SECURITY;
CREATE POLICY select_poi_markers ON poi_markers FOR SELECT USING (true);
