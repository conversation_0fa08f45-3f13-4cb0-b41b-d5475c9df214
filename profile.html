<!DOCTYPE html>
<html>

<head>
  <!-- Document metadata -->
  <title>My Profile - Vestigia</title>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />

  <!-- Core stylesheets -->
  <link rel="stylesheet" href="css/main.css">      <!-- Global styles -->
  <link rel="stylesheet" href="css/navbar.css">    <!-- Navigation bar styles -->
  <link rel="stylesheet" href="css/footer.css">    <!-- Footer styles -->

  <!-- External resources -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"> <!-- Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">      <!-- Additional icons -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Playfair+Display:wght@700;900&display=swap"> <!-- Fonts -->
  <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" /> <!-- Animation on scroll library -->

  <!-- Favicon -->
  <link rel="icon" href="images/vestigiaLogo.png" type="image/png">

  <style>
    :root {
      --dark-color: #262626;
      --main-color: #861818;
      --highlight-color: #ffd700;
      --light-color: #f5f5f5;
      --text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
      --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      --transition: all 0.3s ease;
    }

    body {
      background-color: var(--dark-color);
      color: var(--light-color);
      font-family: 'Open Sans', sans-serif;
    }

    .profile-container {
      max-width: 1200px;
      margin: 120px auto 50px;
      padding: 0 20px;
    }

    .profile-header {
      background: linear-gradient(135deg, var(--main-color), #a01d1d);
      border-radius: 15px;
      padding: 40px;
      margin-bottom: 40px;
      box-shadow: var(--box-shadow);
      position: relative;
      overflow: hidden;
    }

    .profile-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1));
      z-index: 1;
    }

    .profile-header-content {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      gap: 30px;
    }

    .profile-avatar {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      background: linear-gradient(135deg, var(--highlight-color), #e6c200);
      color: var(--dark-color);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 48px;
      font-weight: bold;
      box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
      border: 4px solid rgba(255, 215, 0, 0.2);
    }

    .profile-info h1 {
      margin: 0 0 10px 0;
      color: var(--light-color);
      font-size: 2.5rem;
      font-family: 'Playfair Display', serif;
    }

    .profile-info p {
      margin: 5px 0;
      color: rgba(245, 245, 245, 0.8);
      font-size: 1.1rem;
    }

    .profile-stats {
      display: flex;
      gap: 30px;
      margin-top: 20px;
    }

    .stat-item {
      text-align: center;
    }

    .stat-number {
      font-size: 1.8rem;
      font-weight: bold;
      color: var(--highlight-color);
      display: block;
    }

    .stat-label {
      font-size: 0.9rem;
      color: rgba(245, 245, 245, 0.7);
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .profile-sections {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 30px;
      margin-bottom: 40px;
    }

    .profile-section {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 15px;
      padding: 30px;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
      position: relative;
      overflow: hidden;
    }

    .profile-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: linear-gradient(to right, var(--main-color), var(--highlight-color));
      transform: scaleX(0);
      transform-origin: left;
      transition: transform 0.3s ease;
    }

    .profile-section:hover {
      transform: translateY(-5px);
      background: rgba(255, 255, 255, 0.08);
    }

    .profile-section:hover::before {
      transform: scaleX(1);
    }

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }

    .section-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: rgba(255, 215, 0, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
    }

    .section-icon i {
      font-size: 24px;
      color: var(--highlight-color);
    }

    .profile-section h2 {
      color: var(--highlight-color);
      margin: 0;
      font-size: 1.5rem;
      font-family: 'Playfair Display', serif;
    }

    .section-content {
      color: var(--light-color);
      line-height: 1.6;
    }

    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: rgba(245, 245, 245, 0.6);
    }

    .empty-state i {
      font-size: 48px;
      color: rgba(255, 215, 0, 0.3);
      margin-bottom: 15px;
    }

    .empty-state p {
      margin: 0;
      font-style: italic;
    }

    .item-list {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .list-item {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 10px;
      padding: 15px;
      border-left: 4px solid var(--highlight-color);
      transition: var(--transition);
    }

    .list-item:hover {
      background: rgba(255, 255, 255, 0.1);
      transform: translateX(5px);
    }

    .item-title {
      font-weight: 600;
      color: var(--highlight-color);
      margin-bottom: 5px;
    }

    .item-details {
      font-size: 0.9rem;
      color: rgba(245, 245, 245, 0.7);
    }

    .profile-actions {
      display: flex;
      gap: 15px;
      margin-top: 30px;
      flex-wrap: wrap;
    }

    .profile-button {
      padding: 12px 25px;
      border: none;
      border-radius: 30px;
      cursor: pointer;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      transition: var(--transition);
      font-size: 0.9rem;
    }

    .primary-button {
      background-color: var(--highlight-color);
      color: var(--dark-color);
      box-shadow: var(--box-shadow);
    }

    .primary-button:hover {
      background-color: #e6c200;
      transform: translateY(-3px);
    }

    .secondary-button {
      background-color: transparent;
      color: var(--light-color);
      border: 2px solid var(--highlight-color);
    }

    .secondary-button:hover {
      background-color: var(--highlight-color);
      color: var(--dark-color);
      transform: translateY(-3px);
    }

    .danger-button {
      background-color: transparent;
      color: #ff6b6b;
      border: 2px solid #ff6b6b;
    }

    .danger-button:hover {
      background-color: #ff6b6b;
      color: white;
      transform: translateY(-3px);
    }

    .not-logged-in {
      text-align: center;
      padding: 80px 20px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 15px;
      box-shadow: var(--box-shadow);
    }

    .not-logged-in h2 {
      color: var(--highlight-color);
      margin-bottom: 20px;
      font-size: 2.5rem;
      font-family: 'Playfair Display', serif;
    }

    .not-logged-in p {
      margin-bottom: 30px;
      color: var(--light-color);
      font-size: 1.1rem;
    }

    .login-button {
      display: inline-block;
      padding: 15px 35px;
      background-color: var(--highlight-color);
      color: var(--dark-color);
      text-decoration: none;
      border-radius: 30px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      transition: var(--transition);
      box-shadow: var(--box-shadow);
    }

    .login-button:hover {
      background-color: #e6c200;
      transform: translateY(-3px);
    }

    .loading-spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(255, 215, 0, 0.3);
      border-radius: 50%;
      border-top-color: var(--highlight-color);
      animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    .interests-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 10px;
      margin-top: 15px;
    }

    .interest-tag {
      background: rgba(255, 215, 0, 0.1);
      color: var(--highlight-color);
      padding: 8px 12px;
      border-radius: 20px;
      text-align: center;
      font-size: 0.9rem;
      border: 1px solid rgba(255, 215, 0, 0.3);
    }

    @media (max-width: 768px) {
      .profile-container {
        margin-top: 100px;
        padding: 0 15px;
      }

      .profile-header {
        padding: 30px 20px;
      }

      .profile-header-content {
        flex-direction: column;
        text-align: center;
        gap: 20px;
      }

      .profile-avatar {
        width: 100px;
        height: 100px;
        font-size: 40px;
      }

      .profile-info h1 {
        font-size: 2rem;
      }

      .profile-stats {
        justify-content: center;
        gap: 20px;
      }

      .profile-sections {
        grid-template-columns: 1fr;
        gap: 20px;
      }

      .profile-section {
        padding: 20px;
      }

      .profile-actions {
        flex-direction: column;
      }

      .interests-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      }
    }
  </style>
</head>

<body>
  <!-- Navigation Bar -->
  <div id="navbar-placeholder"></div>

  <!-- Main Content -->
  <div class="profile-container">
    <div id="profile-content">
      <!-- Content will be dynamically loaded based on login status -->
      <div class="not-logged-in">
        <h2>You are not logged in</h2>
        <p>Please log in to view your profile and access your saved locations, itineraries, and bookings.</p>
        <a href="auth-login.html" class="login-button">Log In</a>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <div id="footer-placeholder"></div>

  <!-- JavaScript Resources -->
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <script src="js/auth.js"></script>
  <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
  <script src="js/main.js"></script>
  <script src="js/mobile-menu.js"></script>
  <script>
    // Global variables for profile data
    let userMarkers = [];
    let userEvents = [];
    let userReservations = [];
    let profileStats = {
      savedLocations: 0,
      itineraries: 0,
      bookings: 0,
      placesVisited: 0
    };

    document.addEventListener('DOMContentLoaded', function() {
      // Load navbar and footer
      fetch('navbar.html')
        .then(response => response.text())
        .then(data => {
          document.getElementById('navbar-placeholder').innerHTML = data;
          // Initialize auth after navbar is loaded
          if (typeof auth !== 'undefined') {
            auth.init();
          }
        });

      fetch('footer-template.html')
        .then(response => response.text())
        .then(data => {
          document.getElementById('footer-placeholder').innerHTML = data;
        });

      // Check if user is logged in
      setTimeout(() => {
        if (auth && auth.isLoggedIn()) {
          loadUserProfile();
        }
      }, 500); // Small delay to ensure auth is initialized

      // Initialize AOS
      AOS.init();
    });

    async function loadUserProfile() {
      const user = auth.user;
      const profileContent = document.getElementById('profile-content');

      // Get first letter of name or email for avatar
      const nameInitial = user.user_metadata && user.user_metadata.full_name
        ? user.user_metadata.full_name.charAt(0).toUpperCase()
        : user.email.charAt(0).toUpperCase();

      // Get display name
      const displayName = user.user_metadata && user.user_metadata.full_name
        ? user.user_metadata.full_name
        : user.email;

      // Show loading state
      profileContent.innerHTML = `
        <div class="profile-header">
          <div class="profile-header-content">
            <div class="profile-avatar">
              ${nameInitial}
            </div>
            <div class="profile-info">
              <h1>${displayName}</h1>
              <p>${user.email}</p>
              <p>Member since ${new Date(user.created_at).toLocaleDateString()}</p>
              <div class="profile-stats">
                <div class="stat-item">
                  <span class="stat-number" id="saved-count">
                    <div class="loading-spinner"></div>
                  </span>
                  <span class="stat-label">Saved Places</span>
                </div>
                <div class="stat-item">
                  <span class="stat-number" id="itinerary-count">
                    <div class="loading-spinner"></div>
                  </span>
                  <span class="stat-label">Itineraries</span>
                </div>
                <div class="stat-item">
                  <span class="stat-number" id="booking-count">
                    <div class="loading-spinner"></div>
                  </span>
                  <span class="stat-label">Bookings</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="profile-sections">
          <!-- Interests Section -->
          <div class="profile-section">
            <div class="section-header">
              <div class="section-icon">
                <i class="fas fa-heart"></i>
              </div>
              <h2>Travel Interests</h2>
            </div>
            <div class="section-content">
              <div class="interests-grid">
                <div class="interest-tag">Cultural Sites</div>
                <div class="interest-tag">Museums</div>
                <div class="interest-tag">Architecture</div>
                <div class="interest-tag">History</div>
                <div class="interest-tag">Art Galleries</div>
                <div class="interest-tag">Landmarks</div>
              </div>
            </div>
          </div>

          <!-- Saved Locations Section -->
          <div class="profile-section">
            <div class="section-header">
              <div class="section-icon">
                <i class="fas fa-map-marker-alt"></i>
              </div>
              <h2>Saved Locations</h2>
            </div>
            <div class="section-content" id="saved-locations-content">
              <div class="loading-spinner"></div>
            </div>
          </div>

          <!-- Itineraries Section -->
          <div class="profile-section">
            <div class="section-header">
              <div class="section-icon">
                <i class="fas fa-calendar-alt"></i>
              </div>
              <h2>My Itineraries</h2>
            </div>
            <div class="section-content" id="itineraries-content">
              <div class="loading-spinner"></div>
            </div>
          </div>

          <!-- Bookings Section -->
          <div class="profile-section">
            <div class="section-header">
              <div class="section-icon">
                <i class="fas fa-plane"></i>
              </div>
              <h2>Transportation Bookings</h2>
            </div>
            <div class="section-content" id="bookings-content">
              <div class="loading-spinner"></div>
            </div>
          </div>

          <!-- Places Visited Section -->
          <div class="profile-section">
            <div class="section-header">
              <div class="section-icon">
                <i class="fas fa-globe"></i>
              </div>
              <h2>Places Visited</h2>
            </div>
            <div class="section-content">
              <div class="empty-state">
                <i class="fas fa-map"></i>
                <p>Track your visited places by marking them on the map</p>
              </div>
            </div>
          </div>

          <!-- Account Settings Section -->
          <div class="profile-section">
            <div class="section-header">
              <div class="section-icon">
                <i class="fas fa-cog"></i>
              </div>
              <h2>Account Settings</h2>
            </div>
            <div class="section-content">
              <div class="profile-actions">
                <button class="profile-button primary-button" id="change-password-btn">Change Password</button>
                <button class="profile-button secondary-button" id="edit-profile-btn">Edit Profile</button>
                <button class="profile-button danger-button" id="logout-btn">Logout</button>
              </div>
            </div>
          </div>
        </div>
      `;

      // Load user data
      await loadUserData();
      setupEventListeners();
    }

    async function loadUserData() {
      try {
        const userId = auth.user.id;

        // Load saved markers
        await loadSavedLocations(userId);

        // Load itineraries
        await loadItineraries(userId);

        // Load bookings
        await loadBookings(userId);

        // Update stats
        updateProfileStats();

      } catch (error) {
        console.error('Error loading user data:', error);
      }
    }

    async function loadSavedLocations(userId) {
      try {
        const response = await fetch('/.netlify/functions/sync-user-markers', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            user_id: userId,
            action: 'fetch'
          })
        });

        if (response.ok) {
          const data = await response.json();
          userMarkers = data.markers || [];
          displaySavedLocations();
        } else {
          throw new Error('Failed to load saved locations');
        }
      } catch (error) {
        console.error('Error loading saved locations:', error);
        document.getElementById('saved-locations-content').innerHTML = `
          <div class="empty-state">
            <i class="fas fa-map-marker-alt"></i>
            <p>No saved locations found</p>
          </div>
        `;
      }
    }

    async function loadItineraries(userId) {
      try {
        const response = await fetch('/.netlify/functions/sync-user-events', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            user_id: userId,
            action: 'fetch'
          })
        });

        if (response.ok) {
          const data = await response.json();
          userEvents = data.events || [];
          displayItineraries();
        } else {
          throw new Error('Failed to load itineraries');
        }
      } catch (error) {
        console.error('Error loading itineraries:', error);
        document.getElementById('itineraries-content').innerHTML = `
          <div class="empty-state">
            <i class="fas fa-calendar-alt"></i>
            <p>No itineraries found</p>
          </div>
        `;
      }
    }

    async function loadBookings(userId) {
      try {
        const response = await fetch('/.netlify/functions/sync-transportation-reservations', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            user_id: userId,
            action: 'fetch'
          })
        });

        if (response.ok) {
          const data = await response.json();
          userReservations = data.reservations || [];
          displayBookings();
        } else {
          throw new Error('Failed to load bookings');
        }
      } catch (error) {
        console.error('Error loading bookings:', error);
        document.getElementById('bookings-content').innerHTML = `
          <div class="empty-state">
            <i class="fas fa-plane"></i>
            <p>No bookings found</p>
          </div>
        `;
      }
    }

    function displaySavedLocations() {
      const content = document.getElementById('saved-locations-content');

      if (userMarkers.length === 0) {
        content.innerHTML = `
          <div class="empty-state">
            <i class="fas fa-map-marker-alt"></i>
            <p>No saved locations yet. Start exploring the map to save your favorite places!</p>
          </div>
        `;
        return;
      }

      const itemsHtml = userMarkers.slice(0, 5).map(marker => `
        <div class="list-item">
          <div class="item-title">${marker.name}</div>
          <div class="item-details">
            ${marker.description || 'No description'}
            ${marker.is_poi ? ' • Point of Interest' : ''}
            ${marker.is_custom ? ' • Custom Location' : ''}
          </div>
        </div>
      `).join('');

      content.innerHTML = `
        <div class="item-list">
          ${itemsHtml}
        </div>
        ${userMarkers.length > 5 ? `<p style="margin-top: 15px; text-align: center; color: rgba(245, 245, 245, 0.6);">And ${userMarkers.length - 5} more...</p>` : ''}
      `;
    }

    function displayItineraries() {
      const content = document.getElementById('itineraries-content');

      if (userEvents.length === 0) {
        content.innerHTML = `
          <div class="empty-state">
            <i class="fas fa-calendar-alt"></i>
            <p>No itineraries created yet. Plan your next adventure!</p>
          </div>
        `;
        return;
      }

      // Group events by date
      const eventsByDate = {};
      userEvents.forEach(event => {
        const date = new Date(event.start_time).toDateString();
        if (!eventsByDate[date]) {
          eventsByDate[date] = [];
        }
        eventsByDate[date].push(event);
      });

      const itemsHtml = Object.entries(eventsByDate).slice(0, 3).map(([date, events]) => `
        <div class="list-item">
          <div class="item-title">${date}</div>
          <div class="item-details">
            ${events.length} event${events.length > 1 ? 's' : ''}: ${events.map(e => e.title).join(', ')}
          </div>
        </div>
      `).join('');

      content.innerHTML = `
        <div class="item-list">
          ${itemsHtml}
        </div>
        ${Object.keys(eventsByDate).length > 3 ? `<p style="margin-top: 15px; text-align: center; color: rgba(245, 245, 245, 0.6);">And ${Object.keys(eventsByDate).length - 3} more days...</p>` : ''}
      `;
    }

    function displayBookings() {
      const content = document.getElementById('bookings-content');

      if (userReservations.length === 0) {
        content.innerHTML = `
          <div class="empty-state">
            <i class="fas fa-plane"></i>
            <p>No bookings yet. Search for flights and transportation!</p>
          </div>
        `;
        return;
      }

      const itemsHtml = userReservations.slice(0, 5).map(reservation => `
        <div class="list-item">
          <div class="item-title">${reservation.from_location} → ${reservation.to_location}</div>
          <div class="item-details">
            ${reservation.type.charAt(0).toUpperCase() + reservation.type.slice(1)} • ${new Date(reservation.departure_date).toLocaleDateString()}
            ${reservation.carrier ? ` • ${reservation.carrier}` : ''}
          </div>
        </div>
      `).join('');

      content.innerHTML = `
        <div class="item-list">
          ${itemsHtml}
        </div>
        ${userReservations.length > 5 ? `<p style="margin-top: 15px; text-align: center; color: rgba(245, 245, 245, 0.6);">And ${userReservations.length - 5} more...</p>` : ''}
      `;
    }

    function updateProfileStats() {
      profileStats.savedLocations = userMarkers.length;
      profileStats.itineraries = new Set(userEvents.map(e => new Date(e.start_time).toDateString())).size;
      profileStats.bookings = userReservations.length;

      document.getElementById('saved-count').textContent = profileStats.savedLocations;
      document.getElementById('itinerary-count').textContent = profileStats.itineraries;
      document.getElementById('booking-count').textContent = profileStats.bookings;
    }

    function setupEventListeners() {
      // Change password button
      document.getElementById('change-password-btn').addEventListener('click', showPasswordChangeModal);

      // Edit profile button
      document.getElementById('edit-profile-btn').addEventListener('click', function() {
        alert('Profile editing feature coming soon!');
      });

      // Logout button
      document.getElementById('logout-btn').addEventListener('click', function() {
        if (confirm('Are you sure you want to logout?')) {
          auth.logout();
          window.location.href = 'index.html';
        }
      });
    }

    function showPasswordChangeModal() {
      // Create password change modal
      const overlay = document.createElement('div');
      overlay.style.position = 'fixed';
      overlay.style.top = '0';
      overlay.style.left = '0';
      overlay.style.width = '100%';
      overlay.style.height = '100%';
      overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
      overlay.style.zIndex = '999';
      overlay.style.display = 'flex';
      overlay.style.alignItems = 'center';
      overlay.style.justifyContent = 'center';
      document.body.appendChild(overlay);

      const popup = document.createElement('div');
      popup.style.backgroundColor = 'var(--dark-color)';
      popup.style.padding = '30px';
      popup.style.borderRadius = '15px';
      popup.style.boxShadow = '0 20px 60px rgba(0, 0, 0, 0.5)';
      popup.style.width = '400px';
      popup.style.maxWidth = '90%';
      popup.style.border = '1px solid rgba(255, 215, 0, 0.2)';
      popup.innerHTML = `
        <h2 style="color: var(--highlight-color); margin-bottom: 20px; font-family: 'Playfair Display', serif;">Change Password</h2>
        <div id="password-message" style="margin-bottom: 15px;"></div>
        <div style="margin-bottom: 15px;">
          <label for="new-password" style="display: block; margin-bottom: 5px; color: var(--light-color);">New Password</label>
          <input type="password" id="new-password" style="width: 100%; padding: 12px; border: 1px solid rgba(255, 215, 0, 0.3); border-radius: 8px; box-sizing: border-box; background: rgba(255, 255, 255, 0.05); color: var(--light-color);">
        </div>
        <div style="margin-bottom: 20px;">
          <label for="confirm-new-password" style="display: block; margin-bottom: 5px; color: var(--light-color);">Confirm New Password</label>
          <input type="password" id="confirm-new-password" style="width: 100%; padding: 12px; border: 1px solid rgba(255, 215, 0, 0.3); border-radius: 8px; box-sizing: border-box; background: rgba(255, 255, 255, 0.05); color: var(--light-color);">
        </div>
        <div style="display: flex; justify-content: flex-end; gap: 15px;">
          <button id="cancel-password-change" class="profile-button secondary-button">Cancel</button>
          <button id="save-password-change" class="profile-button primary-button">Save</button>
        </div>
      `;
      overlay.appendChild(popup);

      // Handle cancel button
      document.getElementById('cancel-password-change').addEventListener('click', function() {
        document.body.removeChild(overlay);
      });

      // Handle save button
      document.getElementById('save-password-change').addEventListener('click', async function() {
        const newPassword = document.getElementById('new-password').value;
        const confirmNewPassword = document.getElementById('confirm-new-password').value;
        const passwordMessage = document.getElementById('password-message');

        // Validate passwords
        if (!newPassword) {
          passwordMessage.innerHTML = '<div style="color: #ff6b6b;">Please enter a new password</div>';
          return;
        }

        if (newPassword.length < 6) {
          passwordMessage.innerHTML = '<div style="color: #ff6b6b;">Password must be at least 6 characters long</div>';
          return;
        }

        if (newPassword !== confirmNewPassword) {
          passwordMessage.innerHTML = '<div style="color: #ff6b6b;">Passwords do not match</div>';
          return;
        }

        try {
          passwordMessage.innerHTML = '<div style="color: var(--highlight-color);">Updating password...</div>';

          // Update password
          const { error } = await supabase.auth.updateUser({
            password: newPassword
          });

          if (error) {
            throw new Error(error.message);
          }

          passwordMessage.innerHTML = '<div style="color: #4CAF50;">Password updated successfully!</div>';

          // Close the modal after 2 seconds
          setTimeout(() => {
            document.body.removeChild(overlay);
          }, 2000);
        } catch (error) {
          passwordMessage.innerHTML = `<div style="color: #ff6b6b;">${error.message || 'Failed to update password'}</div>`;
        }
      });

      // Close modal when clicking outside
      overlay.addEventListener('click', function(e) {
        if (e.target === overlay) {
          document.body.removeChild(overlay);
        }
      });
    }
  </script>
</body>

</html>
