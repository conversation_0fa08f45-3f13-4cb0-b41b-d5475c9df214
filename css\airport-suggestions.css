-/* Modern styling for airport suggestions */

/* Suggestions container */
.suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    max-height: 320px;
    overflow-y: auto;
    background-color: #2a2a2a;
    border: 2px solid var(--main-color);
    border-radius: 0 0 12px 12px;
    z-index: 1000;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(134, 24, 24, 0.1);
    margin-top: 4px;
    backdrop-filter: blur(5px);
    transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
}

/* Suggestions display controlled by JavaScript */
.suggestions {
    display: none;
    opacity: 0;
    transform: translateY(-5px);
}

/* Class for hidden suggestions with very low z-index */
.suggestions.hidden-suggestions {
    z-index: -1 !important;
}

/* Only show suggestions when explicitly set by JavaScript */
.suggestions[style*="display: block"] {
    display: block !important;
    opacity: 1 !important;
    transform: translateY(0) !important;
}

/* Suggestion item */
.suggestion-item {
    padding: 12px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #3a3a3a;
    transition: all 0.2s ease;
    background-color: #2a2a2a;
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.3s ease-out forwards;
    opacity: 0;
    transform: translateY(10px);
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }

    70% {
        opacity: 0.8;
        transform: translateY(-2px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.suggestion-item:last-child {
    border-bottom: none;
    position: relative;
}

.suggestion-item:last-child::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, var(--main-color), var(--highlight-color), transparent);
    opacity: 0.7;
    z-index: 1;
}

.suggestion-item:hover {
    background-color: #3a3a3a;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 2;
}

.suggestion-item:hover::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: linear-gradient(to bottom, var(--main-color), var(--highlight-color));
}

/* Airport code badge */
.suggestion-code {
    font-weight: bold;
    color: #262626;
    background: linear-gradient(135deg, var(--highlight-color), #e6c200);
    padding: 6px 10px;
    border-radius: 6px;
    min-width: 42px;
    text-align: center;
    font-size: 15px;
    margin-right: 16px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 215, 0, 0.3);
}

/* Airport details container */
.suggestion-details {
    flex: 1;
    min-width: 0;
    max-width: calc(100% - 70px);
}

/* Airport name */
.suggestion-name {
    font-weight: 600;
    color: #fff;
    margin-bottom: 6px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 15px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Airport location */
.suggestion-location {
    font-size: 13px;
    color: #ccc;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    opacity: 0.9;
    letter-spacing: 0.2px;
}

/* Loading state */
.suggestion-loading {
    padding: 18px;
    text-align: center;
    color: #eee;
    font-size: 15px;
    background-color: #2a2a2a;
    position: relative;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.suggestion-loading::before {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    border-top-color: var(--highlight-color);
    border-right-color: var(--main-color);
    animation: spin 1s ease-in-out infinite;
    margin-right: 10px;
    vertical-align: middle;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Error state */
.suggestion-error {
    padding: 16px;
    text-align: center;
    color: #ff6b6b;
    background-color: rgba(134, 24, 24, 0.2);
    border-left: 4px solid var(--main-color);
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0.3px;
}

/* No results state */
.suggestion-no-results {
    padding: 16px;
    text-align: center;
    color: var(--light-color);
    font-style: italic;
    background-color: #2a2a2a;
    font-size: 14px;
    letter-spacing: 0.3px;
    border-bottom: 1px solid var(--main-color);
}

/* Scrollbar styling */
.suggestions::-webkit-scrollbar {
    width: 10px;
}

.suggestions::-webkit-scrollbar-track {
    background: #222;
    border-radius: 0 0 12px 0;
}

.suggestions::-webkit-scrollbar-thumb {
    background: #444;
    border-radius: 5px;
    border: 2px solid #222;
    background-image: linear-gradient(to bottom, var(--main-color), #444);
}

.suggestions::-webkit-scrollbar-thumb:hover {
    background: var(--highlight-color);
    background-image: linear-gradient(to bottom, var(--main-color), var(--highlight-color));
}

/* Fix for location fields */
.location-field {
    position: relative;
}

/* Fix for modal suggestions */
.modal-body .form-group {
    position: relative;
}

/* Ensure suggestions appear on top of other elements */
body .suggestions {
    z-index: 9999;
}

/* Styling for suggestion header */
.suggestion-header {
    padding: 12px 16px;
    background: linear-gradient(to right, #333, var(--main-color));
    color: var(--highlight-color);
    font-weight: bold;
    font-size: 14px;
    border-bottom: 2px solid var(--highlight-color);
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Fix for external reservation modal suggestions */
#externalReservationModal .suggestions {
    max-height: 220px;
    width: 100%;
}

/* Focused suggestion item */
.suggestion-item.focused {
    background-color: #3a3a3a !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 2;
}

.suggestion-item.focused::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background: linear-gradient(to bottom, var(--main-color), var(--highlight-color));
}

/* Ensure suggestions are visible and clickable */
.suggestions {
    position: absolute !important;
    z-index: 9999 !important;
    background-color: #2a2a2a !important;
    border: 2px solid var(--main-color) !important;
    border-radius: 0 0 12px 12px !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4) !important, 0 0 0 1px rgba(134, 24, 24, 0.1) !important;
    max-height: 320px !important;
    overflow-y: auto !important;
    width: 100% !important;
}