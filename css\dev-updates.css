/* Development Updates Signup Page Styles */
:root {
  --dark-color: #262626;
  --main-color: #861818;
  --highlight-color: #ffd700;
  --light-color: #f5f5f5;
  --text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  --transition: all 0.3s ease;
}

.dev-updates-page {
  background-color: var(--dark-color);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Back to Home Button */
.back-to-home {
  position: fixed;
  top: 80px;
  left: 20px;
  z-index: 100;
}

.back-button {
  display: inline-flex;
  align-items: center;
  padding: 10px 15px;
  background-color: var(--main-color);
  color: var(--highlight-color);
  border-radius: 30px;
  text-decoration: none;
  font-weight: 600;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  margin-top: 50px;
}

.back-button i {
  margin-right: 8px;
}

.back-button:hover {
  background-color: #9a1c1c;
  transform: translateX(-5px);
}

.dev-updates-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 80px 20px;
  flex: 1;
}

.dev-updates-header {
  text-align: center;
  margin-bottom: 50px;
}

.dev-updates-header h1 {
  font-size: 3rem;
  font-family: 'Playfair Display', serif;
  color: var(--highlight-color);
  margin-bottom: 20px;
  text-shadow: var(--text-shadow);
}

.dev-updates-header p {
  font-size: 1.2rem;
  color: var(--light-color);
  max-width: 800px;
  margin: 0 auto 30px;
  line-height: 1.6;
}

.signup-form-container {
  max-width: 600px;
  margin: 0 auto;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 40px;
  box-shadow: var(--box-shadow);
}

.signup-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 1.1rem;
  color: var(--highlight-color);
  font-weight: 600;
}

.form-group input {
  padding: 12px 15px;
  border-radius: 8px;
  border: 2px solid transparent;
  background-color: rgba(134, 24, 24, 0.8);
  color: var(--light-color);
  font-size: 1rem;
  transition: var(--transition);
}

.form-group input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.form-group input:focus {
  outline: none;
  border-color: var(--highlight-color);
  background-color: rgba(134, 24, 24, 1);
}

.form-group input:hover {
  background-color: rgba(134, 24, 24, 1);
}

.submit-btn {
  background-color: var(--highlight-color);
  color: var(--dark-color);
  border: none;
  padding: 14px 20px;
  border-radius: 30px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-top: 10px;
  box-shadow: var(--box-shadow);
}

.submit-btn:hover {
  background-color: #e6c200;
  transform: translateY(-3px);
}

.submit-btn:active {
  transform: translateY(1px);
}

.form-message {
  padding: 15px;
  border-radius: 8px;
  margin-top: 20px;
  text-align: center;
  font-weight: 600;
  display: none;
}

.success-message {
  background-color: rgba(39, 174, 96, 0.2);
  color: #2ecc71;
  border: 1px solid #27ae60;
}

.error-message {
  background-color: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
  border: 1px solid #c0392b;
}

.features-section {
  margin-top: 80px;
}

.features-header {
  text-align: center;
  margin-bottom: 40px;
}

.features-header h2 {
  font-size: 2.5rem;
  font-family: 'Playfair Display', serif;
  color: var(--highlight-color);
  margin-bottom: 15px;
  text-shadow: var(--text-shadow);
}

.features-header p {
  font-size: 1.1rem;
  color: var(--light-color);
  max-width: 800px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.feature-card {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  transition: var(--transition);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--box-shadow);
}

.feature-icon {
  font-size: 2.5rem;
  color: var(--highlight-color);
  margin-bottom: 20px;
}

.feature-title {
  font-size: 1.5rem;
  color: var(--highlight-color);
  margin-bottom: 15px;
  font-weight: 600;
}

.feature-description {
  font-size: 1rem;
  color: var(--light-color);
  line-height: 1.6;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .dev-updates-header h1 {
    font-size: 2.5rem;
  }

  .signup-form-container {
    padding: 30px 20px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .back-to-home {
    top: 70px;
    left: 10px;
  }

  .back-button {
    padding: 8px 12px;
    font-size: 0.9rem;
  }
}