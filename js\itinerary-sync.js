/**
 * Itinerary Sync Module for Vestigia
 * 
 * This module provides functions to synchronize itinerary events between
 * the local browser storage and Supabase database.
 */

// Wrap everything in an IIFE to avoid global variable conflicts
(function() {
  // Local storage key for events - make it available globally for other scripts to access
  window.EVENTS_STORAGE_KEY = 'vestigia_calendar_events';
  
  // Sync events with Supabase - expose this function globally
  window.syncEventsWithSupabase = async function(events) {
  try {
    console.log('Starting syncEventsWithSupabase with', events.length, 'events');
    
    // First check if we have a session
    const { data: { session } } = await supabase.auth.getSession();
    console.log('Session check result:', session ? 'Session found' : 'No session');
    
    if (!session) {
      console.log('No active session, checking for user...');
      // If no session, try to get the user
      const { data: { user }, error } = await supabase.auth.getUser();
      console.log('User check result:', user ? `User found: ${user.id}` : 'No user', error ? `Error: ${error.message}` : 'No error');
      
      if (error || !user) {
        console.log('User not logged in, skipping Supabase sync', error);
        return { success: false, error: 'User not authenticated' };
      }
    }
    
    // Now get the current user with the session
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.log('Could not get user after session check');
      return { success: false, error: 'User not found' };
    }

    console.log('Syncing events with Supabase for user:', user.id);
    
    // Use the itineraryAPI client to sync events
    try {
      const result = await itineraryAPI.syncUserEvents(user.id, events);
      console.log('Events synced with Supabase:', result);
      return { success: true, data: result };
    } catch (apiError) {
      console.error('Error in API call for syncing events:', apiError);
      return { success: false, error: apiError.message };
    }
  } catch (error) {
    console.error('Error during Supabase sync:', error);
    return { success: false, error: error.message };
  }
}

// Load events from Supabase
async function loadEventsFromSupabase() {
  try {
    console.log('Starting loadEventsFromSupabase...');
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.log('User not logged in, cannot load from Supabase');
      return [];
    }

    console.log('Loading events from Supabase for user:', user.id);
    
    // Get the API base URL from the itineraryAPI object if available
    const apiBaseUrl = window.itineraryAPI ? window.itineraryAPI.getBaseUrl() : '/api';
    console.log('Using API endpoint:', `${apiBaseUrl}/get-user-events`);
    
    const response = await fetch(`${apiBaseUrl}/get-user-events?user_id=${user.id}`, {
      headers: {
        'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
      }
    });
    
    console.log('Get events response status:', response.status, response.statusText);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error fetching events from Supabase:', response.status, response.statusText);
      console.error('Error details:', errorText);
      return [];
    }
    
    const data = await response.json();
    console.log(`Retrieved ${data.events?.length || 0} events from Supabase`);
    console.log('Raw events data:', data);
    
    // Convert Supabase events to our format
    const formattedEvents = (data.events || []).map(event => ({
      id: event.event_id,
      title: event.title,
      start: event.start_time,
      end: event.end_time,
      description: event.description,
      location: event.location,
      category: event.category || 'other'
    }));
    
    console.log('Formatted events for calendar:', formattedEvents);
    return formattedEvents;
  } catch (error) {
    console.error('Error loading from Supabase:', error);
    return [];
  }
}

// Merge local and remote events
function mergeEvents(localEvents, remoteEvents) {
  const merged = [...localEvents];
  const localEventIds = new Set(localEvents.map(e => e.id));
  
  // Add remote events that don't exist locally
  for (const remoteEvent of remoteEvents) {
    if (!localEventIds.has(remoteEvent.id)) {
      merged.push(remoteEvent);
    }
  }
  
  return merged;
}

// Save events to local storage
function saveEventsToLocalStorage(events) {
  localStorage.setItem(EVENTS_STORAGE_KEY, JSON.stringify(events));
  console.log(`Saved ${events.length} events to local storage`);
}

// Load events from local storage
function loadEventsFromLocalStorage() {
  const storedEvents = localStorage.getItem(EVENTS_STORAGE_KEY);
  if (!storedEvents) {
    return [];
  }
  
  try {
    return JSON.parse(storedEvents);
  } catch (error) {
    console.error('Error parsing stored events:', error);
    return [];
  }
}

// Initialize event syncing
async function initEventSync(calendar) {
  console.log('Starting initEventSync...');
  // Check if user is authenticated
  const { data: { user } } = await supabase.auth.getUser();
  const isAuthenticated = !!user;
  console.log('Authentication check:', isAuthenticated ? `User authenticated: ${user.id}` : 'No authenticated user');
  
  // Load local events
  let localEvents = loadEventsFromLocalStorage();
  console.log(`Loaded ${localEvents.length} events from local storage`);
  
  // If authenticated, load and merge remote events
  if (isAuthenticated) {
    try {
      console.log('Attempting to load events from Supabase...');
      const remoteEvents = await loadEventsFromSupabase();
      console.log(`Loaded ${remoteEvents.length} events from Supabase`);
      
      // Merge events
      const mergedEvents = mergeEvents(localEvents, remoteEvents);
      console.log(`Merged into ${mergedEvents.length} total events`);
      
      // Update local storage with merged events
      saveEventsToLocalStorage(mergedEvents);
      console.log('Saved merged events to local storage');
      
      // Sync merged events back to Supabase
      console.log('Attempting to sync merged events back to Supabase...');
      const syncResult = await syncEventsWithSupabase(mergedEvents);
      console.log('Sync result:', syncResult);
      
      // Update calendar with merged events
      if (calendar) {
        calendar.removeAllEvents();
        calendar.addEventSource(mergedEvents);
        console.log('Updated calendar with merged events');
      }
      
      return mergedEvents;
    } catch (error) {
      console.error('Error during event sync initialization:', error);
      
      // Fall back to local events if sync fails
      if (calendar) {
        calendar.removeAllEvents();
        calendar.addEventSource(localEvents);
        console.log('Updated calendar with local events (sync failed)');
      }
      
      return localEvents;
    }
  } else {
    // Not authenticated, just use local events
    console.log('User not authenticated, using local events only');
    
    if (calendar) {
      calendar.removeAllEvents();
      calendar.addEventSource(localEvents);
      console.log('Updated calendar with local events');
    }
    
    return localEvents;
  }
}

// Handle event changes (add, update, delete)
async function handleEventChange(action, event, calendar) {
  // Get current events
  let events = loadEventsFromLocalStorage();
  
  switch (action) {
    case 'add':
      // Add new event
      events.push(event);
      break;
    case 'update':
      // Update existing event
      events = events.map(e => e.id === event.id ? event : e);
      break;
    case 'delete':
      // Delete event
      events = events.filter(e => e.id !== event.id);
      break;
  }
  
  // Save to local storage
  saveEventsToLocalStorage(events);
  
  // Sync with Supabase if authenticated
  const { data: { user } } = await supabase.auth.getUser();
  if (user) {
    await syncEventsWithSupabase(events);
  }
  
  // Update calendar if provided
  if (calendar) {
    calendar.removeAllEvents();
    calendar.addEventSource(events);
  }
  
  return events;
}

  // Simple initialization function to match what the HTML file is expecting
  async function init() {
    console.log('Initializing itinerarySync module...');
    return Promise.resolve(true);
  }

  // Export functions for use in other modules
  window.itinerarySync = {
    init,
    initEventSync,
    handleEventChange,
    syncEventsWithSupabase,
    loadEventsFromSupabase,
    saveEventsToLocalStorage,
    loadEventsFromLocalStorage
  };
  
  console.log('Itinerary Sync module loaded and ready to use');
  
})(); // Close the IIFE
