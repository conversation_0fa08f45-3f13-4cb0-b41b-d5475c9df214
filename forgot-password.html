<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Vestigia</title>
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Playfair+Display:wght@700;900&display=swap">
    <link rel="icon" type="image/png" sizes="96x96" href="favicon/favicon-96x96.png">
    <link rel="icon" href="favicon/favicon.ico">
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        html {
            scroll-behavior: smooth;
        }
        
        body {
            overflow-x: hidden;
        }
    </style>
</head>
<body class="auth-page">
    <!-- Navbar will be injected here by navbar-loader.js -->
    <div id="navbar-container"></div>
    <div class="auth-overlay"></div>

    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header" style="background: transparent;">
                <img src="images/vestigiaLogoBig.png" alt="Vestigia Logo" class="auth-logo" style="background: #262626; padding: 10px; border-radius: 8px;">
                <h1>Reset Password</h1>
                <p>Enter your email to receive a password reset link</p>
            </div>
            
            <form class="auth-form" id="resetPasswordForm">
                <div id="error-message" class="error-message"></div>
                <div id="success-message" class="success-message" style="display: none;"></div>
                
                <div class="auth-form-content">
                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <div class="input-with-icon">
                            <i class="fas fa-envelope"></i>
                            <input type="email" id="email" name="email" placeholder="Enter your email" required>
                        </div>
                    </div>

                    <button type="submit" class="auth-button">
                        Send Reset Link
                        <i class="fas fa-arrow-right"></i>
                    </button>
                </div>

                <div class="auth-footer">
                    <p>Remembered your password? <a href="auth-login.html">Sign In</a></p>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="js/supabase-config.js"></script>
    <script src="js/navbar-loader.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const resetPasswordForm = document.getElementById('resetPasswordForm');
            const errorMessage = document.getElementById('error-message');
            const successMessage = document.getElementById('success-message');

            resetPasswordForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const email = document.getElementById('email').value.trim();
                errorMessage.textContent = '';
                errorMessage.style.display = 'none';
                successMessage.style.display = 'none';

                try {
                    const { error } = await supabase.auth.resetPasswordForEmail(email, {
                        redirectTo: window.location.origin + '/update-password.html'
                    });

                    if (error) throw error;

                    // Show success message
                    successMessage.textContent = 'Password reset link sent! Please check your email.';
                    successMessage.style.display = 'block';
                    resetPasswordForm.reset();
                } catch (error) {
                    console.error('Error:', error);
                    errorMessage.textContent = error.message || 'An error occurred. Please try again.';
                    errorMessage.style.display = 'block';
                }
            });
        });
    </script>
</body>
</html>
