/* Date Picker Fixes */

/* Basic styling for flatpickr calendar */
.flatpickr-calendar {
    z-index: 9999 !important;
    /* Ensure it appears above all elements */
    box-shadow: 0 3px 13px rgba(0, 0, 0, 0.3) !important;
    /* Add shadow for better visibility */
    border: 1px solid var(--main-color) !important;
    /* Add border to match site theme */
    background-color: #333 !important;
    /* Dark background to match site theme */
}

/* Ensure the date picker container is positioned relatively */
.date-field,
.form-group {
    position: relative;
}

/* Style the calendar to match the site theme */
.flatpickr-day {
    color: #eee !important;
    border-radius: 4px !important;
}

.flatpickr-day.selected {
    background-color: var(--main-color) !important;
    border-color: var(--main-color) !important;
}

.flatpickr-day:hover {
    background-color: #555 !important;
}

.flatpickr-day.today {
    border-color: var(--main-color) !important;
}

/* Style the month navigation */
.flatpickr-months {
    background-color: #222 !important;
    border-bottom: 1px solid var(--main-color) !important;
}

.flatpickr-month {
    color: #eee !important;
}

.flatpickr-weekdays {
    background-color: #222 !important;
}

.flatpickr-weekday {
    color: #ccc !important;
}

/* Style the time picker if used */
.flatpickr-time {
    background-color: #333 !important;
    border-top: 1px solid var(--main-color) !important;
}

.flatpickr-time input {
    color: #eee !important;
}

/* Make the calendar a bit more compact */
.flatpickr-calendar {
    width: 280px !important;
}

/* Make sure the calendar doesn't get cut off at the bottom of the screen */
.flatpickr-calendar.open {
    max-height: none;
    overflow-y: visible;
    /* Changed from auto to visible to remove scrollbar */
    width: auto !important;
    /* Allow the calendar to be as wide as needed */
    min-width: 260px !important;
    /* Increased width to fit all 7 days */
}

/* Ensure the calendar is visible and not covered by other elements */
.flatpickr-calendar.open {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--main-color);
}

/* Style the calendar to match the site theme */
.flatpickr-calendar {
    background-color: #333;
    color: white;
    border-radius: 8px;
    padding: 5px;
}

/* Make the days compact but readable */
.flatpickr-weekday {
    /* Width controlled by flex layout */
    max-width: none !important;
    font-size: 75% !important;
    /* Readable font for weekday names */
    padding: 0 !important;
}

.flatpickr-day {
    /* Width controlled by flex layout */
    max-width: none !important;
    height: 30px !important;
    line-height: 30px !important;
    font-size: 12px !important;
    padding: 0 !important;
}

/* Ensure the days container is wide enough for all 7 days */
.flatpickr-days,
.dayContainer,
.flatpickr-weekdays {
    width: 100% !important;
    min-width: 245px !important;
    max-width: 260px !important;
}

/* Ensure proper alignment of days */
.flatpickr-days {
    display: flex !important;
    justify-content: center !important;
    width: 100% !important;
}

.flatpickr-weekdays {
    display: flex !important;
    justify-content: space-between !important;
    /* Changed to space-between for perfect alignment */
    width: 100% !important;
}

.flatpickr-weekday {
    flex: 1 !important;
    text-align: center !important;
    width: calc(100% / 7) !important;
    box-sizing: border-box !important;
    margin: 0 !important;
    padding: 0 !important;
}

.dayContainer {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: space-between !important;
    /* Changed to space-between for perfect alignment */
    width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Force exact alignment */
.flatpickr-day {
    flex: 1 0 calc(100% / 7) !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    display: inline-flex !important;
    justify-content: center !important;
    align-items: center !important;
    width: calc(100% / 7) !important;
    max-width: calc(100% / 7) !important;
    height: 36px !important;
}

/* Limit to 6 rows of dates but show all days in each row */
.dayContainer {
    min-height: auto !important;
    max-height: 210px !important;
    /* Increased to fit 6 complete rows */
    /* Ensure all days in each row are visible */
}

/* Hide the last row if it contains only next month dates */
.flatpickr-calendar .dayContainer .flatpickr-day:nth-child(n+36):nth-child(-n+42) {
    /* This will be overridden for current month days */
    display: none !important;
}

/* Show current month days in the last row */
.flatpickr-calendar .dayContainer .flatpickr-day:not(.nextMonthDay):not(.prevMonthDay):nth-child(n+36):nth-child(-n+42) {
    display: inline-flex !important;
}

/* Always hide anything beyond 6 rows */
.flatpickr-calendar .dayContainer .flatpickr-day:nth-child(n+43) {
    display: none !important;
}

/* Make sure the calendar has enough space at the bottom */
.flatpickr-calendar {
    margin-bottom: 10px !important;
    position: absolute !important;
    top: calc(100% + 5px) !important;
    /* Position below the input */
    left: 0 !important;
    z-index: 9999 !important;
}

/* Smaller calendar for external reservation modal */
.flatpickr-calendar.external-calendar {
    width: 240px !important;
    font-size: 11px !important;
}

.flatpickr-calendar.external-calendar .flatpickr-month {
    height: 30px !important;
    padding: 0 !important;
    width: 100% !important;
}

.flatpickr-calendar.external-calendar .flatpickr-current-month {
    height: 30px !important;
    font-size: 90% !important;
    padding: 0 !important;
    margin: 0 auto !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    width: auto !important;
    position: absolute !important;
    left: 0 !important;
    right: 0 !important;
    text-align: center !important;
}

.flatpickr-calendar.external-calendar .flatpickr-current-month .flatpickr-monthDropdown-months {
    font-size: 12px !important;
    font-weight: 600 !important;
    width: 80px !important;
    padding: 2px 4px !important;
}

.flatpickr-calendar.external-calendar .flatpickr-current-month input.cur-year {
    font-size: 12px !important;
    padding: 2px 4px !important;
    width: 50px !important;
    display: inline-block !important;
    height: 22px !important;
    line-height: 22px !important;
    margin-left: 5px !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.flatpickr-calendar.external-calendar .numInputWrapper {
    display: inline-block !important;
    opacity: 1 !important;
    visibility: visible !important;
    width: auto !important;
}

.flatpickr-calendar.external-calendar .flatpickr-weekday {
    font-size: 70% !important;
    height: 24px !important;
    line-height: 24px !important;
}

.flatpickr-calendar.external-calendar .flatpickr-day {
    height: 28px !important;
    line-height: 28px !important;
    font-size: 11px !important;
    margin: 0 !important;
    max-width: calc(100% / 7) !important;
}

.flatpickr-calendar.external-calendar .dayContainer {
    max-height: 168px !important;
    /* 6 rows of 28px */
}

/* Adjust navigation arrows for external calendar */
.flatpickr-calendar.external-calendar .flatpickr-prev-month,
.flatpickr-calendar.external-calendar .flatpickr-next-month {
    height: 30px !important;
    padding: 0 !important;
    width: 28px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.flatpickr-calendar.external-calendar .flatpickr-prev-month svg,
.flatpickr-calendar.external-calendar .flatpickr-next-month svg {
    width: 14px !important;
    height: 14px !important;
}

/* Make previous and next month days darker */
.flatpickr-day.prevMonthDay,
.flatpickr-day.nextMonthDay {
    background-color: #2a2a2a !important;
    color: #777 !important;
    opacity: 1 !important;
}

/* Style for disabled/past dates */
.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover,
.flatpickr-day.past-date {
    background-color: #222 !important;
    color: #555 !important;
    text-decoration: line-through !important;
    cursor: not-allowed !important;
    opacity: 0.8 !important;
}

/* Additional styling for adjacent month days */
.flatpickr-day.adjacent-month {
    background-color: #2a2a2a !important;
    color: #666 !important;
}

/* Make the month header properly aligned */
.flatpickr-month {
    height: 36px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.flatpickr-current-month {
    font-size: 100% !important;
    padding: 0 !important;
    height: 36px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    top: 0 !important;
    position: relative !important;
    margin-left: -60px !important;
    /* Move month/year selectors even further to the left */
}

.flatpickr-current-month .flatpickr-monthDropdown-months,
.flatpickr-current-month input.cur-year {
    font-size: 14px !important;
    padding: 0 4px !important;
    height: 24px !important;
    line-height: 24px !important;
}

/* Make the calendar more compact overall */
.flatpickr-calendar {
    padding: 2px !important;
    font-size: 12px !important;
}

.flatpickr-month {
    background-color: #444;
    color: white;
    /* Height and padding already set above */
}

.flatpickr-weekday {
    color: var(--highlight-color);
    font-weight: bold;
}

.flatpickr-day {
    color: white;
    border-radius: 4px;
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange {
    background-color: var(--main-color);
    border-color: var(--main-color);
}

.flatpickr-day:hover {
    background-color: #555;
}

.flatpickr-day.today {
    border-color: var(--highlight-color);
}

.flatpickr-day.today:hover {
    background-color: var(--highlight-color);
    color: white;
}

/* Fix for the arrows */
.flatpickr-prev-month,
.flatpickr-next-month {
    fill: var(--highlight-color) !important;
    padding: 0 !important;
    height: 36px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    top: 0 !important;
}

.flatpickr-prev-month svg,
.flatpickr-next-month svg {
    width: 16px !important;
    height: 16px !important;
}

.flatpickr-prev-month:hover svg,
.flatpickr-next-month:hover svg {
    fill: white !important;
}