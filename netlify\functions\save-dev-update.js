// Netlify function for saving development update subscribers to Supabase
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://vwpyvjoycnrgmpatzqzk.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ3cHl2am95Y25yZ21wYXR6cXprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDExMzk0MTksImV4cCI6MjA1NjcxNTQxOX0.sG7amfnd8UTMv3zzmaa3KKyPRTzW70cDW9VVcUCSq4c';
const supabase = createClient(supabaseUrl, supabaseKey);

// Set CORS headers
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ message: 'Preflight call successful' })
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    // Parse request body
    const requestBody = JSON.parse(event.body);
    const { email, name } = requestBody;

    // Validate required parameters
    if (!email) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing required parameter: email' })
      };
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Invalid email format' })
      };
    }

    // Insert subscriber into Supabase
    const { data, error } = await supabase
      .from('devupdates')
      .insert([
        {
          email,
          name: name || null
        }
      ])
      .select();

    if (error) {
      // Check if it's a unique constraint violation (email already exists)
      if (error.code === '23505') {
        return {
          statusCode: 409,
          headers,
          body: JSON.stringify({ 
            error: 'Email already registered', 
            message: 'This email is already subscribed to development updates.' 
          })
        };
      }

      console.error('Error saving subscriber to Supabase:', error);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ 
          error: 'Failed to save subscriber', 
          message: error.message 
        })
      };
    }

    // Return successful response
    return {
      statusCode: 201,
      headers,
      body: JSON.stringify({ 
        success: true, 
        message: 'Successfully subscribed to development updates!',
        data: data[0]
      })
    };
  } catch (error) {
    console.error('Error in save-dev-update function:', error);

    // Return error response
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Failed to process subscription',
        message: error.message
      })
    };
  }
};
