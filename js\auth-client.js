/**
 * Authentication Client for Vestigia
 *
 * This file provides a client-side interface to the Netlify Functions
 * that handle authentication with Supabase.
 */

// API base URL configuration for Netlify Functions
const AUTH_API_CONFIG = {
  // Local development URL (when using Netlify CLI)
  development: 'http://localhost:8888/.netlify/functions',

  // Production URL (Netlify Functions)
  production: '/.netlify/functions'
};

// Determine which environment we're in
const isAuthApiProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';

// Select the appropriate API base URL
const AUTH_API_BASE_URL = isAuthApiProduction ? AUTH_API_CONFIG.production : AUTH_API_CONFIG.development;

console.log(`Using Auth API base URL: ${AUTH_API_BASE_URL} (${isAuthApiProduction ? 'production' : 'development'} mode)`);

/**
 * Authentication Client
 */
window.authClient = {
  /**
   * Register a new user
   * @param {string} email - User's email
   * @param {string} password - User's password
   * @param {string} name - User's full name (optional)
   * @returns {Promise<Object>} Registration result
   */
  signup: async (email, password, name = '') => {
    try {
      const response = await fetch(`${AUTH_API_BASE_URL}/auth-signup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password, name })
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Registration failed');
      }
      
      return data;
    } catch (error) {
      console.error('Error during signup:', error);
      throw error;
    }
  },

  /**
   * Log in a user
   * @param {string} email - User's email
   * @param {string} password - User's password
   * @returns {Promise<Object>} Login result with session and user data
   */
  login: async (email, password) => {
    try {
      const response = await fetch(`${AUTH_API_BASE_URL}/auth-login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Login failed');
      }
      
      // Store session in localStorage
      localStorage.setItem('vestigia_session', JSON.stringify(data.session));
      localStorage.setItem('vestigia_user', JSON.stringify(data.user));
      
      return data;
    } catch (error) {
      console.error('Error during login:', error);
      throw error;
    }
  },

  /**
   * Log out the current user
   * @returns {Promise<Object>} Logout result
   */
  logout: async () => {
    try {
      const response = await fetch(`${AUTH_API_BASE_URL}/auth-logout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Logout failed');
      }
      
      // Clear session from localStorage
      localStorage.removeItem('vestigia_session');
      localStorage.removeItem('vestigia_user');
      
      return data;
    } catch (error) {
      console.error('Error during logout:', error);
      throw error;
    }
  },

  /**
   * Send a password reset email
   * @param {string} email - User's email
   * @returns {Promise<Object>} Password reset result
   */
  resetPassword: async (email) => {
    try {
      const response = await fetch(`${AUTH_API_BASE_URL}/auth-reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email })
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Password reset failed');
      }
      
      return data;
    } catch (error) {
      console.error('Error during password reset:', error);
      throw error;
    }
  },

  /**
   * Get the current user's information
   * @returns {Promise<Object>} User information
   */
  getCurrentUser: async () => {
    try {
      // First check if we have a session in localStorage
      const sessionStr = localStorage.getItem('vestigia_session');
      const userStr = localStorage.getItem('vestigia_user');
      
      if (!sessionStr || !userStr) {
        return null;
      }
      
      const session = JSON.parse(sessionStr);
      const user = JSON.parse(userStr);
      
      // Check if the token is expired
      if (new Date(session.expires_at * 1000) < new Date()) {
        // Token is expired, clear session
        localStorage.removeItem('vestigia_session');
        localStorage.removeItem('vestigia_user');
        return null;
      }
      
      return user;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  },

  /**
   * Check if the user is logged in
   * @returns {Promise<boolean>} True if logged in, false otherwise
   */
  isLoggedIn: async () => {
    const user = await window.authClient.getCurrentUser();
    return !!user;
  },

  /**
   * Initialize the auth client and update UI based on login state
   */
  init: async () => {
    try {
      const isLoggedIn = await window.authClient.isLoggedIn();
      
      // Update UI based on login state
      const loginButtons = document.querySelectorAll('.login-btn');
      const logoutButtons = document.querySelectorAll('.logout-btn');
      
      if (isLoggedIn) {
        // User is logged in
        const user = await window.authClient.getCurrentUser();
        
        // Update login/logout buttons
        loginButtons.forEach(btn => {
          btn.style.display = 'none';
        });
        
        logoutButtons.forEach(btn => {
          btn.style.display = 'block';
        });
        
        // If there's a user profile element, update it
        const userProfileElements = document.querySelectorAll('.user-profile');
        userProfileElements.forEach(el => {
          el.textContent = user.email;
          el.style.display = 'block';
        });
      } else {
        // User is not logged in
        loginButtons.forEach(btn => {
          btn.style.display = 'block';
        });
        
        logoutButtons.forEach(btn => {
          btn.style.display = 'none';
        });
        
        // Hide user profile elements
        const userProfileElements = document.querySelectorAll('.user-profile');
        userProfileElements.forEach(el => {
          el.style.display = 'none';
        });
      }
    } catch (error) {
      console.error('Error initializing auth client:', error);
    }
  }
};

// Initialize the auth client when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.authClient.init();
});
