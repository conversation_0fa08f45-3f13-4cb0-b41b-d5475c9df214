// Netlify function for fetching weather data from OpenWeatherMap API
const axios = require('axios');

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  try {
    // Parse query parameters
    const params = event.queryStringParameters || {};
    const { lat, lng } = params;

    // Validate required parameters
    if (!lat || !lng) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing required parameters: lat and lng' })
      };
    }

    // Use the API key from environment variables or fallback to a default
    const apiKey = process.env.OPENWEATHERMAP_API_KEY || "********************************";

    // Call OpenWeatherMap API
    const response = await axios({
      method: 'get',
      url: 'https://api.openweathermap.org/data/2.5/weather',
      params: {
        lat,
        lon: lng,
        units: 'metric',
        appid: apiKey
      }
    });

    // Return successful response
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify(response.data)
    };
  } catch (error) {
    console.error('Error fetching weather data:', error.response?.data || error.message);

    // Return error response
    return {
      statusCode: error.response?.status || 500,
      headers,
      body: JSON.stringify({
        error: 'Failed to fetch weather data',
        message: error.message
      })
    };
  }
};
