/**
 * Map API Client for Vestigia
 *
 * This file provides a client-side interface to the Netlify Functions
 * that protect our Supabase API keys.
 */

// API base URL configuration for Netlify Functions
const MAP_API_CONFIG = {
  // Local development URL (when using Netlify CLI)
  development: 'http://localhost:8888/api',

  // Production URL (Netlify Functions)
  production: '/api'
};

// Determine which environment we're in
const isMapApiProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';

// Select the appropriate API base URL
const MAP_API_BASE_URL = isMapApiProduction ? MAP_API_CONFIG.production : MAP_API_CONFIG.development;

console.log(`Using Map API base URL: ${MAP_API_BASE_URL} (${isMapApiProduction ? 'production' : 'development'} mode)`);

/**
 * Map API Client
 */
const mapAPI = {
  /**
   * Fetch POI markers from Supabase
   * @returns {Promise<Array>} POI markers
   */
  fetchPOIMarkers: async () => {
    try {
      console.log('Fetching POI markers from Netlify Function');
      const response = await fetch(`${MAP_API_BASE_URL}/fetch-poi-markers`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch POI markers');
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching POI markers:', error);
      throw error;
    }
  },

  /**
   * Save a POI suggestion to Supabase
   * @param {Object} suggestion - Suggestion data
   * @returns {Promise<Object>} Saved suggestion
   */
  savePOISuggestion: async (suggestion) => {
    try {
      console.log('Saving POI suggestion via Netlify Function:', suggestion);
      const response = await fetch(`${MAP_API_BASE_URL}/save-poi-suggestion`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(suggestion)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save POI suggestion');
      }

      return await response.json();
    } catch (error) {
      console.error('Error saving POI suggestion:', error);
      throw error;
    }
  },

  /**
   * Get weather data for a location
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @returns {Promise<Object>} Weather data
   */
  getWeatherData: async (lat, lng) => {
    try {
      console.log(`Fetching weather data for ${lat},${lng} via Netlify Function`);
      const response = await fetch(`${MAP_API_BASE_URL}/get-weather?lat=${lat}&lng=${lng}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch weather data');
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching weather data:', error);
      throw error;
    }
  },

  /**
   * Search for locations using Nominatim
   * @param {string} query - Search query
   * @returns {Promise<Array>} Search results
   */
  searchLocations: async (query) => {
    try {
      console.log(`Searching locations for "${query}" via Netlify Function`);
      const response = await fetch(`${MAP_API_BASE_URL}/geocode-search?q=${encodeURIComponent(query)}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to search locations');
      }

      return await response.json();
    } catch (error) {
      console.error('Error searching locations:', error);
      throw error;
    }
  },

  /**
   * Get location name from coordinates using reverse geocoding
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @returns {Promise<Object>} Location data
   */
  getLocationName: async (lat, lng) => {
    try {
      console.log(`Getting location name for ${lat},${lng} via Netlify Function`);
      const response = await fetch(`${MAP_API_BASE_URL}/geocode-reverse?lat=${lat}&lng=${lng}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get location name');
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting location name:', error);
      throw error;
    }
  },

  /**
   * Get all user markers from Supabase
   * @returns {Promise<Array>} User's saved markers
   */
  getUserMarkers: async () => {
    console.log('Fetching user markers...');
    try {
      const token = localStorage.getItem('sb-access-token');
      if (!token) {
        console.log('No auth token found, returning empty markers');
        return [];
      }

      console.log('Making request to fetch user markers');
      const response = await fetch(`${MAP_API_BASE_URL}/user-markers`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        credentials: 'same-origin'
      });

      const responseData = await response.json().catch(() => ({}));
      
      if (!response.ok) {
        const errorMessage = responseData.error || 
                           responseData.message || 
                           `Failed to fetch user markers: ${response.status} ${response.statusText}`;
        console.error('Error response from server:', { status: response.status, error: errorMessage });
        throw new Error(errorMessage);
      }

      console.log('Successfully fetched user markers');
      return Array.isArray(responseData) ? responseData : [];
    } catch (error) {
      console.error('Error in getUserMarkers:', {
        message: error.message,
        stack: error.stack
      });
      return [];
    }
  },

  /**
   * Save a user marker to Supabase
   * @param {Object} marker - Marker data to save
   * @returns {Promise<Object>} Saved marker data
   */
  saveUserMarker: async (marker) => {
    console.log('Saving user marker:', marker);
    try {
      const token = localStorage.getItem('sb-access-token');
      if (!token) {
        const error = new Error('Not authenticated');
        error.code = 'UNAUTHENTICATED';
        throw error;
      }

      console.log('Sending marker to server...');
      const response = await fetch(`${MAP_API_BASE_URL}/user-markers`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json'
        },
        body: JSON.stringify(marker),
        credentials: 'same-origin'
      });

      let responseData;
      try {
        responseData = await response.json();
      } catch (parseError) {
        console.error('Failed to parse response:', parseError);
        throw new Error('Invalid response from server');
      }

      if (!response.ok) {
        const errorMessage = responseData?.error || 
                           responseData?.message || 
                           `Failed to save marker: ${response.status} ${response.statusText}`;
        console.error('Error response from server:', { status: response.status, error: errorMessage });
        
        const error = new Error(errorMessage);
        error.status = response.status;
        error.response = responseData;
        throw error;
      }

      console.log('Successfully saved marker:', responseData);
      return responseData;
    } catch (error) {
      console.error('Error in saveUserMarker:', {
        message: error.message,
        code: error.code,
        status: error.status,
        stack: error.stack
      });
      
      // Rethrow with more context if it's not already a custom error
      if (!error.code && !error.status) {
        error.message = `Failed to save marker: ${error.message}`;
      }
      throw error;
    }
  },

  /**
   * Delete a user marker from Supabase
   * @param {string} userId - ID of the user who owns the marker
   * @param {string|number} markerId - ID of the marker to delete (can be UUID or integer)
   * @returns {Promise<boolean>} True if deletion was successful
   */
  deleteUserMarker: async (userId, markerId) => {
    try {
      const token = localStorage.getItem('sb-access-token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      // Ensure markerId is a string for the URL parameter
      const markerIdStr = typeof markerId === 'number' ? markerId.toString() : markerId;
      
      // If we have a UUID, we need to find the corresponding integer ID first
      if (markerIdStr.includes('-')) { // Likely a UUID
        // First, get the marker to find its integer ID
        const response = await fetch(`${MAP_API_BASE_URL}/user-markers?user_id=${encodeURIComponent(userId)}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch markers');
        }

        const markers = await response.json();
        const marker = markers.find(m => m.id === markerIdStr || m.marker_id === markerIdStr);
        
        if (!marker) {
          throw new Error('Marker not found');
        }

        // Now delete using the integer ID
        return window.mapAPI.deleteUserMarker(userId, marker.id);
      }

      // For numeric IDs, proceed with deletion
      const deleteResponse = await fetch(`${MAP_API_BASE_URL}/user-markers?id=${encodeURIComponent(markerIdStr)}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!deleteResponse.ok) {
        const errorData = await deleteResponse.json();
        throw new Error(errorData.error || 'Failed to delete marker');
      }

      return true;
    } catch (error) {
      console.error('Error in deleteUserMarker:', {
        message: error.message,
        markerId,
        error: error.stack
      });
      throw error;
    }
  }
};

// Export the API client
console.log('Map API client script loaded, setting up mapAPI object');
window.mapAPI = mapAPI;
console.log('mapAPI object created');
