/**
 * Authentication Client for Vestigia
 *
 * This file provides client-side authentication functionality using Supabase
 * through Netlify Functions to protect API keys.
 */

// Authentication API configuration
const AUTH_CONFIG = {
  // Local development URL
  development: 'http://localhost:8888/.netlify/functions',
  // Production URL
  production: '/.netlify/functions'
};

// Determine environment
const isAuthProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
const AUTH_API_BASE_URL = isAuthProduction ? AUTH_CONFIG.production : AUTH_CONFIG.development;

console.log(`Using Auth API base URL: ${AUTH_API_BASE_URL} (${isAuthProduction ? 'production' : 'development'} mode)`);

// Helper function to handle redirects
function handleAuthRedirect() {
  // Update the cleanupDuplicatesInSupabase function
  async function cleanupDuplicatesInSupabase() {
    if (auth && auth.isLoggedIn() && auth.user) {
      try {
        console.log(`Cleaning up duplicate markers in Supabase for user ${auth.user.id}`);

        // Get all markers for this user
        const response = await window.mapAPI.getUserMarkers(auth.user.id);
        if (!response || !response.data) {
          console.error('No response from getUserMarkers');
          return {
            error: 'No response from getUserMarkers',
            success: false,
            duplicates_removed: 0
          };
        }

        const markers = response.data;

        // Track unique markers using a Map with compound key of normalized lat,lng,name
        const uniqueMarkers = new Map();
        const duplicates = [];

        // Sort markers by created_at to ensure we keep the oldest entries
        markers.sort((a, b) => new Date(a.created_at || 0) - new Date(b.created_at || 0));

        markers.forEach(marker => {
          // Normalize coordinates to 5 decimal places and name to lowercase
          const normalizedLat = parseFloat(marker.lat).toFixed(5);
          const normalizedLng = parseFloat(marker.lng).toFixed(5);
          const normalizedName = (marker.name || '').toString().trim().toLowerCase();

          // Create a unique key with normalized values
          const key = `${normalizedLat},${normalizedLng},${normalizedName}`;

          if (!uniqueMarkers.has(key)) {
            uniqueMarkers.set(key, marker);
          } else {
            // If we find a duplicate, keep the one with the earlier created_at
            const existing = uniqueMarkers.get(key);
            const markerTime = new Date(marker.created_at || 0);
            const existingTime = new Date(existing.created_at || 0);

            if (markerTime < existingTime) {
              duplicates.push(existing.id);
              uniqueMarkers.set(key, marker);
            } else {
              duplicates.push(marker.id);
            }
          }
        });

        // If we found duplicates, delete them
        if (duplicates.length > 0) {
          console.log(`Found ${duplicates.length} duplicates to remove`);
          // Delete markers in chunks to avoid overwhelming the server
          const chunkSize = 10;
          for (let i = 0; i < duplicates.length; i += chunkSize) {
            const chunk = duplicates.slice(i, i + chunkSize);
            try {
              await Promise.all(chunk.map(id =>
                window.mapAPI.deleteUserMarker(auth.user.id, id)
              ));
            } catch (deleteError) {
              console.error(`Error deleting markers chunk:`, deleteError);
              // Continue with other deletions even if one fails
            }
          }
          return {
            success: true,
            message: `Cleaned up ${duplicates.length} duplicate markers`,
            duplicates_removed: duplicates.length
          };
        }

        return {
          success: true,
          message: "No duplicates found",
          duplicates_removed: 0
        };
      } catch (error) {
        console.error('Error in cleanupDuplicatesInSupabase:', error);
        return {
          error: error.message,
          success: false,
          duplicates_removed: 0
        };
      }
    } else {
      console.log('User not logged in, skipping Supabase cleanup');
      return {
        error: 'User not logged in',
        success: false,
        duplicates_removed: 0
      };
    }
  } async function loadUserMarkers() {
    try {
      // First, clean up duplicates in local storage
      cleanupLocalStorageDuplicates();

      // If user is logged in, also clean up duplicates in Supabase
      if (auth && auth.isLoggedIn() && auth.user) {
        try {
          console.log('Cleaning up duplicates in Supabase...');
          const cleanupResult = await cleanupDuplicatesInSupabase();
          if (cleanupResult.duplicates_removed > 0) {
            console.log(`Cleaned up ${cleanupResult.duplicates_removed} duplicates from Supabase`);
          }
        } catch (error) {
          console.error('Error cleaning up duplicates in Supabase:', error);
          // Continue with local storage markers
        }
      }

      // Now load markers from local storage
      const localStorageMarkers = JSON.parse(localStorage.getItem('userMarkers') || '[]');
      console.log('Loaded markers from local storage:', localStorageMarkers.length);

      // Create a map to track markers by normalized location and name for deduplication
      const markerMap = new Map();
      const normalizeKey = (lat, lng, name) => {
        return `${parseFloat(lat).toFixed(5)},${parseFloat(lng).toFixed(5)},${(name || '').toString().trim().toLowerCase()}`;
      };

      // Add local storage markers to the map
      localStorageMarkers.forEach(marker => {
        const key = normalizeKey(marker.lat, marker.lng, marker.name);
        markerMap.set(key, { ...marker, source: 'localStorage' });
      });

      // If user is logged in, also load markers from Supabase
      let supabaseMarkers = [];
      if (auth && auth.isLoggedIn() && auth.user) {
        console.log('User is logged in, fetching markers from Supabase');
        try {
          // Fetch markers from Supabase
          supabaseMarkers = await window.mapAPI.fetchUserMarkers(auth.user.id);
          console.log('Loaded markers from Supabase:', supabaseMarkers.length);

          // Add Supabase markers to the map, overriding local storage markers if they exist
          supabaseMarkers.forEach(marker => {
            const key = normalizeKey(marker.lat, marker.lng, marker.name);
            // Supabase markers take precedence over local storage markers
            markerMap.set(key, { ...marker, source: 'supabase' });
          });

          // Save the merged markers back to Supabase to ensure consistency
          const mergedMarkers = Array.from(markerMap.values())
            .filter(m => m.source === 'supabase' || m.source === 'localStorage')
            .map(({ source, ...marker }) => marker);

          if (mergedMarkers.length > 0) {
            try {
              await window.mapAPI.syncUserMarkers(auth.user.id, mergedMarkers);
            } catch (syncError) {
              console.error('Error syncing merged markers to Supabase:', syncError);
            }
          }
        } catch (fetchError) {
          console.error('Error fetching markers from Supabase:', fetchError);
          // Continue with local storage markers if Supabase fetch fails
        }
      }

      // Convert the map to an array of unique markers
      const allMarkers = Array.from(markerMap.values());
      console.log('Total unique markers after deduplication:', allMarkers.length);

      // Rest of the existing loadUserMarkers function remains the same...
      // [Previous code for processing markers and adding them to the map]

    } catch (error) {
      console.error('Error loading user markers:', error);
    }
  }// Add this function to handle marker syncing and deduplication
  async function syncAndDeduplicateMarkers() {
    try {
      if (!auth || !auth.isLoggedIn() || !auth.user) {
        console.log('User not logged in, skipping marker sync');
        return;
      }

      console.log('Starting marker sync and deduplication...');

      // 1. Get markers from both sources
      const localMarkers = JSON.parse(localStorage.getItem('userMarkers') || '[]');
      console.log('Local markers before sync:', localMarkers.length);

      // 2. Get markers from Supabase
      let supabaseMarkers = [];
      try {
        supabaseMarkers = await window.mapAPI.fetchUserMarkers(auth.user.id) || [];
        console.log('Supabase markers before sync:', supabaseMarkers.length);
      } catch (error) {
        console.error('Error fetching markers from Supabase:', error);
        // Continue with local markers only
      }

      // 3. Create a map for deduplication
      const markerMap = new Map();
      const normalizeKey = (lat, lng, name) => {
        return `${parseFloat(lat).toFixed(5)},${parseFloat(lng).toFixed(5)},${String(name || '').trim().toLowerCase()}`;
      };

      // 4. Add Supabase markers first (they take precedence)
      supabaseMarkers.forEach(marker => {
        const key = normalizeKey(marker.lat, marker.lng, marker.name);
        markerMap.set(key, { ...marker, source: 'supabase' });
      });

      // 5. Add local markers, but don't override Supabase markers
      localMarkers.forEach(marker => {
        const key = normalizeKey(marker.lat, marker.lng, marker.name);
        if (!markerMap.has(key)) {
          markerMap.set(key, { ...marker, source: 'local' });
        }
      });

      // 6. Convert back to array and prepare for saving
      const allMarkers = Array.from(markerMap.values()).map(({ source, ...marker }) => marker);
      console.log('Total unique markers after deduplication:', allMarkers.length);

      // 7. Save to Supabase
      try {
        await window.mapAPI.syncUserMarkers(auth.user.id, allMarkers);
        console.log('Markers synced to Supabase');
      } catch (syncError) {
        console.error('Error syncing markers to Supabase:', syncError);
        throw syncError;
      }

      // 8. Update local storage with the merged set
      localStorage.setItem('userMarkers', JSON.stringify(allMarkers));
      console.log('Markers saved to local storage');

      return allMarkers;
    } catch (error) {
      console.error('Error in syncAndDeduplicateMarkers:', error);
      throw error;
    }
  }

  // Add the syncMarkers method to the auth object
  auth.syncMarkers = syncAndDeduplicateMarkers;

  // Update the login function to sync markers after successful login
  const originalLogin = auth.login;
  auth.login = async function (email, password) {
    try {
      console.log('Attempting login for:', email);
      const result = await originalLogin.call(this, email, password);

      // Sync markers after successful login
      try {
        console.log('Syncing markers after login...');
        await this.syncMarkers();
        console.log('Markers synced successfully');
      } catch (syncError) {
        console.error('Error syncing markers after login:', syncError);
        // Continue with the login flow even if sync fails
      }

      return result;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  // Update the init function to sync markers on page load
  const originalInit = auth.init;
  auth.init = async function () {
    try {
      await originalInit.call(this);

      // If user is logged in, sync markers
      if (this.user) {
        console.log('User is logged in, syncing markers...');
        try {
          await this.syncMarkers();
          console.log('Markers synced on init');
        } catch (syncError) {
          console.error('Error syncing markers on init:', syncError);
        }
      }
    } catch (error) {
      console.error('Init error:', error);
    }
  }; // List of auth-related paths that shouldn't be stored as redirect targets
  const authPaths = ['/login.html', '/auth-login.html', '/signup.html', '/auth-signup.html', '/reset-password.html'];

  // Get current path and full URL
  const currentPath = window.location.pathname;
  const currentUrl = window.location.href;

  console.log('handleAuthRedirect called. Current path:', currentPath, 'Full URL:', currentUrl);

  // Check if current page is an auth page
  const isAuthPage = authPaths.some(path => currentPath.endsWith(path));

  if (!isAuthPage) {
    // Store the full URL including query parameters and hash
    console.log('Storing redirect URL:', currentUrl);
    sessionStorage.setItem('auth_redirect_url', currentUrl);
    console.log('Current sessionStorage:', JSON.stringify(sessionStorage));
  } else {
    console.log('Not storing redirect URL for auth page:', currentPath);
  }
}

// Helper function to get and clear the redirect URL
function getAndClearRedirectUrl() {
  const storedUrl = sessionStorage.getItem('auth_redirect_url');
  const defaultUrl = 'index.html';

  console.log('getAndClearRedirectUrl called. Stored URL:', storedUrl);

  // If no stored URL, return default
  if (!storedUrl) {
    console.log('No stored redirect URL found, using default:', defaultUrl);
    return defaultUrl;
  }

  // Clean up the stored URL
  let redirectUrl;
  try {
    // Create URL object to parse and validate
    const url = new URL(storedUrl, window.location.origin);

    // Check if the URL is from our domain
    if (url.origin !== window.location.origin) {
      console.log('Redirect URL is from a different origin, using default');
      return defaultUrl;
    }

    // Get the full path including query parameters and hash
    redirectUrl = url.pathname + url.search + url.hash;

    // Ensure we don't redirect back to auth pages
    const authPaths = ['/login.html', '/auth-login.html', '/signup.html', '/auth-signup.html', '/reset-password.html'];
    if (authPaths.some(path => url.pathname.endsWith(path))) {
      console.log('Redirect URL is an auth page, defaulting to index.html');
      return defaultUrl;
    }

    console.log('Using stored redirect URL:', redirectUrl);
    return redirectUrl;
  } catch (error) {
    console.error('Error parsing redirect URL:', error);
    return defaultUrl;
  } finally {
    // Always clear the stored URL to prevent redirect loops
    sessionStorage.removeItem('auth_redirect_url');
  }
}

// Create the auth client
const auth = {
  // Current user session
  session: null,

  // Current user data
  user: null,

  /**
   * Initialize the auth client
   * Checks for existing session and updates UI
   */
  /**
   * Handle login link clicks to store current URL before redirect
   */
  setupLoginLinks: function () {
    // Set up click handlers for login links
    document.addEventListener('click', (e) => {
      // Check if the clicked element or its parent is a login link
      const loginLink = e.target.closest('a[href*="login"], a[href*="auth-login"]');
      if (loginLink && !loginLink.classList.contains('logout-btn')) {
        // Don't store redirect URL if we're already on an auth page
        const isAuthPage = window.location.pathname.includes('login.html') ||
          window.location.pathname.includes('auth-login.html') ||
          window.location.pathname.includes('signup.html') ||
          window.location.pathname.includes('auth-signup.html');

        if (!isAuthPage) {
          console.log('Login link clicked, storing current URL');
          // Store current URL before navigating to login
          handleAuthRedirect();
        } else {
          console.log('On auth page, not storing redirect URL');
        }

        // If we're already on a login page, prevent default to avoid double handling
        if (window.location.pathname.includes('login.html') ||
          window.location.pathname.includes('auth-login.html')) {
          e.preventDefault();
        }
      }
    }, true); // Use capture phase to ensure we catch the event
  },

  init: function () {
    console.log('Initializing auth client...');

    // Return a promise to handle async operations
    return new Promise((resolve) => {
      // Set up login link handlers
      this.setupLoginLinks();

      // Check for existing session in localStorage
      const sessionStr = localStorage.getItem('vestigia_session');
      const userStr = localStorage.getItem('vestigia_user');

      if (sessionStr && userStr) {
        try {
          this.session = JSON.parse(sessionStr);
          this.user = JSON.parse(userStr);

          // Check if session is expired
          const expiresAt = new Date(this.session.expires_at * 1000);
          if (expiresAt < new Date()) {
            console.log('Session expired, logging out');
            this.logout(false);
            resolve(false);
            return;
          }

          console.log('User is logged in:', this.user.email);
          this.updateUI(true);
          resolve(true);
        } catch (error) {
          console.error('Error parsing stored session:', error);
          this.logout(false);
          resolve(false);
        }
      } else {
        console.log('No active session found');
        this.updateUI(false);
        resolve(false);
      }
    });
  },

  /**
   * Sign up a new user
   * @param {string} email - User's email
   * @param {string} password - User's password
   * @param {string} fullName - User's full name
   * @returns {Promise<Object>} Signup result
   */
  signup: async function (email, password, fullName) {
    try {
      const response = await fetch(`${AUTH_API_BASE_URL}/auth-signup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email,
          password,
          name: fullName
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || data.error || 'Signup failed');
      }

      return data;
    } catch (error) {
      console.error('Signup error:', error);
      throw error;
    }
  },

  /**
   * Log in a user
   * @param {string} email - User's email
   * @param {string} password - User's password
   * @returns {Promise<Object>} Login result
   */
  login: async function (email, password) {
    try {
      console.log('Attempting login for:', email);

      const response = await fetch(`${AUTH_API_BASE_URL}/auth-login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email,
          password
        })
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('Login failed:', data.message || data.error || 'Unknown error');
        throw new Error(data.message || data.error || 'Login failed');
      }

      console.log('Login successful, storing session data');

      // Store session and user data
      this.session = data.session;
      this.user = data.user;

      // Save to localStorage
      localStorage.setItem('vestigia_session', JSON.stringify(this.session));
      localStorage.setItem('vestigia_user', JSON.stringify(this.user));

      // Update UI
      console.log('Updating UI after login');
      this.updateUI(true);

      // Get the redirect URL with detailed logging
      console.log('Before getAndClearRedirectUrl - sessionStorage:', JSON.stringify(sessionStorage));
      const redirectUrl = getAndClearRedirectUrl();
      console.log('After getAndClearRedirectUrl - redirectUrl:', redirectUrl);
      console.log('Current URL:', window.location.href);
      console.log('Redirect URL to use:', redirectUrl);

      // Add a small delay to ensure everything is processed
      await new Promise(resolve => setTimeout(resolve, 100));

      // Redirect to the stored URL or home page
      if (redirectUrl && redirectUrl !== window.location.href) {
        console.log(`Performing redirect to: ${redirectUrl}`);
        window.location.href = redirectUrl;
      } else {
        console.log('No valid redirect URL, defaulting to index.html');
        window.location.href = 'index.html';
      }

      return data;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },

  /**
   * Log out the current user
   * @param {boolean} callApi - Whether to call the logout API (default: true)
   * @returns {Promise<void>}
   */
  logout: async function (callApi = true) {
    try {
      if (callApi) {
        await fetch(`${AUTH_API_BASE_URL}/auth-logout`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });
      }

      // Clear session data
      this.session = null;
      this.user = null;

      // Remove from localStorage
      localStorage.removeItem('vestigia_session');
      localStorage.removeItem('vestigia_user');

      // Update UI
      this.updateUI(false);

      // Redirect to index.html if on profile/settings page, otherwise refresh current page
      if (window.location.pathname.endsWith('profile.html') ||
        window.location.pathname.endsWith('settings.html')) {
        window.location.href = 'index.html';
      } else {
        window.location.reload();
      }
    } catch (error) {
      console.error('Logout error:', error);
      // Still clear local data even if API call fails
      this.session = null;
      this.user = null;
      localStorage.removeItem('vestigia_session');
      localStorage.removeItem('vestigia_user');
      this.updateUI(false);

      // Still redirect/refresh even if there was an error
      if (window.location.pathname.endsWith('profile.html') ||
        window.location.pathname.endsWith('settings.html')) {
        window.location.href = 'index.html';
      } else {
        window.location.reload();
      }
    }
  },

  /**
   * Reset password for a user
   * @param {string} email - User's email
   * @returns {Promise<Object>} Password reset result
   */
  resetPassword: async function (email) {
    try {
      const response = await fetch(`${AUTH_API_BASE_URL}/auth-reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || data.error || 'Password reset failed');
      }

      return data;
    } catch (error) {
      console.error('Password reset error:', error);
      throw error;
    }
  },

  /**
   * Get the current user
   * @returns {Object|null} The current user object or null if not logged in
   */
  getCurrentUser: function () {
    return this.user;
  },

  /**
   * Check if user is logged in
   * @returns {boolean} True if logged in
   */
  isLoggedIn: function () {
    return !!this.user;
  },

  /**
   * Update UI based on authentication state
   * @param {boolean} isLoggedIn - Whether user is logged in
   */
  updateUI: function (isLoggedIn) {
    console.log('Updating UI, isLoggedIn:', isLoggedIn);

    // If not logged in and on a protected page, redirect to login
    if (!isLoggedIn &&
      (window.location.pathname.endsWith('profile.html') ||
        window.location.pathname.endsWith('settings.html'))) {
      window.location.href = 'auth-login.html?redirect=' + encodeURIComponent(window.location.pathname);
      return;
    }

    // Find all login, signup, profile, and logout buttons
    const loginButtons = document.querySelectorAll('.login-btn');
    const signupButtons = document.querySelectorAll('.signup-btn');
    const profileButtons = document.querySelectorAll('.profile-btn');
    const logoutButtons = document.querySelectorAll('.logout-btn');
    const userElements = document.querySelectorAll('.user-profile');

    // Also find all auth-item elements (used in map.html)
    const authItems = document.querySelectorAll('.auth-item');

    console.log(`Found ${loginButtons.length} login buttons, ${signupButtons.length} signup buttons, ${profileButtons.length} profile buttons, ${logoutButtons.length} logout buttons, ${authItems.length} auth items`);

    if (isLoggedIn && this.user) {
      console.log('User is logged in, updating UI elements');

      // First, handle auth-item elements specifically for map.html
      authItems.forEach(item => {
        const isLoginItem = item.querySelector('.login-btn');
        const isSignupItem = item.querySelector('.signup-btn');
        const isProfileItem = item.querySelector('.profile-btn');
        const isLogoutItem = item.querySelector('.logout-btn');

        if (isLoginItem || isSignupItem) {
          // Hide login and signup items
          item.style.display = 'none';
        } else if (isProfileItem || isLogoutItem) {
          // Show profile and logout items
          item.style.display = 'flex';
        }
      });

      // User is logged in
      // Hide login and signup buttons
      loginButtons.forEach(btn => {
        // Handle both mobile and desktop navbar items
        if (btn.closest('.mobile-links')) {
          // Mobile menu item
          btn.closest('li').style.display = 'none';
        } else if (btn.closest('.nav-item')) {
          // Desktop navbar item
          btn.closest('.nav-item').style.display = 'none';
        } else {
          // Fallback for other login buttons
          const parent = btn.parentElement;
          if (parent) {
            parent.style.display = 'none';
          }
        }
      });

      signupButtons.forEach(btn => {
        // Handle both mobile and desktop navbar items
        if (btn.closest('.mobile-links')) {
          // Mobile menu item
          btn.closest('li').style.display = 'none';
        } else if (btn.closest('.nav-item')) {
          // Desktop navbar item
          btn.closest('.nav-item').style.display = 'none';
        } else {
          // Fallback for other signup buttons
          const parent = btn.parentElement;
          if (parent) {
            parent.style.display = 'none';
          }
        }
      });

      // Show profile button with dropdown
      profileButtons.forEach(btn => {
        // Handle both mobile and desktop navbar items
        if (btn.closest('.mobile-links')) {
          // Mobile menu item
          btn.closest('li').style.display = 'block';
        } else if (btn.closest('.nav-item')) {
          // Desktop navbar item
          btn.closest('.nav-item').style.display = 'flex';
        } else {
          // Fallback for other profile buttons
          const parent = btn.parentElement;
          if (parent) {
            parent.style.display = 'flex';
          }
        }
      });

      // Show logout buttons
      logoutButtons.forEach(btn => {
        // Handle both mobile and desktop navbar items
        if (btn.closest('.mobile-links')) {
          // Mobile menu item
          btn.closest('li').style.display = 'block';
        } else if (btn.closest('.nav-item')) {
          // Desktop navbar item
          btn.closest('.nav-item').style.display = 'flex';
        }

        // Add event listeners to logout buttons
        btn.addEventListener('click', (e) => {
          e.preventDefault();
          this.logout();
        });
      });

      // Update user profile elements
      userElements.forEach(el => {
        el.textContent = this.user.email;
        el.style.display = 'block';
      });
    } else {
      console.log('User is logged out, updating UI elements');

      // First, handle auth-item elements specifically for map.html
      authItems.forEach(item => {
        const isLoginItem = item.querySelector('.login-btn');
        const isSignupItem = item.querySelector('.signup-btn');
        const isProfileItem = item.querySelector('.profile-btn');
        const isLogoutItem = item.querySelector('.logout-btn');

        if (isLoginItem || isSignupItem) {
          // Show login and signup items
          item.style.display = 'flex';
        } else if (isProfileItem || isLogoutItem) {
          // Hide profile and logout items
          item.style.display = 'none';
        }
      });

      // User is logged out
      // Show login and signup buttons
      loginButtons.forEach(btn => {
        // Handle both mobile and desktop navbar items
        if (btn.closest('.mobile-links')) {
          // Mobile menu item
          btn.closest('li').style.display = 'block';
        } else if (btn.closest('.nav-item')) {
          // Desktop navbar item
          btn.closest('.nav-item').style.display = 'flex';
        } else {
          // Fallback for other login buttons
          const parent = btn.parentElement;
          if (parent) {
            parent.style.display = 'flex';
          }
        }
      });

      signupButtons.forEach(btn => {
        // Handle both mobile and desktop navbar items
        if (btn.closest('.mobile-links')) {
          // Mobile menu item
          btn.closest('li').style.display = 'block';
        } else if (btn.closest('.nav-item')) {
          // Desktop navbar item
          btn.closest('.nav-item').style.display = 'flex';
        } else {
          // Fallback for other signup buttons
          const parent = btn.parentElement;
          if (parent) {
            parent.style.display = 'flex';
          }
        }
      });

      // Hide profile button
      profileButtons.forEach(btn => {
        // Handle both mobile and desktop navbar items
        if (btn.closest('.mobile-links')) {
          // Mobile menu item
          btn.closest('li').style.display = 'none';
        } else if (btn.closest('.nav-item')) {
          // Desktop navbar item
          btn.closest('.nav-item').style.display = 'none';
        } else {
          // Fallback for other profile buttons
          const parent = btn.parentElement;
          if (parent) {
            parent.style.display = 'none';
          }
        }
      });

      // Hide logout buttons
      logoutButtons.forEach(btn => {
        // Handle both mobile and desktop navbar items
        if (btn.closest('.mobile-links')) {
          // Mobile menu item
          btn.closest('li').style.display = 'none';
        } else if (btn.closest('.nav-item')) {
          // Desktop navbar item
          btn.closest('.nav-item').style.display = 'none';
        }
      });

      // Hide user profile elements
      userElements.forEach(el => {
        el.style.display = 'none';
      });
    }
  }
};

// Initialize auth when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM loaded, initializing auth...');
  auth.init();
});

// Also initialize immediately in case the DOM is already loaded
// This helps when the script is loaded after the DOMContentLoaded event
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  console.log('Document already loaded, initializing auth immediately...');
  setTimeout(() => auth.init(), 0);
}
