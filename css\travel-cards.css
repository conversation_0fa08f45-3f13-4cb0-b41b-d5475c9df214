/* Travel Solution Cards Styling */

/* Main card container */
.result-card {
    background-color: #444;
    border-radius: 10px;
    margin-bottom: 20px;
    overflow: hidden;
    display: flex;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border: 1px solid #555;
}

.result-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    border-color: var(--main-color);
}

/* Left side of the card with details */
.result-left {
    flex: 3;
    padding: 15px;
    border-right: 1px solid #555;
}

/* Right side with price and buttons */
.result-right {
    flex: 1;
    padding: 15px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    background-color: #3a3a3a;
}

/* Card header with carrier info */
.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #555;
}

.carrier {
    display: flex;
    align-items: center;
}

.carrier-logo {
    width: 40px;
    height: 40px;
    background-color: var(--main-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    color: white;
    font-size: 18px;
}

.carrier-name {
    font-weight: 600;
    font-size: 16px;
    color: white;
}

.flight-number {
    font-size: 14px;
    color: #aaa;
    background-color: #333;
    padding: 4px 8px;
    border-radius: 4px;
}

/* Card body with route info */
.result-body {
    margin-bottom: 15px;
}

.time-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.time-group {
    text-align: center;
    flex: 1;
}

.time {
    font-size: 18px;
    font-weight: 600;
    color: white;
    margin-bottom: 5px;
}

.airport {
    font-size: 14px;
    color: #bbb;
}

/* Flight path visualization */
.flight-path {
    flex: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 15px;
    position: relative;
}

.duration {
    font-size: 14px;
    color: #aaa;
    margin-bottom: 5px;
}

.path-line {
    height: 2px;
    background-color: var(--main-color);
    width: 100%;
    position: relative;
    margin: 5px 0;
}

.path-line:before,
.path-line:after {
    content: '';
    position: absolute;
    width: 6px;
    height: 6px;
    background-color: var(--main-color);
    border-radius: 50%;
    top: -2px;
}

.path-line:before {
    left: 0;
}

.path-line:after {
    right: 0;
}

.stops {
    font-size: 12px;
    color: #aaa;
    margin-top: 5px;
    padding: 2px 8px;
    background-color: #333;
    border-radius: 10px;
}

/* Amenities section */
.amenities {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.amenity {
    font-size: 12px;
    color: #bbb;
    background-color: #333;
    padding: 4px 8px;
    border-radius: 4px;
    display: flex;
    align-items: center;
}

.amenity i {
    color: var(--highlight-color);
    margin-right: 5px;
    font-size: 10px;
}

/* Price section */
.price-container {
    text-align: center;
    margin-bottom: 15px;
}

.price {
    font-size: 24px;
    font-weight: 700;
    color: white;
    margin-bottom: 5px;
}

.price-subtitle {
    font-size: 12px;
    color: #aaa;
}

/* Buttons */
.select-btn,
.book-btn {
    width: 100%;
    padding: 10px 0;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    text-decoration: none;
}

.book-btn {
    background-color: var(--main-color);
    color: white;
}

.book-btn:hover {
    background-color: #a52020;
}

.select-btn {
    background-color: #555;
    color: white;
}

.select-btn:hover {
    background-color: #666;
}

.book-btn i,
.select-btn i {
    margin-right: 5px;
}

/* Reserved state */
.select-btn.reserved {
    background-color: #2e7d32;
    cursor: default;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .result-card {
        flex-direction: column;
    }

    .result-left {
        border-right: none;
        border-bottom: 1px solid #555;
    }

    .result-right {
        padding: 15px;
    }

    .time-info {
        flex-direction: column;
        gap: 15px;
    }

    .flight-path {
        width: 100%;
        padding: 15px 0;
    }
}

/* Reservation card styles */
.reservation-card {
    background-color: #444;
    border-radius: 10px;
    margin-bottom: 15px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    border: 1px solid #555;
    transition: all 0.3s ease;
}

.reservation-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border-color: var(--main-color);
}

.reservation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #3a3a3a;
    border-bottom: 1px solid #555;
}

.reservation-type {
    display: flex;
    align-items: center;
    font-weight: 600;
}

.reservation-type i {
    margin-right: 8px;
    color: var(--main-color);
}

.reservation-actions {
    display: flex;
    gap: 5px;
}

.reservation-actions button {
    background: none;
    border: none;
    color: #aaa;
    cursor: pointer;
    font-size: 14px;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.add-to-itinerary {
    color: #4caf50 !important;
}

.edit-reservation {
    color: #2196f3 !important;
}

.remove-reservation {
    color: #f44336 !important;
}

.reservation-actions button:hover {
    background-color: #555;
    color: #fff !important;
}