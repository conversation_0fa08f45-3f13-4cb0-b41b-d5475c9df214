/* Navbar */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: var(--dark-color);
  color: #fff;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000000;
  /* More reasonable z-index value */
  box-sizing: border-box;
  /* Ensure padding is included in the width */
  min-height: 60px;
  /* Set a minimum height for the navbar */
}

.navbar .logo img {
  height: 40px;
  margin: 0px;
  /* Remove top margin */
  margin-top: 0px;
}

.navbar .search-container {
  flex-grow: 1;
  /* Allow the search container to grow */
  display: flex;
  justify-content: center;
  /* Center the search bar */
  align-items: center;
  margin: 10px 20px;
  /* Add margin to separate from logo and icons */
}

.navbar .search-bar {
  width: 100%;
  /* Take full width of the container */
  max-width: 500px;
  /* Limit the maximum width */
  border-radius: 20px;
  padding: 15px 15px;
  font-size: 14px;
  background-color: #5a5a5a;
  color: white;
  transition: width 0.3s ease;
  height: 40px;
  /* Match the height of the login button */
}

.navbar .search-bar::placeholder {
  color: #fff;
}

.navbar .search-bar:focus {
  outline: none;
  background-color: #ffd700;
}

.nav-links {
  display: flex;
  list-style-type: none;
  align-items: center;
  /* Align items vertically */
}

.nav-item {
  position: relative;
  margin: 0 15px;
}

.nav-link {
  display: flex;
  align-items: center;
  color: #fff;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
  height: 40px;
  /* Set a consistent height for all navbar items */
}

.nav-link i {
  font-size: 20px;
  margin-right: 8px;
  transition: color 0.3s ease;
}

/* Active state for desktop navigation */
.nav-links .nav-item .nav-link.active,
.nav-links .nav-item .nav-link.active i {
  color: #ffd700 !important;
}

/* Active state for mobile navigation */
.mobile-links .nav-link.active,
.mobile-links .nav-link.active i {
  color: #ffd700 !important;
}

/* Hover states */
.nav-item:hover .nav-link:not(.active),
.nav-item:hover .nav-link:not(.active) i {
  color: #ffd700;
}

/* Auth buttons - common properties */
.nav-link.auth-link {
  padding: 5px 15px;
  border-radius: 20px;
  font-weight: bold;
  transition: all 0.3s ease;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Login button styling */
.nav-link.auth-link.login-btn {
  background-color: #ffd700;
  /* Gold/yellow */
  color: #262626;
  /* Dark gray/black */
  padding: 5px 25px;
  border-radius: 20px;
  font-weight: bold;
  transition: background-color 0.3s ease, color 0.3s ease;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-link.auth-link.login-btn:hover {
  background-color: #861818;
  /* Vestigia red */
  color: #ffd700;
  /* Gold/yellow */
}

/* Sign up button styling */
.nav-link.auth-link.signup-btn {
  background-color: #861818;
  /* Vestigia red */
  color: #ffd700;
  /* Gold/yellow text */
  padding: 5px 25px;
  border-radius: 20px;
  font-weight: bold;
  transition: all 0.3s ease;
  height: 40px;
  margin-left: -10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-link.auth-link.signup-btn:hover {
  background-color: #6b1313;
  /* Darker red on hover */
  color: #ffd700;
  /* Keep yellow text */
  transform: scale(1.05);
  /* Slight scale effect on hover */
}

/* Profile and auth buttons */
.nav-item.auth-item {
  margin-left: 10px;
  margin-right: 10px;
}

.nav-link.auth-link {
  padding: 5px 15px;
}

/* Profile button specific styling */
.nav-link.auth-link.profile-btn {
  padding: 5px 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
}

/* For mobile menu */
.mobile-links .login-btn,
.mobile-links .profile-btn,
.mobile-links .logout-btn {
  margin: 5px auto;
  width: 60%;
  max-width: 150px;
  border-radius: 20px;
  padding: 8px 0;
  /* Reduced and even padding */
  display: flex;
  align-items: center;
  justify-content: center;
  height: 36px;
  /* Fixed height for better centering */
}

/* Mobile signup button */
.mobile-links .signup-btn {
  margin: 5px auto;
  width: 60%;
  max-width: 150px;
  background-color: #861818;
  /* Vestigia red */
  color: #ffd700;
  /* Yellow text */
  font-weight: bold;
  border-radius: 20px;
  padding: 8px 0;
  /* Reduced and even padding */
  display: flex;
  align-items: center;
  justify-content: center;
  height: 36px;
  /* Fixed height for better centering */
}

.mobile-links .signup-btn:hover {
  background-color: #6b1313;
  /* Darker red on hover */
  transform: scale(1.02);
  /* Slight scale effect on hover */
}

.tooltip {
  display: none;
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #ffd700;
  color: #1e1e1e;
  padding: 5px;
  font-size: 12px;
  border-radius: 4px;
  white-space: nowrap;
  z-index: 10;
}

.nav-item:hover .tooltip {
  display: block;
}

.nav-item:hover .nav-link {
  color: #ffd700;
}

.nav-item:hover .dropdown-menu {
  display: block;
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  right: 0;
}

.dropdown-menu {
  display: none;
  position: absolute;
  top: 100%;
  right: 0;
  transform: translateY(5px);
  background-color: #1e1e1e;
  padding: 10px;
  border-radius: 5px;
  width: 120px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, transform 0.2s ease, visibility 0.2s;
  z-index: 1001;
}

.dropdown-menu a {
  color: #fff;
  text-decoration: none;
  padding: 8px 10px;
  display: block;
  text-align: left;
  font-size: 14px;
  border-radius: 3px;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.dropdown-menu a:hover {
  color: #ffd700;
  background-color: #333333;
}

/* Mobile Hamburger Menu */
.hamburger-menu {
  display: none;
  position: relative;
}

.hamburger-btn {
  background: none;
  border: none;
  color: #fff;
  /* White/gray color for simplicity */
  font-size: 30px;
  cursor: pointer;
  padding: 10px;
  /* Larger touch target */
  outline: none;
  /* Remove outline on focus */
  -webkit-tap-highlight-color: transparent;
  /* Remove tap highlight on mobile */
}

.mobile-menu {
  position: fixed;
  /* Fixed position to cover the entire viewport */
  top: 0;
  left: 0;
  background-color: #1e1e1e;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  height: 100vh;
  /* Full viewport height */
  overflow-y: auto;
  width: 100%;
  /* Ensure it takes the full width of the screen */
  text-align: center;
  /* Center align the text */
  display: flex;
  flex-direction: column;
  align-items: center;
  /* Center align the items */
  justify-content: flex-start;
  /* Align items from the top */
  padding-top: 80px;
  /* Add padding at the top to push content below navbar */
  z-index: 1000001;
  /* Ensure it's above all other elements */
  -webkit-overflow-scrolling: touch;
  /* Smooth scrolling on iOS */
  visibility: visible;
  opacity: 1;
}

.mobile-menu.open {
  transform: translateX(0) !important;
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.mobile-links {
  list-style-type: none;
  padding: 0;
  /* Remove padding */
  margin: 0;
  /* Remove margin */
  width: 100%;
  /* Ensure the links take the full width */
  display: flex;
  flex-direction: column;
  align-items: center;
  /* Center align the items */
}

.mobile-links li {
  margin: 25px 0;
  padding: 0;
  /* Remove padding */
  width: 100%;
  /* Take full width */
  text-align: center;
}

.mobile-links a {
  color: #fff;
  text-decoration: none;
  font-size: 20px;
  /* Larger font size */
  display: block;
  padding: 15px 0;
  /* Add padding for larger touch target */
  font-weight: 600;
  /* Make text slightly bolder */
}

.mobile-links a.active {
  color: #ffd700 !important;
  /* Highlight color for active link */
}

.mobile-links a.active i {
  color: #ffd700 !important;
  /* Highlight color for active link icon */
}

.mobile-links a i {
  margin-right: 10px;
  /* Add space between icon and text */
  font-size: 22px;
  /* Slightly larger icons */
}

/* Ensure icons in login/signup buttons are properly aligned */
.mobile-links .login-btn i,
.mobile-links .signup-btn i {
  margin-right: 8px;
  font-size: 18px;
}

.mobile-links a:hover,
.mobile-links a.active {
  color: #ffd700;
}

.close-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  color: #fff;
  /* White color for simplicity */
  font-size: 30px;
  cursor: pointer;
  z-index: 1000002;
  /* Ensure it's above the mobile menu */
  padding: 10px;
  /* Larger touch target */
  -webkit-tap-highlight-color: transparent;
  /* Remove tap highlight on mobile */
  outline: none;
  /* Remove outline on focus */
}

/* Media Queries */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .hamburger-menu {
    display: block;
  }

  .navbar .logo {
    display: block;
  }

  .search-container {
    display: block;
  }

  /* Mobile menu is always in the DOM but transformed off-screen */
  .mobile-menu {
    display: flex;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .mobile-menu.open {
    transform: translateX(0);
    display: flex !important;
  }

  .nav-item {
    margin: 0 10px;
  }

  .navbar .logo img {
    height: 30px;
  }

  .mobile-links {
    padding: 0;
  }

  .mobile-links li {
    margin: 15px 0;
  }

  /* Improve touch targets for mobile */
  .hamburger-btn,
  .close-btn {
    padding: 15px;
    min-width: 44px;
    min-height: 44px;
  }
}

/* Show mobile menu when hamburger is clicked */
.hamburger-menu.open .mobile-menu {
  transform: translateX(0);
}

/* Hide mobile menu when clicking outside */
body.overlay-open {
  overflow: hidden;
}

body.overlay-open .mobile-menu {
  transform: translateX(0);
}

/* Add overlay effect when mobile menu is open */
body.overlay-open::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000000;
  /* Just below the mobile menu */
  pointer-events: none;
  /* Allow clicks to pass through */
}

/* Search Bar */
.search-container {
  display: flex;
  align-items: center;
  margin: 0 15px;
  padding: 0;
}

.search-bar {
  border: none;
  border-radius: 20px;
  height: 40px;
  /* Match the height of the login button */
  font-size: 14px;
  width: 100%;
  max-width: 500px;
  transition: width 0.3s ease;
  background-color: #5a5a5a;
  color: white;
}

.search-bar::placeholder {
  color: #fff;
}

.search-bar:focus {
  outline: none;
  background-color: #ffd700;
  color: var(--main-color);
}

/* Navbar links */
.nav-links .search-container {
  display: flex;
  align-items: center;
  margin: 0 20px;
  padding: 0;
  /* Ensure no extra padding */
}

.nav-links .search-bar {
  width: 150px;
  /* Default width for desktop */
  border-radius: 20px;
  font-size: 14px;
}

/* Mobile menu styling */
.mobile-links .search-container {
  margin-top: 15px;
  margin-left: 20px;
  padding: 0;
  /* Remove extra padding in mobile menu */
}

.mobile-links .search-bar {
  width: 100%;
  border-radius: 20px;
  padding: 8px 15px;
  font-size: 14px;
}

/* Mobile profile dropdown */
.mobile-profile-item {
  position: relative;
}

.mobile-dropdown {
  display: none;
  background-color: #333;
  border-radius: 5px;
  margin: 5px 0;
  padding: 5px;
  width: 90%;
  margin-left: auto;
  margin-right: auto;
  text-align: left;
}

.mobile-profile-item.active .mobile-dropdown {
  display: block;
}

.mobile-dropdown a {
  padding: 10px !important;
  border-radius: 3px;
  margin: 5px 0;
  text-align: left;
}

.mobile-dropdown a:hover {
  background-color: #444;
}

/* Responsive for mobile */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .hamburger-menu {
    display: block;
  }

  .search-bar {
    width: 200px;
    /* Expand search bar for mobile */
  }

  .mobile-links .search-bar {
    width: 100%;
  }

  /* Fix for mobile menu display */
  .mobile-menu {
    display: block;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: 10000;
  }

  .mobile-menu.open {
    transform: translateX(0);
    display: block;
  }
}

/* Mobile menu active states */
.mobile-links .nav-link.active {
  color: #ffd700 !important;
}

.mobile-links .nav-link.active i {
  color: #ffd700 !important;
}