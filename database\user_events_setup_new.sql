-- SQL script to set up user_events table for itinerary functionality
-- Similar to user_markers table but for events
-- Run this directly in the Supabase SQL Editor

-- Drop the table if it exists and start fresh
DROP TABLE IF EXISTS user_events;

-- Create the table with all needed columns
CREATE TABLE user_events (
  id SERIAL PRIMARY KEY,
  user_id TEXT NOT NULL,
  event_id TEXT NOT NULL, -- Client-generated ID for syncing (similar to marker IDs)
  title TEXT NOT NULL,
  start_time TIMESTAMP WITH TIME ZONE,
  end_time TIMESTAMP WITH TIME ZONE,
  description TEXT,
  location TEXT,
  category TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_user_events_user_id ON user_events(user_id);
CREATE INDEX IF NOT EXISTS idx_user_events_event_id ON user_events(event_id);
CREATE INDEX IF NOT EXISTS idx_user_events_start_time ON user_events(start_time);

-- Create a unique constraint to prevent duplicate events (same user + event_id)
ALTER TABLE user_events ADD CONSTRAINT unique_user_event UNIQUE (user_id, event_id);

-- Add updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS set_updated_at ON user_events;
CREATE TRIGGER set_updated_at
BEFORE UPDATE ON user_events
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Enable Row Level Security
ALTER TABLE user_events ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to select only their own events
DROP POLICY IF EXISTS select_own_events ON user_events;
CREATE POLICY select_own_events ON user_events
  FOR SELECT USING (auth.uid()::text = user_id);

-- Create policy to allow users to insert only their own events
DROP POLICY IF EXISTS insert_own_events ON user_events;
CREATE POLICY insert_own_events ON user_events
  FOR INSERT WITH CHECK (auth.uid()::text = user_id);

-- Create policy to allow users to update only their own events
DROP POLICY IF EXISTS update_own_events ON user_events;
CREATE POLICY update_own_events ON user_events
  FOR UPDATE USING (auth.uid()::text = user_id);

-- Create policy to allow users to delete only their own events
DROP POLICY IF EXISTS delete_own_events ON user_events;
CREATE POLICY delete_own_events ON user_events
  FOR DELETE USING (auth.uid()::text = user_id);

-- Create policy to allow service role to access all events (for Netlify functions)
DROP POLICY IF EXISTS service_role_access_events ON user_events;
CREATE POLICY service_role_access_events ON user_events
  FOR ALL USING (auth.role() = 'service_role');

-- Add policy to allow anonymous role to insert events (for development/testing)
DROP POLICY IF EXISTS anon_insert_events ON user_events;
CREATE POLICY anon_insert_events ON user_events
  FOR INSERT TO anon
  WITH CHECK (true);

-- Add policy to allow anonymous role to select events (for development/testing)
DROP POLICY IF EXISTS anon_select_events ON user_events;
CREATE POLICY anon_select_events ON user_events
  FOR SELECT TO anon
  USING (true);

-- Add policy to allow anonymous role to update events (for development/testing)
DROP POLICY IF EXISTS anon_update_events ON user_events;
CREATE POLICY anon_update_events ON user_events
  FOR UPDATE TO anon
  USING (true);

-- Add policy to allow anonymous role to delete events (for development/testing)
DROP POLICY IF EXISTS anon_delete_events ON user_events;
CREATE POLICY anon_delete_events ON user_events
  FOR DELETE TO anon
  USING (true);

-- Add comments for documentation
COMMENT ON TABLE user_events IS 'Stores user itinerary events for the Vestigia application';
COMMENT ON COLUMN user_events.user_id IS 'ID of the user who owns this event';
COMMENT ON COLUMN user_events.event_id IS 'Client-generated unique ID for syncing between local storage and Supabase';
COMMENT ON COLUMN user_events.title IS 'Title/name of the event';
COMMENT ON COLUMN user_events.start_time IS 'Start date and time of the event';
COMMENT ON COLUMN user_events.end_time IS 'End date and time of the event (optional)';
COMMENT ON COLUMN user_events.description IS 'Additional information about the event';
COMMENT ON COLUMN user_events.location IS 'Location where the event takes place';
COMMENT ON COLUMN user_events.category IS 'Category of the event (cultural, historical, etc.)';

-- Grant necessary permissions
GRANT ALL ON user_events TO authenticated;
GRANT ALL ON user_events TO service_role;
GRANT ALL ON user_events TO anon;

-- Grant sequence permissions
GRANT USAGE, SELECT ON SEQUENCE user_events_id_seq TO authenticated;
GRANT USAGE, SELECT ON SEQUENCE user_events_id_seq TO service_role;
GRANT USAGE, SELECT ON SEQUENCE user_events_id_seq TO anon;

-- Verify the table was created successfully
SELECT 'user_events table created successfully' AS status;
