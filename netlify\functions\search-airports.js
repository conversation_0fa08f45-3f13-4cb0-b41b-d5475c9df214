// Netlify function for searching airports via Amadeus API
const axios = require('axios');

// Amadeus API endpoints
const AMADEUS_AUTH_URL = 'https://test.api.amadeus.com/v1/security/oauth2/token';
const AMADEUS_AIRPORT_SEARCH_URL = 'https://test.api.amadeus.com/v1/reference-data/locations';

// Function to get Amadeus access token
async function getAmadeusToken() {
  try {
    // Use the API credentials from environment variables or hardcoded fallback
    const AMADEUS_API_KEY = process.env.AMADEUS_API_KEY || '********************************';
    const AMADEUS_API_SECRET = process.env.AMADEUS_API_SECRET || 'Rlqq9tyhGebLjV6d';

    const response = await axios({
      method: 'post',
      url: AMADEUS_AUTH_URL,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: `grant_type=client_credentials&client_id=${AMADEUS_API_KEY}&client_secret=${AMADEUS_API_SECRET}`
    });

    return response.data.access_token;
  } catch (error) {
    console.error('Error getting Amadeus token:', error.response?.data || error.message);
    throw new Error('Failed to authenticate with Amadeus API');
  }
}

// Main handler function
exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*', // Or set to your specific domain
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
  };

  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  try {
    // Parse query parameters
    const params = event.queryStringParameters || {};
    const { keyword } = params;

    // Validate required parameters
    if (!keyword) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing required parameter: keyword' })
      };
    }

    // Get Amadeus access token
    const token = await getAmadeusToken();

    // Call Amadeus Airport Search API
    const response = await axios({
      method: 'get',
      url: AMADEUS_AIRPORT_SEARCH_URL,
      headers: {
        'Authorization': `Bearer ${token}`
      },
      params: {
        keyword: keyword,
        subType: 'AIRPORT,CITY',
        'page[limit]': 10
      }
    });

    // Return successful response
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify(response.data)
    };
  } catch (error) {
    console.error('Error searching airports:', error.response?.data || error.message);

    // Return error response
    return {
      statusCode: error.response?.status || 500,
      headers,
      body: JSON.stringify({
        error: error.response?.data?.errors?.[0]?.detail || 'Failed to search airports',
        message: error.message
      })
    };
  }
};
