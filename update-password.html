<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Password - Vestigia</title>
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&family=Playfair+Display:wght@700;900&display=swap">
    <link rel="icon" type="image/png" sizes="96x96" href="favicon/favicon-96x96.png">
    <link rel="icon" href="favicon/favicon.ico">
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        html {
            scroll-behavior: smooth;
        }
        
        body {
            overflow-x: hidden;
        }

        .password-requirements {
            margin-top: 10px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 5px;
            font-size: 0.85rem;
        }

        .requirement {
            display: flex;
            align-items: center;
            margin: 5px 0;
            color: var(--text-light);
        }

        .requirement.valid {
            color: var(--success-color);
        }

        .requirement i {
            margin-right: 8px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body class="auth-page">
    <!-- Navbar will be injected here by navbar-loader.js -->
    <div id="navbar-container"></div>
    <div class="auth-overlay"></div>

    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header" style="background: transparent;">
                <img src="images/vestigiaLogoBig.png" alt="Vestigia Logo" class="auth-logo" style="background: #262626; padding: 10px; border-radius: 8px;">
                <h1>Update Password</h1>
                <p>Create a new password for your account</p>
            </div>
            
            <form class="auth-form" id="updatePasswordForm">
                <div id="error-message" class="error-message"></div>
                <div id="success-message" class="success-message" style="display: none;"></div>
                
                <div class="auth-form-content">
                    <div class="form-group">
                        <label for="password">New Password</label>
                        <div class="input-with-icon">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="password" name="password" placeholder="Enter new password" required>
                            <i class="fas fa-eye toggle-password" onclick="togglePasswordVisibility('password')"></i>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword">Confirm New Password</label>
                        <div class="input-with-icon">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="confirmPassword" name="confirmPassword" placeholder="Confirm new password" required>
                            <i class="fas fa-eye toggle-password" onclick="togglePasswordVisibility('confirmPassword')"></i>
                        </div>
                    </div>

                    <div class="password-requirements">
                        <p>Password must contain:</p>
                        <div class="requirement" id="length">
                            <i class="fas" id="length-icon"></i>
                            <span>At least 8 characters</span>
                        </div>
                        <div class="requirement" id="uppercase">
                            <i class="fas" id="uppercase-icon"></i>
                            <span>At least one uppercase letter</span>
                        </div>
                        <div class="requirement" id="number">
                            <i class="fas" id="number-icon"></i>
                            <span>At least one number</span>
                        </div>
                    </div>

                    <button type="submit" class="auth-button" id="updateButton" disabled>
                        Update Password
                        <i class="fas fa-arrow-right"></i>
                    </button>
                </div>

                <div class="auth-footer">
                    <p>Remembered your password? <a href="auth-login.html">Sign In</a></p>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="js/supabase-config.js"></script>
    <script src="js/navbar-loader.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const updatePasswordForm = document.getElementById('updatePasswordForm');
            const errorMessage = document.getElementById('error-message');
            const successMessage = document.getElementById('success-message');
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirmPassword');
            const updateButton = document.getElementById('updateButton');

            // Check if we're in a password recovery flow
            const urlParams = new URLSearchParams(window.location.hash.substring(1));
            const accessToken = urlParams.get('access_token');
            const refreshToken = urlParams.get('refresh_token');
            const type = urlParams.get('type');

            if (type === 'recovery' && accessToken && refreshToken) {
                // Set the session with the tokens from the URL
                supabase.auth.setSession({
                    access_token: accessToken,
                    refresh_token: refreshToken
                }).then(({ error }) => {
                    if (error) {
                        console.error('Error setting session:', error);
                        errorMessage.textContent = 'This link is invalid or has expired. Please request a new password reset link.';
                        errorMessage.style.display = 'block';
                    }
                });
            }


            // Password validation
            function validatePassword(password) {
                const hasMinLength = password.length >= 8;
                const hasUppercase = /[A-Z]/.test(password);
                const hasNumber = /[0-9]/.test(password);
                const isValid = hasMinLength && hasUppercase && hasNumber;

                // Update UI
                document.getElementById('length').classList.toggle('valid', hasMinLength);
                document.getElementById('length-icon').className = hasMinLength ? 'fas fa-check' : 'fas fa-times';
                
                document.getElementById('uppercase').classList.toggle('valid', hasUppercase);
                document.getElementById('uppercase-icon').className = hasUppercase ? 'fas fa-check' : 'fas fa-times';
                
                document.getElementById('number').classList.toggle('valid', hasNumber);
                document.getElementById('number-icon').className = hasNumber ? 'fas fa-check' : 'fas fa-times';

                return isValid;
            }

            // Check if passwords match
            function passwordsMatch() {
                const password = passwordInput.value;
                const confirmPassword = confirmPasswordInput.value;
                return password === confirmPassword && password.length > 0;
            }

            // Update button state based on form validity
            function updateButtonState() {
                const password = passwordInput.value;
                const isPasswordValid = validatePassword(password);
                const doPasswordsMatch = passwordsMatch();
                
                updateButton.disabled = !(isPasswordValid && doPasswordsMatch);
            }

            // Toggle password visibility
            window.togglePasswordVisibility = function(inputId) {
                const input = document.getElementById(inputId);
                const icon = input.nextElementSibling;
                
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            };

            // Event listeners
            passwordInput.addEventListener('input', updateButtonState);
            confirmPasswordInput.addEventListener('input', updateButtonState);

            updatePasswordForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const password = passwordInput.value;
                const confirmPassword = confirmPasswordInput.value;
                
                errorMessage.textContent = '';
                errorMessage.style.display = 'none';
                successMessage.style.display = 'none';

                // Client-side validation
                if (!validatePassword(password)) {
                    errorMessage.textContent = 'Please ensure your password meets all requirements.';
                    errorMessage.style.display = 'block';
                    return;
                }


                if (password !== confirmPassword) {
                    errorMessage.textContent = 'Passwords do not match.';
                    errorMessage.style.display = 'block';
                    return;
                }


                try {
                    const { error } = await supabase.auth.updateUser({
                        password: password
                    });

                    if (error) throw error;

                    // Show success message
                    successMessage.textContent = 'Your password has been updated successfully! Redirecting to login...';
                    successMessage.style.display = 'block';
                    
                    // Redirect to login after a short delay
                    setTimeout(() => {
                        window.location.href = 'auth-login.html';
                    }, 3000);
                } catch (error) {
                    console.error('Error updating password:', error);
                    errorMessage.textContent = error.message || 'An error occurred while updating your password. Please try again.';
                    errorMessage.style.display = 'block';
                }
            });
        });
    </script>
</body>
</html>
