# Vestigia Setup Instructions

## Backend Server Setup

The Vestigia website uses a backend server to protect API keys and provide secure endpoints for features like airport search, flight search, and map directions.

### Prerequisites

- Node.js and npm installed on your computer
- The project files downloaded to your local machine

### Starting the Server

1. Open a terminal or command prompt
2. Navigate to the project directory:
   ```
   cd path/to/vestigiawebsite
   ```
3. Start the server:
   ```
   node server.js
   ```
4. You should see the following output:
   ```
   Server running on port 3000
   API available at http://localhost:3000/api
   ```
5. Keep this terminal window open while using the website

### Verifying the Server is Running

To check if the server is running correctly, open a new terminal window and run:
```
curl http://localhost:3000/api/health
```

You should see a response like:
```json
{"status":"ok","message":"Server is running"}
```

### Environment Variables

The server uses environment variables from the `.env` file to store API keys securely. This file should already be set up with the necessary keys.

If you need to add or modify API keys, edit the `.env` file in the project root directory.

### Fallback Data

The server includes fallback data for airport searches in case the external API is unavailable or the API key is invalid. This ensures that the airport search functionality will continue to work even if there are issues with the external API.

The fallback data includes a list of major international airports that can be searched by code, name, city, or country.

## Using the Website

1. Make sure the server is running as described above
2. Open the website in your browser by opening any of the HTML files
3. Features that require API access (like airport search) will now work correctly

## Troubleshooting

If you see a red error banner at the top of the page saying "Server Not Running!", follow these steps:

1. Check if you have the server running in a terminal window
2. If not, start it using the instructions above
3. If the server is running but you still see the error, check if it's running on port 3000
4. Make sure there are no firewall issues blocking access to localhost:3000

## Development Notes

- The frontend communicates with the backend using the API client in `js/api-client.js`
- API keys are stored in the `.env` file and accessed by the server
- The server provides endpoints for various API services without exposing the keys to the frontend
