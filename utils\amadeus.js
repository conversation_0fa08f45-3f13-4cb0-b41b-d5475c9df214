const axios = require('axios');

// Amadeus token storage
let amadeusToken = null;
let tokenExpiration = null;

/**
 * Get an access token from Amadeus API
 * @returns {Promise<string>} The access token
 */
async function getAmadeusToken() {
  // Check if we have a valid token
  if (amadeusToken && tokenExpiration && new Date() < tokenExpiration) {
    return amadeusToken;
  }

  try {
    const response = await axios({
      method: 'POST',
      url: 'https://test.api.amadeus.com/v1/security/oauth2/token',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: `grant_type=client_credentials&client_id=${process.env.AMADEUS_API_KEY}&client_secret=${process.env.AMADEUS_API_SECRET}`
    });

    amadeusToken = response.data.access_token;
    // Set expiration time (token is valid for 30 minutes, we set it to 29 to be safe)
    tokenExpiration = new Date(new Date().getTime() + 29 * 60000);
    
    return amadeusToken;
  } catch (error) {
    console.error('Error getting Amadeus token:', error.response?.data || error.message);
    throw new Error('Failed to get authentication token');
  }
}

module.exports = {
  getAmadeusToken
};
