-- SQL script for setting up itinerary sync functionality in Supabase
-- Run this directly in the Supabase SQL Editor

-- Create the user_events table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_events (
  id SERIAL PRIMARY KEY,
  user_id TEXT NOT NULL,
  event_id TEXT NOT NULL, -- Client-generated ID for syncing
  title TEXT NOT NULL,
  start_time TIMESTAMP WITH TIME ZONE,
  end_time TIMESTAMP WITH TIME ZONE,
  description TEXT,
  location TEXT,
  category TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_user_events_user_id ON user_events(user_id);
CREATE INDEX IF NOT EXISTS idx_user_events_event_id ON user_events(event_id);
CREATE INDEX IF NOT EXISTS idx_user_events_start_time ON user_events(start_time);

-- Create a unique constraint to prevent duplicate events
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint WHERE conname = 'unique_user_event'
  ) THEN
    ALTER TABLE user_events ADD CONSTRAINT unique_user_event UNIQUE (user_id, event_id);
  END IF;
END
$$;

-- Add updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS set_updated_at ON user_events;
CREATE TRIGGER set_updated_at
BEFORE UPDATE ON user_events
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Enable Row Level Security
ALTER TABLE user_events ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to select only their own events
DROP POLICY IF EXISTS select_own_events ON user_events;
CREATE POLICY select_own_events ON user_events
  FOR SELECT USING (auth.uid()::text = user_id);

-- Create policy to allow users to insert only their own events
DROP POLICY IF EXISTS insert_own_events ON user_events;
CREATE POLICY insert_own_events ON user_events
  FOR INSERT WITH CHECK (auth.uid()::text = user_id);

-- Create policy to allow users to update only their own events
DROP POLICY IF EXISTS update_own_events ON user_events;
CREATE POLICY update_own_events ON user_events
  FOR UPDATE USING (auth.uid()::text = user_id);

-- Create policy to allow users to delete only their own events
DROP POLICY IF EXISTS delete_own_events ON user_events;
CREATE POLICY delete_own_events ON user_events
  FOR DELETE USING (auth.uid()::text = user_id);

-- Create policy to allow service role to access all events
DROP POLICY IF EXISTS service_role_access_events ON user_events;
CREATE POLICY service_role_access_events ON user_events
  FOR ALL USING (auth.role() = 'service_role');

-- Create function to sync events
CREATE OR REPLACE FUNCTION sync_user_events(
  p_user_id TEXT,
  p_events JSONB
)
RETURNS JSONB AS $$
DECLARE
  v_result JSONB := '{"inserted": [], "updated": [], "deleted": [], "errors": []}'::JSONB;
  v_event JSONB;
  v_db_event RECORD;
  v_client_event_ids TEXT[] := '{}';
  v_error TEXT;
BEGIN
  -- Extract all event IDs from the client events
  FOR v_event IN SELECT * FROM jsonb_array_elements(p_events)
  LOOP
    v_client_event_ids := v_client_event_ids || (v_event->>'id')::TEXT;
  END LOOP;

  -- Process each event from the client
  FOR v_event IN SELECT * FROM jsonb_array_elements(p_events)
  LOOP
    BEGIN
      -- Check if this event already exists
      SELECT * INTO v_db_event
      FROM user_events
      WHERE user_id = p_user_id AND event_id = (v_event->>'id')::TEXT;

      IF v_db_event.id IS NOT NULL THEN
        -- Update existing event
        UPDATE user_events
        SET
          title = (v_event->>'title')::TEXT,
          start_time = (v_event->>'start')::TIMESTAMP WITH TIME ZONE,
          end_time = CASE WHEN v_event->>'end' IS NULL THEN NULL ELSE (v_event->>'end')::TIMESTAMP WITH TIME ZONE END,
          description = (v_event->>'description')::TEXT,
          location = (v_event->>'location')::TEXT,
          category = (v_event->>'category')::TEXT,
          updated_at = NOW()
        WHERE user_id = p_user_id AND event_id = (v_event->>'id')::TEXT
        RETURNING id, event_id, title, start_time, end_time, description, location, category
        INTO v_db_event;

        -- Add to updated events
        v_result := jsonb_set(
          v_result,
          '{updated}',
          (v_result->'updated') || jsonb_build_object(
            'id', v_db_event.id,
            'event_id', v_db_event.event_id,
            'title', v_db_event.title,
            'start_time', v_db_event.start_time,
            'end_time', v_db_event.end_time,
            'description', v_db_event.description,
            'location', v_db_event.location,
            'category', v_db_event.category
          )
        );
      ELSE
        -- Insert new event
        INSERT INTO user_events (
          user_id,
          event_id,
          title,
          start_time,
          end_time,
          description,
          location,
          category
        ) VALUES (
          p_user_id,
          (v_event->>'id')::TEXT,
          (v_event->>'title')::TEXT,
          (v_event->>'start')::TIMESTAMP WITH TIME ZONE,
          CASE WHEN v_event->>'end' IS NULL THEN NULL ELSE (v_event->>'end')::TIMESTAMP WITH TIME ZONE END,
          (v_event->>'description')::TEXT,
          (v_event->>'location')::TEXT,
          (v_event->>'category')::TEXT
        )
        RETURNING id, event_id, title, start_time, end_time, description, location, category
        INTO v_db_event;

        -- Add to inserted events
        v_result := jsonb_set(
          v_result,
          '{inserted}',
          (v_result->'inserted') || jsonb_build_object(
            'id', v_db_event.id,
            'event_id', v_db_event.event_id,
            'title', v_db_event.title,
            'start_time', v_db_event.start_time,
            'end_time', v_db_event.end_time,
            'description', v_db_event.description,
            'location', v_db_event.location,
            'category', v_db_event.category
          )
        );
      END IF;
    EXCEPTION WHEN OTHERS THEN
      -- Add to errors
      GET STACKED DIAGNOSTICS v_error = PG_EXCEPTION_CONTEXT;
      v_result := jsonb_set(
        v_result,
        '{errors}',
        (v_result->'errors') || jsonb_build_object(
          'event_id', (v_event->>'id')::TEXT,
          'message', SQLERRM,
          'detail', SQLSTATE,
          'context', v_error
        )
      );
    END;
  END LOOP;

  -- Delete events that exist in the database but not in the client's list
  FOR v_db_event IN
    SELECT id, event_id
    FROM user_events
    WHERE user_id = p_user_id AND event_id IS NOT NULL AND event_id <> ''
    AND event_id <> ALL(v_client_event_ids)
  LOOP
    BEGIN
      DELETE FROM user_events
      WHERE id = v_db_event.id;

      -- Add to deleted events
      v_result := jsonb_set(
        v_result,
        '{deleted}',
        (v_result->'deleted') || jsonb_build_object(
          'id', v_db_event.id,
          'event_id', v_db_event.event_id
        )
      );
    EXCEPTION WHEN OTHERS THEN
      -- Add to errors
      GET STACKED DIAGNOSTICS v_error = PG_EXCEPTION_CONTEXT;
      v_result := jsonb_set(
        v_result,
        '{errors}',
        (v_result->'errors') || jsonb_build_object(
          'event_id', v_db_event.event_id,
          'message', SQLERRM,
          'detail', SQLSTATE,
          'context', v_error
        )
      );
    END;
  END LOOP;

  RETURN v_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions to the sync function
GRANT EXECUTE ON FUNCTION sync_user_events(TEXT, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION sync_user_events(TEXT, JSONB) TO service_role;

-- Add comments for documentation
COMMENT ON TABLE user_events IS 'Stores user itinerary events for the Vestigia application';
COMMENT ON FUNCTION sync_user_events IS 'Synchronizes user events between client and server';
