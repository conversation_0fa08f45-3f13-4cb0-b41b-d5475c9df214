// Netlify function for syncing user events between local storage and Supabase
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
// Using service role key to bypass RLS policies
const supabaseUrl = process.env.SUPABASE_URL || 'https://vwpyvjoycnrgmpatzqzk.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ3cHl2am95Y25yZ21wYXR6cXprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDExMzk0MTksImV4cCI6MjA1NjcxNTQxOX0.sG7amfnd8UTMv3zzmaa3KKyPRTzW70cDW9VVcUCSq4c';
const supabase = createClient(supabaseUrl, supabaseKey);

// CORS headers
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ message: 'Preflight request successful' })
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    console.log('sync-user-events function called');
    
    // Parse request body
    const requestBody = JSON.parse(event.body);
    const { user_id, events } = requestBody;
    
    console.log(`Received sync request for user ${user_id} with ${events?.length || 0} events`);
    if (events && events.length > 0) {
      console.log('First event sample:', JSON.stringify(events[0]));
    }

    // Validate required parameters
    if (!user_id || !events || !Array.isArray(events)) {
      console.error('Missing required parameters:', { user_id: !!user_id, events: !!events, isArray: Array.isArray(events) });
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing required parameters: user_id and events array' })
      };
    }

    // Get existing events for this user from Supabase
    const { data: existingEvents, error: fetchError } = await supabase
      .from('user_events')
      .select('*')
      .eq('user_id', user_id);

    if (fetchError) {
      console.error('Error fetching existing events:', fetchError);
      
      // Check if the error is because the table doesn't exist
      if (fetchError.code === '42P01') {
        console.log('Table user_events does not exist, creating it...');
        
        // Try to create the table directly with SQL
        const { error: sqlError } = await supabase.rpc('exec_sql', {
          sql_query: `
            CREATE TABLE IF NOT EXISTS user_events (
              id SERIAL PRIMARY KEY,
              user_id TEXT NOT NULL,
              event_id TEXT NOT NULL,
              title TEXT NOT NULL,
              start_time TIMESTAMP WITH TIME ZONE,
              end_time TIMESTAMP WITH TIME ZONE,
              description TEXT,
              location TEXT,
              category TEXT,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            
            CREATE INDEX IF NOT EXISTS idx_user_events_user_id ON user_events(user_id);
            CREATE INDEX IF NOT EXISTS idx_user_events_event_id ON user_events(event_id);
            
            ALTER TABLE user_events ENABLE ROW LEVEL SECURITY;
            
            CREATE POLICY select_own_events ON user_events
              FOR SELECT USING (auth.uid()::text = user_id);
            
            CREATE POLICY insert_own_events ON user_events
              FOR INSERT WITH CHECK (auth.uid()::text = user_id);
            
            CREATE POLICY update_own_events ON user_events
              FOR UPDATE USING (auth.uid()::text = user_id);
            
            CREATE POLICY delete_own_events ON user_events
              FOR DELETE USING (auth.uid()::text = user_id);
          `
        });
        
        if (sqlError) {
          console.error('Error creating SQL function:', sqlError);
          return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Failed to create user_events table', message: sqlError.message })
          };
        }
      } else {
        return {
          statusCode: 500,
          headers,
          body: JSON.stringify({ error: 'Failed to fetch existing events', message: fetchError.message })
        };
      }
    }

    // Create a map of existing events by event_id for quick lookup
    const existingEventsMap = {};
    if (existingEvents && existingEvents.length > 0) {
      console.log(`Found ${existingEvents.length} existing events for user ${user_id}`);
      existingEvents.forEach(event => {
        existingEventsMap[event.event_id] = event;
      });
    } else {
      console.log(`No existing events found for user ${user_id}`);
    }

    // Process each event from the client
    console.log(`Processing ${events.length} events from client`);
    const results = [];
    for (const event of events) {
      console.log(`Processing event: ${event.id} - ${event.title}`);
      
      // Ensure each event has the user_id
      event.user_id = user_id;

      // Convert event properties to database format
      const dbEvent = {
        user_id: event.user_id,
        event_id: event.id,
        title: event.title,
        start_time: event.start,
        end_time: event.end || null,
        description: event.description || null,
        location: event.location || null,
        category: event.category || null
      };
      
      console.log('Converted to DB format:', dbEvent);

      // Check if this event already exists
      if (existingEventsMap[event.id]) {
        console.log(`Event ${event.id} exists, updating...`);
        // Update existing event
        const { data, error } = await supabase
          .from('user_events')
          .update(dbEvent)
          .eq('user_id', user_id)
          .eq('event_id', event.id)
          .select();

        if (error) {
          console.error('Error updating event:', error);
          console.error('Error details:', JSON.stringify(error));
          results.push({ error: error.message, event });
        } else {
          console.log(`Event ${event.id} updated successfully`);
          results.push({ success: true, event: data[0], action: 'updated' });
        }
      } else {
        console.log(`Event ${event.id} is new, inserting...`);
        // Insert new event
        const { data, error } = await supabase
          .from('user_events')
          .insert([dbEvent])
          .select();

        if (error) {
          console.error('Error inserting event:', error);
          console.error('Error details:', JSON.stringify(error));
          results.push({ error: error.message, event });
        } else {
          console.log(`Event ${event.id} inserted successfully`);
          results.push({ success: true, event: data[0], action: 'inserted' });
        }
      }
    }

    // Check for events in the database that are not in the client's list
    // These should be deleted as they've been removed on the client
    if (existingEvents && existingEvents.length > 0) {
      const clientEventIds = new Set(events.map(e => e.id));
      const eventsToDelete = existingEvents.filter(e => !clientEventIds.has(e.event_id));

      if (eventsToDelete.length > 0) {
        for (const eventToDelete of eventsToDelete) {
          const { error } = await supabase
            .from('user_events')
            .delete()
            .eq('user_id', user_id)
            .eq('event_id', eventToDelete.event_id);

          if (error) {
            console.error('Error deleting event:', error);
            results.push({ error: error.message, event: eventToDelete, action: 'delete_failed' });
          } else {
            results.push({ success: true, event_id: eventToDelete.event_id, action: 'deleted' });
          }
        }
      }
    }

    // Log summary of operations
    const successCount = results.filter(r => r.success).length;
    const errorCount = results.filter(r => r.error).length;
    console.log(`Sync completed with ${successCount} successful operations and ${errorCount} errors`);
    
    // Return successful response with results
    const response = {
      statusCode: 200,
      headers,
      body: JSON.stringify({ success: true, message: 'Events synced successfully', results })
    };
    
    console.log('Returning response:', response.statusCode, JSON.stringify(response.body).substring(0, 200) + '...');
    return response;
  } catch (error) {
    console.error('Error in sync-user-events function:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Internal server error',
        message: error.message
      })
    };
  }
};
