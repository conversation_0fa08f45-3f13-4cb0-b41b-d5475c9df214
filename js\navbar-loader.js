/**
 * Navbar Loader
 * This script handles loading the navbar and setting the active link
 */

// Function to load navbar and set active links
function loadNavbar() {
  console.log('loadNavbar function called');

  // Check if navbar placeholder exists
  const navbarPlaceholder = document.getElementById('navbar-placeholder');
  if (!navbarPlaceholder) {
    console.warn('Navbar placeholder not found');
    return;
  }

  // Determine which navbar to load based on the current page
  const isAuthPage = window.location.pathname.includes('auth-') ||
    window.location.pathname.includes('login') ||
    window.location.pathname.includes('signup');
  const navbarPath = isAuthPage ? 'auth-navbar.html' : 'navbar.html';

  console.log('Loading navbar from:', navbarPath);

  // Load the appropriate navbar
  fetch(navbarPath)
    .then(response => {
      if (!response.ok) {
        throw new Error('Failed to load navbar');
      }
      return response.text();
    })
    .then(data => {
      // Insert navbar HTML
      navbarPlaceholder.innerHTML = data;
      console.log('Navbar HTML inserted');

      // Initialize auth if available
      if (typeof auth !== 'undefined') {
        auth.init();
      }

      // Wait a short moment for the DOM to update
      setTimeout(() => {
        // Set active navigation link
        setActiveNavbarLink();
      }, 100);
    })
    .catch(error => {
      console.error('Error loading navbar:', error);
    });
}

// Function to set active link in navbar
function setActiveNavbarLink() {
  // Get current page filename
  const currentPath = window.location.pathname;
  const currentPage = currentPath.split('/').pop() || 'index.html';
  // Remove any query parameters from the current page
  const currentPageWithoutParams = currentPage.split('?')[0];

  console.log('Setting active navbar link for page:', currentPageWithoutParams);

  // Select desktop navigation links specifically
  const desktopNavLinks = document.querySelectorAll('.nav-links .nav-link');
  const mobileLinks = document.querySelectorAll('.mobile-links .nav-link');

  console.log('Found desktop nav links:', desktopNavLinks.length);
  console.log('Found mobile nav links:', mobileLinks.length);

  // Function to set active state for a link
  const setActiveState = (link) => {
    const href = link.getAttribute('href');
    // Remove any query parameters from the href
    const hrefWithoutParams = href.split('?')[0];

    console.log('Checking link:', hrefWithoutParams, 'against current page:', currentPageWithoutParams);

    // Check if the current page matches the link's href
    if (hrefWithoutParams === currentPageWithoutParams ||
      (currentPageWithoutParams === '' && hrefWithoutParams === 'index.html') ||
      (currentPath === '/' && hrefWithoutParams === 'index.html')) {
      // Add active class to both the link and its parent nav-item
      link.classList.add('active');
      link.closest('.nav-item')?.classList.add('active');

      // For all menu icons, highlight the icon
      const icon = link.querySelector('i');
      if (icon) {
        icon.style.color = '#ffd700';
        icon.classList.add('active');
        console.log('Icon found and highlighted:', icon.className);
      } else {
        console.log('No icon found in link');
      }

      console.log('Active link found and highlighted:', hrefWithoutParams);
    } else {
      // Remove active class from both the link and its parent nav-item
      link.classList.remove('active');
      link.closest('.nav-item')?.classList.remove('active');

      // Reset icon color and remove active class
      const icon = link.querySelector('i');
      if (icon) {
        icon.style.color = '';
        icon.classList.remove('active');
      }
    }
  };

  // Set active state for desktop navigation
  desktopNavLinks.forEach(setActiveState);

  // Set active state for mobile navigation
  mobileLinks.forEach(setActiveState);
}

// Call loadNavbar when the DOM is ready
document.addEventListener('DOMContentLoaded', loadNavbar);
