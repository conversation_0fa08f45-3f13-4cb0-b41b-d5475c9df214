<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aviasales API Test - Vestigia</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #262626;
            color: #f5f5f5;
        }

        .test-container {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            color: #ffd700;
        }

        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            color: #f5f5f5;
        }

        button {
            background-color: #ffd700;
            color: #262626;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin-right: 10px;
        }

        button:hover {
            background-color: #e6c200;
        }

        .results {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }

        .loading {
            color: #ffd700;
        }

        .error {
            color: #ff6b6b;
        }

        .success {
            color: #4ade80;
        }
    </style>
</head>
<body>
    <h1>Aviasales API Test</h1>

    <div class="test-container">
        <h2>Flight Search Test</h2>
        <form id="testForm">
            <div class="form-group">
                <label for="origin">Origin (Airport Code):</label>
                <input type="text" id="origin" value="JFK" placeholder="e.g., JFK">
            </div>

            <div class="form-group">
                <label for="destination">Destination (Airport Code):</label>
                <input type="text" id="destination" value="LHR" placeholder="e.g., LHR">
            </div>

            <div class="form-group">
                <label for="departureDate">Departure Date:</label>
                <input type="date" id="departureDate" required>
            </div>

            <div class="form-group">
                <label for="returnDate">Return Date (optional):</label>
                <input type="date" id="returnDate">
            </div>

            <div class="form-group">
                <label for="adults">Adults:</label>
                <select id="adults">
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                </select>
            </div>

            <button type="submit">Test Flight Search</button>
            <button type="button" id="testAPI">Test API Connection</button>
            <button type="button" id="clearResults">Clear Results</button>
        </form>
    </div>

    <div id="results" class="results" style="display: none;"></div>

    <!-- Load the Travelpayouts client -->
    <script src="js/travelpayouts-client.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('testForm');
            const resultsDiv = document.getElementById('results');
            const clearBtn = document.getElementById('clearResults');

            // Set default departure date to tomorrow
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            document.getElementById('departureDate').value = tomorrow.toISOString().split('T')[0];

            // Set default return date to 7 days from departure
            const returnDate = new Date();
            returnDate.setDate(returnDate.getDate() + 8);
            document.getElementById('returnDate').value = returnDate.toISOString().split('T')[0];

            form.addEventListener('submit', async function(e) {
                e.preventDefault();

                const origin = document.getElementById('origin').value.trim();
                const destination = document.getElementById('destination').value.trim();
                const departureDate = document.getElementById('departureDate').value;
                const returnDate = document.getElementById('returnDate').value;
                const adults = document.getElementById('adults').value;

                if (!origin || !destination || !departureDate) {
                    showResults('Please fill in all required fields', 'error');
                    return;
                }

                const searchParams = {
                    origin: origin,
                    destination: destination,
                    departureDate: departureDate,
                    returnDate: returnDate || null,
                    adults: parseInt(adults),
                    children: 0,
                    infants: 0,
                    tripClass: 'Y',
                    locale: 'en'
                };

                showResults('Starting flight search...', 'loading');
                showResults(`Search Parameters:\n${JSON.stringify(searchParams, null, 2)}\n\n`, 'loading');

                try {
                    if (!window.travelpayoutsAPI) {
                        throw new Error('Travelpayouts API client not loaded');
                    }

                    const results = await window.travelpayoutsAPI.searchFlights(
                        searchParams,
                        function(status) {
                            showResults(`Progress: ${status}\n`, 'loading', true);
                        },
                        60000, // 60 second timeout
                        3000   // 3 second polling interval
                    );

                    showResults('\n=== SEARCH COMPLETED ===\n', 'success', true);
                    showResults(`Status: ${results.status}\n`, 'success', true);
                    showResults(`Search ID: ${results.search_id}\n`, 'success', true);
                    showResults(`Results Count: ${results.data ? results.data.length : 0}\n`, 'success', true);
                    showResults(`Currency: ${results.currency}\n`, 'success', true);

                    if (results.data && results.data.length > 0) {
                        showResults('\n=== FIRST FLIGHT RESULT ===\n', 'success', true);
                        showResults(JSON.stringify(results.data[0], null, 2), 'success', true);

                        showResults('\n=== AIRLINES INFO ===\n', 'success', true);
                        showResults(JSON.stringify(results.airlines, null, 2), 'success', true);

                        showResults('\n=== AIRPORTS INFO ===\n', 'success', true);
                        showResults(JSON.stringify(results.airports, null, 2), 'success', true);
                    } else {
                        showResults('\nNo flights found for this route and date.\n', 'error', true);
                    }

                } catch (error) {
                    showResults(`\n=== ERROR ===\n`, 'error', true);
                    showResults(`Error: ${error.message}\n`, 'error', true);
                    if (error.stack) {
                        showResults(`Stack: ${error.stack}\n`, 'error', true);
                    }
                }
            });

            clearBtn.addEventListener('click', function() {
                resultsDiv.style.display = 'none';
                resultsDiv.innerHTML = '';
            });

            // Test API button
            const testAPIBtn = document.getElementById('testAPI');
            testAPIBtn.addEventListener('click', async function() {
                showResults('Testing Travelpayouts API connection...', 'loading');

                try {
                    const response = await fetch('/.netlify/functions/test-travelpayouts', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    const data = await response.json();

                    showResults('\n=== API CONNECTION TEST ===\n', 'success', true);
                    showResults(`Status: ${response.status}\n`, response.ok ? 'success' : 'error', true);
                    showResults(JSON.stringify(data, null, 2), response.ok ? 'success' : 'error', true);

                } catch (error) {
                    showResults('\n=== API TEST ERROR ===\n', 'error', true);
                    showResults(`Error: ${error.message}\n`, 'error', true);
                }
            });

            function showResults(text, type = 'success', append = false) {
                resultsDiv.style.display = 'block';

                if (!append) {
                    resultsDiv.innerHTML = '';
                }

                const span = document.createElement('span');
                span.className = type;
                span.textContent = text;
                resultsDiv.appendChild(span);

                // Scroll to bottom
                resultsDiv.scrollTop = resultsDiv.scrollHeight;
            }
        });
    </script>
</body>
</html>
