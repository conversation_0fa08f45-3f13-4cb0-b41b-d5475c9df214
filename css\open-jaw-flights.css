/* Styles for Open Jaw flights */

/* Open Jaw segments container */
.open-jaw-segments {
    display: none;
    margin-top: 15px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
}

/* Show the segments container when Open Jaw is selected */
.open-jaw-active .open-jaw-segments {
    display: block;
}

/* Segment header */
.segment-header {
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
}

/* Segment row */
.segment-row {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #ddd;
}

.segment-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

/* Segment fields */
.segment-field {
    flex: 1;
    min-width: 200px;
}

.segment-field label {
    display: block;
    margin-bottom: 5px;
    font-size: 0.9em;
    color: #555;
}

.segment-field input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

/* Add segment button */
.add-segment-btn {
    display: inline-block;
    margin-top: 10px;
    padding: 8px 15px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
}

.add-segment-btn:hover {
    background-color: #45a049;
}

/* Remove segment button */
.remove-segment-btn {
    display: inline-block;
    margin-left: 10px;
    padding: 5px 10px;
    background-color: #f44336;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8em;
}

.remove-segment-btn:hover {
    background-color: #d32f2f;
}

/* Open Jaw flight card */
.flight-card.open-jaw {
    border-left: 3px solid #9C27B0;
}

/* Segment divider */
.segment-divider {
    display: flex;
    align-items: center;
    margin: 15px 0;
    color: #9C27B0;
    font-weight: bold;
    position: relative;
}

.segment-divider::before,
.segment-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background-color: #ddd;
}

.segment-divider span {
    padding: 0 10px;
    background-color: #fff;
    position: relative;
}

/* Segment section */
.flight-times.segment {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px dashed #eee;
}

/* Booking modal segment divider */
.booking-segment-divider {
    margin: 15px 0;
    padding: 5px 10px;
    background-color: #f5f5f5;
    border-left: 3px solid #9C27B0;
    font-weight: bold;
    color: #333;
}
