document.addEventListener('DOMContentLoaded', function () {
  // Hamburger Menu Toggle
  const hamburgerBtn = document.querySelector('.hamburger-btn');
  const mobileMenu = document.querySelector('.mobile-menu');
  const closeBtn = document.querySelector('.close-btn');

  if (hamburgerBtn && mobileMenu && closeBtn) {
    // Use click event which works better across all devices
    hamburgerBtn.addEventListener('click', (e) => {
      e.preventDefault(); // Prevent default behavior
      e.stopPropagation(); // Stop event from bubbling
      mobileMenu.classList.add('open');
      document.body.classList.add('overlay-open'); // Prevent background scrolling
    });

    // Close Button Functionality
    closeBtn.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      mobileMenu.classList.remove('open');
      document.body.classList.remove('overlay-open'); // Allow background scrolling
    });
  }

  // Close mobile menu when clicking outside
  document.addEventListener('click', (e) => {
    if (mobileMenu && mobileMenu.classList.contains('open') &&
      !mobileMenu.contains(e.target) &&
      e.target !== hamburgerBtn) {
      mobileMenu.classList.remove('open');
      document.body.classList.remove('overlay-open');
    }
  });
});

// Mobile Profile Dropdown Toggle
document.addEventListener('DOMContentLoaded', function () {
  const mobileProfileItem = document.querySelector('.mobile-profile-item');
  const mobileProfileBtn = mobileProfileItem ? mobileProfileItem.querySelector('.profile-btn') : null;

  if (mobileProfileBtn) {
    mobileProfileBtn.addEventListener('click', function (e) {
      e.preventDefault();
      e.stopPropagation();
      mobileProfileItem.classList.toggle('active');
    });
  }

  // Close mobile dropdown when clicking outside
  document.addEventListener('click', function (e) {
    if (mobileProfileItem && mobileProfileItem.classList.contains('active') &&
      !mobileProfileItem.contains(e.target)) {
      mobileProfileItem.classList.remove('active');
    }
  });
});

// Set active navigation link based on current page
function setActiveNavLink() {
  const currentPath = window.location.pathname;
  // Get the filename from the path
  const currentPage = currentPath.split('/').pop() || 'index.html';

  console.log('Setting active link for page:', currentPage);

  // Select all navigation links (both mobile and desktop)
  const navLinks = document.querySelectorAll('.nav-link');

  navLinks.forEach(link => {
    const href = link.getAttribute('href');
    // Check if the current page matches the link's href
    if (href === currentPage ||
      (currentPage === '' && href === 'index.html') ||
      (currentPath === '/' && href === 'index.html')) {
      link.classList.add('active');

      // For all menu icons, highlight the icon with !important to override any other styles
      if (link.querySelector('i')) {
        link.querySelector('i').style.cssText = 'color: #ffd700 !important';
      }

      console.log('Active link found and highlighted:', href);
    } else {
      link.classList.remove('active');
      // Reset icon color if not active
      if (link.querySelector('i')) {
        link.querySelector('i').style.cssText = '';
      }
    }
  });
}

// Run on page load
document.addEventListener('DOMContentLoaded', () => {
  // Add scroll effect to navbar
  const navbar = document.querySelector('.navbar');
  if (navbar) {
    window.addEventListener('scroll', () => {
      if (window.scrollY > 100) {
        navbar.style.backgroundColor = 'rgba(38, 38, 38, 0.95)';
        navbar.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.4)';
      } else {
        navbar.style.backgroundColor = 'rgba(38, 38, 38, 0.9)';
        navbar.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.3)';
      }
    });
  }
});

// Preload images for better performance
function preloadImages() {
  const imagesToPreload = [
    'images/vestigiaHomePicture.png',
    'images/vestigiaLogo.png',
    'images/calendarIcon.png',
    'images/mapIcon.png',
    'images/commIcon.png',
    'images/transportIcon.png',
    'images/reviewIcon.png',
    'images/cultureIcon.png'
  ];

  imagesToPreload.forEach(src => {
    const img = new Image();
    img.src = src;
  });
}

// Run preload on page load
preloadImages();