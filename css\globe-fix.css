/* Globe Container Fixes */

.globe-container {
    width: 100%;
    height: 350px;
    min-height: 350px;
    /* Ensure minimum height */
    background-color: #222;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 20px;
    border: 1px solid var(--main-color);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    position: relative;
    display: block !important;
    /* Ensure it's visible */
    z-index: 1;
    /* Ensure proper stacking context */
}

#globeViz {
    width: 100% !important;
    height: 100% !important;
    min-height: 350px !important;
    /* Ensure minimum height */
    position: relative !important;
    display: block !important;
    z-index: 2;
    /* Ensure proper stacking context */
}

/* Make sure the canvas is visible */
#globeViz canvas {
    display: block !important;
    width: 100% !important;
    height: 100% !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 3 !important;
    /* Ensure proper stacking context */
}

/* Fix for WebGL context */
canvas {
    outline: none !important;
}

/* Make sure the globe container is visible on all screen sizes */
@media (max-width: 768px) {
    .globe-container {
        height: 300px;
    }
}

@media (max-width: 480px) {
    .globe-container {
        height: 250px;
    }
}