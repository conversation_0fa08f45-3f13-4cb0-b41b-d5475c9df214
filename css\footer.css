/* Footer Styles */
#footer {
  background-color: #262626;
  color: var(--light-color);
  padding: 80px 0 20px;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 50px;
}

.footer-logo {
  flex: 1;
  min-width: 200px;
  margin-bottom: 30px;
}

.footer-logo img {
  height: 80px;
  margin-bottom: 15px;
}

.footer-logo p {
  color: #999;
  font-size: 0.9rem;
}

.footer-links {
  display: flex;
  flex-wrap: wrap;
  flex: 2;
  justify-content: space-around;
}

.footer-column {
  min-width: 150px;
  margin-bottom: 30px;
}

.footer-column h4 {
  color: var(--highlight-color);
  margin-bottom: 20px;
  font-size: 1.2rem;
}

.footer-column:last-child h4 {
  text-align: center;
}

.footer-column ul {
  list-style: none;
  padding: 0;
}

.footer-column ul li {
  margin-bottom: 10px;
}

.footer-column ul li a {
  color: #999;
  text-decoration: none;
  transition: all 0.3s ease;
}

.footer-column ul li a:hover {
  color: var(--highlight-color);
}

.social-icons {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.social-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--light-color);
  transition: all 0.3s ease;
  font-size: 18px;
  text-decoration: none;
}

.social-icon:hover {
  background-color: var(--highlight-color);
  color: var(--dark-color);
  transform: translateY(-3px);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
  color: #777;
  font-size: 0.9rem;
}

#back-to-top {
  color: var(--highlight-color);
  text-decoration: none;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

#back-to-top i {
  margin-left: 5px;
}

#back-to-top:hover {
  transform: translateY(-3px);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
  }

  .footer-links {
    flex-direction: column;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }

  #back-to-top {
    margin-top: 15px;
  }
}