-- SQL script to clean up old itinerary tables and functions
-- Run this AFTER setting up the new user_events table
-- This will remove the old complex sync functions and keep only the simple table

-- Drop old sync function if it exists
DROP FUNCTION IF EXISTS sync_user_events(TEXT, JSONB);

-- Drop old transportation reservations table if you don't need it
-- (Uncomment the line below if you want to remove transportation reservations)
-- DROP TABLE IF EXISTS transportation_reservations;

-- Verify the new user_events table exists and has the correct structure
SELECT 
  column_name, 
  data_type, 
  is_nullable, 
  column_default
FROM information_schema.columns 
WHERE table_name = 'user_events' 
ORDER BY ordinal_position;

-- Show current policies on user_events table
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE tablename = 'user_events';

-- Show current indexes on user_events table
SELECT 
  indexname,
  indexdef
FROM pg_indexes 
WHERE tablename = 'user_events';

SELECT 'Cleanup completed. New user_events table is ready.' AS status;
