/* Navbar */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: var(--dark-color);
  color: #fff;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000000;
  /* More reasonable z-index value */
  box-sizing: border-box;
  /* Ensure padding is included in the width */
  min-height: 60px;
  /* Set a minimum height for the navbar */
}

.navbar .logo img {
  height: 40px;
  margin: 0px;
  /* Remove top margin */
  margin-top: 0px;
}

.navbar .search-container {
  flex-grow: 1;
  /* Allow the search container to grow */
  display: flex;
  justify-content: center;
  /* Center the search bar */
  align-items: center;
  margin: 10px 20px;
  /* Add margin to separate from logo and icons */
}

.navbar .search-bar {
  width: 100%;
  /* Take full width of the container */
  max-width: 500px;
  /* Limit the maximum width */
  border-radius: 20px;
  padding: 15px 15px;
  font-size: 14px;
  background-color: #5a5a5a;
  color: white;
  transition: width 0.3s ease;
  height: 40px;
  /* Match the height of the login button */
}

.navbar .search-bar::placeholder {
  color: #fff;
}

.navbar .search-bar:focus {
  outline: none;
  background-color: #ffd700;
}

.nav-links {
  display: flex;
  list-style-type: none;
  align-items: center;
  /* Align items vertically */
}

.nav-item {
  position: relative;
  margin: 0 15px;
}

.nav-link {
  display: flex;
  align-items: center;
  color: #fff;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
  height: 40px;
  /* Set a consistent height for all navbar items */
}

.nav-link i {
  font-size: 20px;
  margin-right: 8px;
}

.nav-link.active {
  color: #ffd700;
  /* Highlight color for active link */
}

.nav-link.login-btn {
  background-color: var(--highlight-color);
  color: var(--dark-color);
  padding: 5px 25px;
  border-radius: 20px;
  font-weight: bold;
  transition: background-color 0.3s ease, color 0.3s ease;
  height: 40px;
  /* Set a consistent height for the login button */
  display: flex;
  align-items: center;
  /* Center align items vertically */
  justify-content: center;
  /* Center align items horizontally */
}

.nav-link.login-btn:hover {
  background-color: var(--main-color);
  /* Slightly darker yellow for hover effect */
  color: var(--main-color);
  /* Dark red text color on hover */
}

.tooltip {
  display: none;
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #ffd700;
  color: #1e1e1e;
  padding: 5px;
  font-size: 12px;
  border-radius: 4px;
  white-space: nowrap;
  z-index: 10;
}

.nav-item:hover .tooltip {
  display: block;
}

.nav-item:hover .nav-link {
  color: #ffd700;
}

.profile-btn:hover .dropdown-menu {
  display: block;
  top: 100%;
  /* Adjusted to be closer to the profile icon */
  left: 50%;
  /* Center align with the profile icon */
  transform: translateX(-50%);
  /* Center align with the profile icon */
}

.dropdown-menu {
  display: none;
  position: absolute;
  top: 110%;
  left: -37px;
  background-color: #1e1e1e;
  padding: 10px;
  border-radius: 5px;
  width: 70px;
  padding-left: 20px;
}

.dropdown-menu a {
  color: #fff;
  text-decoration: none;
  padding: 5px 0;
  display: block;
}

.dropdown-menu a:hover {
  color: #ffd700;
}

/* Mobile Hamburger Menu */
.hamburger-menu {
  display: none;
  position: relative;
}

.hamburger-btn {
  background: none;
  border: none;
  color: #fff;
  font-size: 30px;
  cursor: pointer;
}

.mobile-menu {
  position: fixed;
  /* Change to fixed to ensure it stays within the viewport */
  top: 0;
  left: 0;
  background-color: #1e1e1e;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  max-height: 80vh;
  overflow-y: auto;
  width: 100%;
  /* Ensure it takes the full width of the screen */
  text-align: center;
  /* Center align the text */
  display: flex;
  flex-direction: column;
  align-items: center;
  /* Center align the items */
  justify-content: center;
  /* Center align vertically */
}

.mobile-menu.open {
  transform: translateX(0);
}

.mobile-links {
  list-style-type: none;
  padding: 0;
  /* Remove padding */
  margin: 0;
  /* Remove margin */
  width: 100%;
  /* Ensure the links take the full width */
  display: flex;
  flex-direction: column;
  align-items: center;
  /* Center align the items */
}

.mobile-links li {
  margin: 20px 0;
  padding: 0;
  /* Remove padding */
  width: auto;
  /* Adjust width to auto */
}

.mobile-links a {
  color: #fff;
  text-decoration: none;
  font-size: 16px;
}

.mobile-links a:hover {
  color: #ffd700;
}

.close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  color: #fff;
  font-size: 30px;
  cursor: pointer;
}

/* Media Queries */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .hamburger-menu {
    display: block;
  }

  .navbar .logo {
    display: block;
  }

  .search-container {
    display: block;
  }

  .mobile-menu {
    display: none;
  }

  .mobile-menu.open {
    display: block;
  }

  .nav-item {
    margin: 0 10px;
  }

  .navbar .logo img {
    height: 30px;
  }

  .mobile-menu.open {
    transform: translateX(0);
  }

  .mobile-links {
    padding: 0;
  }

  .mobile-links li {
    margin: 15px 0;
  }
}

/* Show mobile menu when hamburger is clicked */
.hamburger-menu.open .mobile-menu {
  transform: translateX(0);
}

/* Hide mobile menu when clicking outside */
body.overlay-open {
  overflow: hidden;
}

body.overlay-open .mobile-menu {
  transform: translateX(0);
}

/* Search Bar */
.search-container {
  display: flex;
  align-items: center;
  margin: 0 15px;
  padding: 0;
}

.search-bar {
  border: none;
  border-radius: 20px;
  height: 40px;
  /* Match the height of the login button */
  font-size: 14px;
  width: 100%;
  max-width: 500px;
  transition: width 0.3s ease;
  background-color: #5a5a5a;
  color: white;
}

.search-bar::placeholder {
  color: #fff;
}

.search-bar:focus {
  outline: none;
  background-color: #ffd700;
  color: var(--main-color);
}

/* Navbar links */
.nav-links .search-container {
  display: flex;
  align-items: center;
  margin: 0 20px;
  padding: 0;
  /* Ensure no extra padding */
}

.nav-links .search-bar {
  width: 150px;
  /* Default width for desktop */
  border-radius: 20px;
  font-size: 14px;
}

/* Mobile menu styling */
.mobile-links .search-container {
  margin-top: 15px;
  margin-left: 20px;
  padding: 0;
  /* Remove extra padding in mobile menu */
}

.mobile-links .search-bar {
  width: 100%;
  border-radius: 20px;
  padding: 8px 15px;
  font-size: 14px;
}

/* Responsive for mobile */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .hamburger-menu {
    display: block;
  }

  .search-bar {
    width: 200px;
    /* Expand search bar for mobile */
  }

  .mobile-links .search-bar {
    width: 100%;
  }
}