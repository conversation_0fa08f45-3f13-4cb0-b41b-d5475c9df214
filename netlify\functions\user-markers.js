const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: false
  }
});

exports.handler = async function(event, context) {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
      },
      body: ''
    };
  }

  // Set CORS headers for all responses
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Content-Type': 'application/json'
  };

  try {
    // Get user ID from Authorization header
    const authHeader = event.headers.authorization || '';
    const token = authHeader.replace('Bearer ', '');
    
    if (!token) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Verify the token and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Invalid or expired token' })
      };
    }

    const userId = user.id;

    // Handle different HTTP methods
    switch (event.httpMethod) {
      case 'GET':
        // Get all markers for the user
        const { data: markers, error: fetchError } = await supabase
          .from('user_markers')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false });

        if (fetchError) throw fetchError;

        return {
          statusCode: 200,
          headers,
          body: JSON.stringify(markers || [])
        };

      case 'POST':
        // Add a new marker
        const markerData = JSON.parse(event.body);
        
        // Check for existing marker with same location and name
        const { data: existingMarkers, error: checkError } = await supabase
          .from('user_markers')
          .select('*')
          .eq('user_id', userId)
          .eq('lat', markerData.lat)
          .eq('lng', markerData.lng)
          .eq('name', markerData.name);

        if (checkError) throw checkError;

        if (existingMarkers && existingMarkers.length > 0) {
          return {
            statusCode: 200,
            headers,
            body: JSON.stringify(existingMarkers[0])
          };
        }

        
        const { data: newMarker, error: insertError } = await supabase
          .from('user_markers')
          .insert([{ ...markerData, user_id: userId }])
          .select()
          .single();


        if (insertError) throw insertError;

        return {
          statusCode: 201,
          headers,
          body: JSON.stringify(newMarker)
        };

      case 'DELETE':
        try {
          // Parse query parameters
          const queryParams = new URLSearchParams(event.rawQuery || '');
          const id = queryParams.get('id');
          
          if (!id) {
            console.error('Missing marker ID in DELETE request');
            return {
              statusCode: 400,
              headers,
              body: JSON.stringify({ error: 'Marker ID is required' })
            };
          }

          console.log(`Deleting marker ${id} for user ${userId}`);
          
          const { data, error: deleteError } = await supabase
            .from('user_markers')
            .delete()
            .eq('id', id)
            .eq('user_id', userId);

          if (deleteError) {
            console.error('Error deleting marker:', deleteError);
            throw deleteError;
          }

          console.log(`Successfully deleted marker ${id}`);
          return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ success: true })
          };
        } catch (error) {
          console.error('Error in DELETE handler:', error);
          return {
            statusCode: error.status || 500,
            headers,
            body: JSON.stringify({ 
              error: error.message || 'Failed to delete marker',
              details: process.env.NODE_ENV === 'development' ? error.stack : undefined
            })
          };
        }

      default:
        return {
          statusCode: 405,
          headers,
          body: JSON.stringify({ error: 'Method not allowed' })
        };
    }
  } catch (error) {
    console.error('Error in user-markers function:', {
      message: error.message,
      stack: error.stack,
      method: event.httpMethod,
      path: event.path,
      query: event.queryStringParameters,
      body: event.body
    });
    
    // Check for common Supabase errors
    let errorMessage = 'Internal server error';
    let statusCode = 500;
    
    if (error.message.includes('JWT')) {
      errorMessage = 'Invalid or expired authentication token';
      statusCode = 401;
    } else if (error.message.includes('permission denied')) {
      errorMessage = 'You do not have permission to perform this action';
      statusCode = 403;
    } else if (error.message.includes('connection')) {
      errorMessage = 'Unable to connect to the database';
      statusCode = 503;
    }
    
    return {
      statusCode,
      headers,
      body: JSON.stringify({ 
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    };
  }
};
