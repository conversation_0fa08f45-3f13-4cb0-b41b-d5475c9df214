<!DOCTYPE HTML>
<html>
<head>
    <title>Contact Vestigia</title>
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-6904623129097601"
    crossorigin="anonymous"></script>
</head>
<body style="font-family: 'Open Sans', sans-serif;">
  

  <!-- Main Content -->
  <section id="main">
    <!-- ...existing code... -->
  </section>

  <!-- Footer -->
  <footer id="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-logo">
          <img src="images/vestigiaLogoBig.png" alt="Vestigia Logo">
          <p>Discover iconic cultural landmarks with ease</p>
        </div>
        <div class="footer-links">
          <div class="footer-column">
            <h4>Navigation</h4>
            <ul>
              <li><a href="index.html">Home</a></li>
              <li><a href="map.html">Map</a></li>
              <li><a href="itinerary.html">Itinerary</a></li>
              <li><a href="transportation.html">Transportation</a></li>
              <li><a href="dev-updates.html">Development Updates</a></li>
            </ul>
          </div>
          <div class="footer-column">
            <h4>Connect</h4>
            <div class="social-icons">
              <a href="https://www.instagram.com/vestigiapp/" class="social-icon" target="_blank"><i class="fab fa-instagram"></i></a>
              <a href="https://www.linkedin.com/in/daniel-romanov-32bb05200/" class="social-icon" target="_blank"><i class="fab fa-linkedin-in"></i></a>
            </div>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2025 Vestigia | All rights reserved.</p>
        <a href="#" id="back-to-top">Back to Top <i class="fa fa-chevron-up"></i></a>
      </div>
    </div>
  </footer>

  <script src="js/main.js"></script>
  <script src="js/mobile-menu.js"></script>
  <!-- Include Supabase JS -->
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <!-- Include Authentication functionality -->
  <script src="js/auth.js"></script>

  <!-- Authentication initialization -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      console.log('Contact page DOM loaded, checking auth status...');
      // Initialize authentication
      if (typeof auth !== 'undefined') {
        // Auth.js will handle initialization on its own
        console.log('Auth module found on contact page');

        // Add event listener to logout buttons
        const logoutButtons = document.querySelectorAll('.logout-btn');
        logoutButtons.forEach(btn => {
          btn.addEventListener('click', function(e) {
            e.preventDefault();
            auth.logout();
            // Redirect to home page after logout
            window.location.href = 'index.html';
          });
        });
      } else {
        console.error('Auth module not found on contact page!');
      }
    });

    // Also check auth status immediately in case DOM is already loaded
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
      console.log('Contact page already loaded, checking auth status immediately...');
      if (typeof auth !== 'undefined') {
        console.log('Auth module found, ensuring UI is updated');
        setTimeout(() => {
          // Force UI update based on localStorage
          auth.init();
        }, 100);
      }
    }
  </script>
</body>
</html>
