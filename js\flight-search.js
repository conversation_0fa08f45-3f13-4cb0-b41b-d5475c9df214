/**
 * Flight Search Module
 *
 * This module handles the flight search functionality using the Travelpayouts API.
 * It manages the search form, displays results, and handles user interactions.
 */

// DOM Elements
const searchForm = document.getElementById('flightSearchForm');
const resultsContainer = document.getElementById('flightResults');
const progressContainer = document.getElementById('searchProgress');
const progressBar = document.getElementById('progressBar');
const progressMessage = document.getElementById('progressMessage');

// Search state
let searchInProgress = false;

// Initialize the module
function init() {
    if (searchForm) {
        searchForm.addEventListener('submit', handleSearch);
    }
}

/**
* Handle search form submission
* @param {Event} event - Form submit event
 */
async function handleSearch(event) {
    event.preventDefault();

    if (searchInProgress) {
        return;
    }

    try {
        searchInProgress = true;
        showProgress('Initializing search...', 0);

        // Get form data
        const formData = new FormData(searchForm);
        const searchParams = {
            segments: [{
                origin: formData.get('origin'),
                destination: formData.get('destination'),
                date: formData.get('departureDate')
            }],
            passengers: {
                adults: parseInt(formData.get('adults') || 1, 10),
                children: parseInt(formData.get('children') || 0, 10),
                infants: parseInt(formData.get('infants') || 0, 10)
            },
            trip_class: formData.get('tripClass') || 'Y',
            locale: 'en'
        };

        // Add return flight segment if return date is provided
        if (formData.get('returnDate')) {
            searchParams.segments.push({
                origin: formData.get('destination'),
                destination: formData.get('origin'),
                date: formData.get('returnDate')
            });
        }

        // Perform search
        const results = await window.travelpayoutsAPI.searchFlights(
            searchParams,
            handleProgress
        );

        // Display results
        displayResults(results);
    } catch (error) {
        console.error('Search error:', error);
        showError(error.message);
    } finally {
        searchInProgress = false;
        hideProgress();
    }
}

/**
* Handle search progress updates
* @param {Object} progress - Progress information
*/
function handleProgress(progress) {
    showProgress(progress.message, progress.progress);
}

/**
* Display search results
* @param {Object} results - Search results
*/
function displayResults(results) {
    if (!resultsContainer) return;

    // Clear previous results
    resultsContainer.innerHTML = '';

    if (!results.data || results.data.length === 0) {
        resultsContainer.innerHTML = '<div class="no-results">No flights found for this route and date.</div>';
        return;
    }

    // Create results container
    const resultsList = document.createElement('div');
    resultsList.className = 'flight-results-list';

    // Add each flight result
    results.data.forEach(flight => {
        const flightCard = createFlightCard(flight, results);
        resultsList.appendChild(flightCard);
    });

    resultsContainer.appendChild(resultsList);
}

/**
 * Create a flight card element
* @param {Object} flight - Flight data
* @param {Object} results - Full search results
* @returns {HTMLElement} Flight card element
 */
function createFlightCard(flight, results) {
    const card = document.createElement('div');
    card.className = 'flight-card';

    // Extract flight information
    const airline = results.airlines[flight.airline] || { name: 'Unknown Airline' };
    const price = formatPrice(flight.price, results.currency);
    const duration = formatDuration(flight.duration);
    const stops = flight.stops === 0 ? 'Direct' : `${flight.stops} stop${flight.stops > 1 ? 's' : ''}`;

    // Create card content
    card.innerHTML = `
        <div class="flight-header">
            <img src="${airline.logo}" alt="${airline.name}" class="airline-logo" onerror="this.src='images/airline-placeholder.png'">
            <span class="airline-name">${airline.name}</span>
            <span class="flight-price">${price}</span>
            </div>
            <div class="flight-details">
                <div class="flight-route">
                    <div class="departure">
                    <span class="time">${formatTime(flight.departure_time)}</span>
                    <span class="airport">${results.airports[flight.origin].name}</span>
                    </div>
                <div class="flight-info">
                    <span class="duration">${duration}</span>
                    <span class="stops">${stops}</span>
                    </div>
                    <div class="arrival">
                    <span class="time">${formatTime(flight.arrival_time)}</span>
                    <span class="airport">${results.airports[flight.destination].name}</span>
                </div>
            </div>
        </div>
        <div class="flight-footer">
            <a href="${flight.link}" target="_blank" class="book-button">Book Now</a>
            </div>
        `;

    return card;
}

/**
* Format price with currency
* @param {number} price - Price amount
* @param {string} currency - Currency code
* @returns {string} Formatted price
 */
function formatPrice(price, currency) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency || 'USD'
    }).format(price);
}

/**
 * Format duration in minutes to hours and minutes
 * @param {number} minutes - Duration in minutes
 * @returns {string} Formatted duration
 */
function formatDuration(minutes) {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
}

/**
* Format time string
* @param {string} time - Time string (HH:mm)
* @returns {string} Formatted time
*/
function formatTime(time) {
    return time;
}

/**
* Show progress indicator
* @param {string} message - Progress message
* @param {number} progress - Progress percentage (0-100)
 */
function showProgress(message, progress) {
    if (!progressContainer || !progressBar || !progressMessage) return;

    progressContainer.style.display = 'block';
    progressBar.style.width = `${progress}%`;
    progressMessage.textContent = message;
}

/**
* Hide progress indicator
 */
function hideProgress() {
    if (!progressContainer) return;
    progressContainer.style.display = 'none';
}

/**
 * Show error message
 * @param {string} message - Error message
 */
function showError(message) {
    if (!resultsContainer) return;
    resultsContainer.innerHTML = `<div class="error-message">${message}</div>`;
}

// Initialize the module when the DOM is ready
document.addEventListener('DOMContentLoaded', init);
