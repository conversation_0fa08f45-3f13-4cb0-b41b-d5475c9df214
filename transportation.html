<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-6904623129097601"
     crossorigin="anonymous"></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transportation Finder - Vestigia</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap">
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="96x96" href="favicon/favicon-96x96.png">
    <link rel="icon" href="favicon/favicon.ico">
    <link rel="manifest" href="favicon/site.webmanifest">
    <meta name="msapplication-TileColor" content="#262626">
    <meta name="theme-color" content="#262626">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/3.10.2/fullcalendar.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/3.10.2/fullcalendar.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <!-- Globe.GL for 3D globe visualization -->
    <script src="https://unpkg.com/globe.gl"></script>
    <link rel="stylesheet" href="css/navbar.css">
    <link rel="stylesheet" href="css/transportation.css">
    <link rel="stylesheet" href="css/modal-fixes.css">
    <link rel="stylesheet" href="css/date-picker-fix.css">
    <link rel="stylesheet" href="css/travel-cards.css">
    <link rel="stylesheet" href="css/globe-fix.css">
    <link rel="stylesheet" href="css/filter-styles.css">
    <link rel="stylesheet" href="css/booking-modal.css">
    <link rel="stylesheet" href="css/airport-suggestions.css">
    <link rel="stylesheet" href="css/reservation-cards.css">
    <link rel="stylesheet" href="css/server-status.css">
    <link rel="stylesheet" href="css/style-fixes.css">
    <link rel="stylesheet" href="css/flight-card-overflow-fix.css">
    <link rel="stylesheet" href="css/round-trip-flights.css">
    <link rel="stylesheet" href="css/open-jaw-flights.css">
    <link rel="stylesheet" href="css/baggage-info.css">
    <link rel="stylesheet" href="css/airline-logos.css">
    <link rel="stylesheet" href="css/flight-search-results.css">
    <link rel="stylesheet" href="css/footer.css">
    <!-- Travelpayouts API Client -->
    <script src="js/travelpayouts-api.js"></script>
    <script data-noptimize="1" data-cfasync="false" data-wpfc-render="false">
        (function () {
            var script = document.createElement("script");
            script.async = 1;
            script.src = 'https://emrld.cc/NDEyOTUz.js?t=412953';
            document.head.appendChild(script);
        })();
      </script>
</head>
<body>
    <div id="navbar-placeholder"></div>

    <!-- Main Container -->
    <div class="transport-layout">
        <!-- Left Column: Main Content -->
        <div class="transport-main">
            <!-- Header -->
            <div class="transport-header">
                <h2>Transportation Finder</h2>
                <p>Find flights to your destination</p>
            </div>

            <!-- Flight Search Results -->
            <div id="flightSearchContainer" class="flight-search-container">
                <!-- Loading Indicator -->
                <div id="loadingIndicator" class="loading-indicator" style="display: none;">
                    <div class="spinner"></div>
                    <p>Searching for flights...</p>
                </div>

                <!-- Error Container -->
                <div id="errorContainer" class="error-container" style="display: none;">
                    <i class="fas fa-exclamation-circle"></i>
                    <span class="error-message"></span>
                </div>

                <!-- Flight Results -->
                <div id="flightResults" class="flight-results">
                    <!-- Flight cards will be inserted here by JavaScript -->
                </div>

                <!-- No Results Message (initially hidden) -->
                <div id="noResults" class="no-results" style="display: none;">
                    <i class="fas fa-search"></i>
                    <h3>No flights found</h3>
                    <p>Try adjusting your search criteria or dates</p>
                </div>
            </div>

            <!-- Search Form -->
            <div class="search-box">
                <!-- Transport Mode Tabs -->
                <div class="transport-tabs">
                    <button class="tab-btn active" data-mode="flight">
                        <i class="fas fa-plane"></i> Flights
                    </button>
                    <!-- Commented out train button
                    <button class="tab-btn" data-mode="train">
                        <i class="fas fa-train"></i> Trains
                    </button>
                    -->
                    <!-- Commented out bus button
                    <button class="tab-btn" data-mode="bus">
                        <i class="fas fa-bus"></i> Buses
                    </button>
                    -->
                </div>

                <form id="transportForm">
                    <!-- Row 1 -->
                    <div class="form-row">
                        <!-- Trip Type -->
                        <div class="form-group trip-type">
                            <div class="trip-option">
                                <input type="radio" id="one-way" name="tripType" value="one-way">
                                <label for="one-way">One way</label>
                            </div>
                            <div class="trip-option">
                                <input type="radio" id="round-trip" name="tripType" value="round-trip" checked>
                                <label for="round-trip">Round trip</label>
                            </div>
                        </div>

                        <!-- Origin & Destination -->
                        <div class="form-group locations">
                            <div class="location-field">
                                <label for="departureInput">From</label>
                                <input type="text" id="departureInput" name="departure" placeholder="Enter departure city" autocomplete="off" required>
                                <div class="suggestions" id="departureSuggestions"></div>
                            </div>
                            <div class="switch-btn">
                                <button type="button" id="switchButton">
                                    <i class="fas fa-exchange-alt"></i>
                                </button>
                            </div>
                            <div class="location-field">
                                <label for="destinationInput">To</label>
                                <input type="text" id="destinationInput" name="destination" placeholder="Enter destination city" autocomplete="off" required>
                                <div class="suggestions" id="destinationSuggestions"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Row 2 -->
                    <div class="form-row">
                        <!-- Dates -->
                        <div class="form-group dates">
                            <div class="date-field">
                                <label for="departDate">Departure</label>
                                <input type="text" id="departDate" name="departDate" class="date-picker" placeholder="Select date" required>
                            </div>
                            <div class="date-field" id="returnDateContainer">
                                <label for="returnDate">Return</label>
                                <input type="text" id="returnDate" name="returnDate" class="date-picker" placeholder="Select date">
                            </div>
                        </div>

                        <!-- Passengers -->
                        <div class="form-group">
                            <label for="passengerCount">Passengers</label>
                            <div class="passenger-selector">
                                <span id="passengerDisplay">1 passenger</span>
                                <i class="fas fa-chevron-down"></i>
                                <div class="passenger-dropdown" style="display: none;">
                                    <div class="passenger-type">
                                        <span>Adults</span>
                                        <div class="counter">
                                            <button type="button" class="counter-btn minus" data-type="adults">-</button>
                                            <span class="count" id="adultsCount">1</span>
                                            <button type="button" class="counter-btn plus" data-type="adults">+</button>
                                        </div>
                                    </div>
                                    <div class="passenger-type">
                                        <span>Children</span>
                                        <div class="counter">
                                            <button type="button" class="counter-btn minus" data-type="children">-</button>
                                            <span class="count" id="childrenCount">0</span>
                                            <button type="button" class="counter-btn plus" data-type="children">+</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" id="passengerInput" name="passengers" value="1">
                        </div>

                        <!-- Search Button -->
                        <div class="form-group">
                            <button type="submit" class="search-btn">Search</button>
                        </div>
                    </div>



                    <!-- Hidden options -->
                    <!-- Commented out train options
                    <div class="mode-options" id="train-options" style="display: none;">
                        <div class="form-group">
                            <label for="railpass">Rail Pass</label>
                            <select id="railpass" name="railpass">
                                <option value="none">No Rail Pass</option>
                                <option value="eurail">Eurail Pass</option>
                                <option value="japan">Japan Rail Pass</option>
                            </select>
                        </div>
                    </div>
                    -->
                </form>
            </div>

            <!-- Globe Visualization -->
            <div class="globe-container">
                <div id="globeViz"></div>
            </div>

            <!-- Results Section -->
            <div class="results-container" style="display: none;">
                <div class="results-header">
                    <div class="route-summary">
                        <span id="routeDisplay">New York → London</span>
                        <span id="dateDisplay">Wed, Oct 12</span>
                    </div>

                    <div class="view-options">

                    </div>
                </div>

                <div class="results-body">
                    <div class="filters-sidebar">
                        <div class="filter-section">
                            <h5>Stops</h5>
                            <div class="filter-options">
                                <label class="filter-option">
                                    <input type="checkbox" name="stops" value="nonstop" checked>
                                    <span>Nonstop</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="stops" value="1-stop" checked>
                                    <span>1 stop</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="stops" value="2plus-stops" checked>
                                    <span>2+ stops</span>
                                </label>
                            </div>
                        </div>

                        <div class="filter-section">
                            <h5>Price</h5>
                            <div class="price-slider">
                                <input type="range" id="priceFilter" min="0" max="1000" step="10" value="1000">
                                <div class="price-range">
                                    <span>$0</span>
                                    <span id="priceValue">$1000</span>
                                </div>
                            </div>
                        </div>

                        <div class="filter-section">
                            <h5>Times</h5>
                            <div class="time-filters">
                                <div class="time-filter-group">
                                    <h6>Departure</h6>
                                    <div class="time-slider" id="departureTimeSlider"></div>
                                </div>
                                <div class="time-filter-group">
                                    <h6>Arrival</h6>
                                    <div class="time-slider" id="arrivalTimeSlider"></div>
                                </div>
                            </div>
                        </div>


                    </div>

                    <div class="results-main">
                        <div class="sort-options">
                            <span>Sort by:</span>
                            <select id="sortBy">
                                <option value="best">Best</option>
                                <option value="price">Price</option>
                                <option value="duration">Duration</option>
                                <option value="departure">Departure time</option>
                                <option value="arrival">Arrival time</option>
                            </select>
                        </div>

                        <div id="searchResults" class="search-results">
                            <!-- Results will be populated here -->
                            <div class="loading">
                                <div class="loading-spinner"></div>
                                <p>Enter your travel details and search for transportation options</p>
                            </div>
                        </div>

                        <div id="mapView" class="map-view" style="display: none;">
                            <!-- Map will be displayed here -->
                            <div class="map-placeholder">Map view will be displayed here</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column: Reservations -->
        <div class="reservations-column">
            <div class="reservations-box">
                <h3>My Reservations</h3>
                <div class="add-reservation-btn-container">
                    <button id="addExternalReservation" class="add-reservation-btn">
                        <i class="fas fa-plus"></i> Add External Booking
                    </button>
                </div>
                <div class="reserved-list" id="reservedList">
                    <div class="empty-reservations">
                        <i class="fas fa-ticket-alt"></i>
                        <p>No reservations yet</p>
                        <p class="hint">Your booked transportation will appear here</p>
                    </div>
                </div>
            </div>

            <!-- External Reservation Modal -->
            <div id="externalReservationModal" class="modal" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4>Add External Booking</h4>
                        <span class="close-modal">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="externalReservationForm">
                            <!-- Hidden field for reservation ID when editing -->
                            <input type="hidden" id="externalReservationId" name="reservationId" value="">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="bookingType">Transportation Type</label>
                                    <select id="bookingType" name="bookingType" required>
                                        <option value="">Select type</option>
                                        <option value="flight">Flight</option>
                                        <option value="train">Train</option>
                                        <option value="bus">Bus</option>
                                    </select>
                                </div>
                                <!-- Trip type removed as reservations are logically one-way -->
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="externalFrom">From</label>
                                    <input type="text" id="externalFrom" name="from" placeholder="Departure city/airport" autocomplete="off" required>
                                    <div class="suggestions" id="externalFromSuggestions"></div>
                                </div>
                                <div class="form-group">
                                    <label for="externalTo">To</label>
                                    <input type="text" id="externalTo" name="to" placeholder="Destination city/airport" autocomplete="off" required>
                                    <div class="suggestions" id="externalToSuggestions"></div>
                                </div>
                            </div>

                            <div class="form-row date-time-row">
                                <div class="form-group">
                                    <label for="externalDate">Departure Date</label>
                                    <input type="text" id="externalDate" name="date" class="date-picker" placeholder="Departure date" required>
                                </div>
                                <!-- Return date removed as reservations are logically one-way -->
                                <div class="form-group">
                                    <label for="externalTime">Departure Time</label>
                                    <input type="time" id="externalTime" name="time" required>
                                    <small class="input-hint">Departure time in 24-hour format</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="externalCarrier">Carrier/Company</label>
                                    <input type="text" id="externalCarrier" name="carrier" placeholder="Airline/Train/Bus company">
                                </div>
                                <div class="form-group">
                                    <label for="externalReference">Booking Reference</label>
                                    <input type="text" id="externalReference" name="reference" placeholder="Confirmation code">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="externalNotes">Notes</label>
                                <textarea id="externalNotes" name="notes" placeholder="Additional information"></textarea>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="cancel-modal-btn">Cancel</button>
                                <button type="submit" class="save-btn">Save Booking</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Booking Confirmation Modal -->
    <div id="bookingConfirmationModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h4>Booking Confirmation</h4>
                <span class="close-modal" id="closeBookingModal">&times;</span>
            </div>
            <div class="modal-body">
                <div id="bookingDetails" class="booking-details">
                    <!-- Flight details will be populated here -->
                </div>
                <p>Did you complete your booking on the airline's website?</p>
                <div class="booking-actions">
                    <button id="confirmBookingBtn" class="confirm-btn">Yes, I Booked</button>
                    <button id="cancelBookingBtn" class="cancel-btn">No, Not Yet</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Globe.GL already included in the head -->
    <!-- Include Supabase JS -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Core API Client -->
    <script src="js/api-client.js"></script>
    
    <!-- Supabase Client -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="js/supabase-client.js"></script>
    
    <!-- Auth Module -->
    <script src="js/auth.js"></script>
    
    <!-- Navbar Loader -->
    <script src="js/navbar-loader.js"></script>
    
    <!-- Page Initialization -->
    <script src="js/transportation-init.js"></script>
    
    <!-- Mobile Menu Functionality -->
    <script src="js/mobile-menu.js"></script>

    <!-- Itinerary API -->
    <script src="js/itinerary-api-client.js"></script>
    
    <!-- Transportation Reservations Management -->
    <script src="js/transportation-reservations.js"></script>
    
    <!-- Date Picker Initialization -->
    <script src="js/date-picker-init.js"></script>
    
    <!-- Airport Search Functionality -->
    <script src="js/airport-search.js"></script>
    
    <!-- Transportation Page Functionality -->
    <script src="js/transportation.js"></script>
    
    <!-- Flight Search Functionality -->
    <script src="js/flight-search.js"></script>

    <!-- Footer -->
    <div id="footer-placeholder"></div>

    <script>
      // Wait for all required modules to be loaded
      function waitForModules() {
        return new Promise((resolve) => {
          const checkModules = () => {
            if (window.supabase && window.auth) {
              console.log('All required modules loaded successfully');
              resolve();
            } else {
              console.log('Waiting for modules... supabase:', !!window.supabase, 'auth:', !!window.auth);
              setTimeout(checkModules, 100);
            }
          };
          checkModules();
        });
      }

      // Initialize the page
      async function initializePage() {
        try {
          // Wait for modules to be loaded
          await waitForModules();
          console.log('Initializing page...');

          // Load navbar and footer
          const navbarResponse = await fetch('navbar.html');
          const navbarData = await navbarResponse.text();
          document.getElementById('navbar-placeholder').innerHTML = navbarData;

          // Initialize auth after navbar is loaded
          if (typeof auth !== 'undefined') {
            console.log('Initializing auth after navbar is loaded');
            await auth.init();
            // Force a second initialization after a short delay to ensure UI is updated
            setTimeout(() => {
              if (typeof auth !== 'undefined') {
                console.log('Forcing auth UI update after navbar load');
                auth.updateUI(auth.isLoggedIn());
              }
            }, 200);
          }

          const footerResponse = await fetch('footer-template.html');
          const footerData = await footerResponse.text();
          document.getElementById('footer-placeholder').innerHTML = footerData;

          // Initialize back to top button
          const backToTopButton = document.getElementById('back-to-top');
          if (backToTopButton) {
            backToTopButton.addEventListener('click', (e) => {
              e.preventDefault();
              window.scrollTo({
                top: 0,
                behavior: 'smooth'
              });
            });

            // Show/hide back to top button based on scroll position
            window.addEventListener('scroll', () => {
              if (window.scrollY > 300) {
                backToTopButton.style.opacity = '1';
              } else {
                backToTopButton.style.opacity = '0';
              }
            });
          }
        } catch (error) {
          console.error('Error initializing page:', error);
        }
      }

      // Start initialization when DOM is loaded
      document.addEventListener('DOMContentLoaded', initializePage);

      // Also initialize if DOM is already loaded
      if (document.readyState === 'complete' || document.readyState === 'interactive') {
        initializePage();
      }
    </script>

    <!-- Inline globe script (based on working globe-test.html) -->
    <script>
        // Initialize the globe when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing inline globe');

            // Create the globe
            const globeElement = document.getElementById('globeViz');
            const globeContainer = document.querySelector('.globe-container');

            if (!globeElement || !globeContainer) {
                console.error('Globe elements not found');
                return;
            }

            // City coordinates database
            const cityCoordinates = {
                // North America
                'New York': { lat: 40.7128, lng: -74.0060 },
                'Los Angeles': { lat: 34.0522, lng: -118.2437 },
                'Chicago': { lat: 41.8781, lng: -87.6298 },
                'Miami': { lat: 25.7617, lng: -80.1918 },
                'San Francisco': { lat: 37.7749, lng: -122.4194 },
                'Las Vegas': { lat: 36.1699, lng: -115.1398 },
                'Orlando': { lat: 28.5383, lng: -81.3792 },
                'Dallas': { lat: 32.7767, lng: -96.7970 },
                'Boston': { lat: 42.3601, lng: -71.0589 },
                'Seattle': { lat: 47.6062, lng: -122.3321 },
                'Denver': { lat: 39.7392, lng: -104.9903 },
                'Atlanta': { lat: 33.7490, lng: -84.3880 },
                'Phoenix': { lat: 33.4484, lng: -112.0740 },
                'Philadelphia': { lat: 39.9526, lng: -75.1652 },
                'Houston': { lat: 29.7604, lng: -95.3698 },
                'San Diego': { lat: 32.7157, lng: -117.1611 },
                'Washington': { lat: 38.9072, lng: -77.0369 },
                'Portland': { lat: 45.5051, lng: -122.6750 },
                'Minneapolis': { lat: 44.9778, lng: -93.2650 },
                'New Orleans': { lat: 29.9511, lng: -90.0715 },
                'Jacksonville': { lat: 30.3322, lng: -81.6557 },
                'Charlotte': { lat: 35.2271, lng: -80.8431 },
                'Detroit': { lat: 42.3314, lng: -83.0458 },
                'Tampa': { lat: 27.9506, lng: -82.4572 },
                'Austin': { lat: 30.2672, lng: -97.7431 },
                'Toronto': { lat: 43.6532, lng: -79.3832 },
                'Vancouver': { lat: 49.2827, lng: -123.1207 },
                'Montreal': { lat: 45.5017, lng: -73.5673 },
                'Mexico City': { lat: 19.4326, lng: -99.1332 },

                // Europe
                'London': { lat: 51.5074, lng: -0.1278 },
                'Paris': { lat: 48.8566, lng: 2.3522 },
                'Rome': { lat: 41.9028, lng: 12.4964 },
                'Berlin': { lat: 52.5200, lng: 13.4050 },
                'Madrid': { lat: 40.4168, lng: -3.7038 },
                'Amsterdam': { lat: 52.3676, lng: 4.9041 },
                'Barcelona': { lat: 41.3851, lng: 2.1734 },
                'Munich': { lat: 48.1351, lng: 11.5820 },
                'Milan': { lat: 45.4642, lng: 9.1900 },
                'Vienna': { lat: 48.2082, lng: 16.3738 },
                'Athens': { lat: 37.9838, lng: 23.7275 },
                'Dublin': { lat: 53.3498, lng: -6.2603 },
                'Zurich': { lat: 47.3769, lng: 8.5417 },
                'Brussels': { lat: 50.8503, lng: 4.3517 },
                'Lisbon': { lat: 38.7223, lng: -9.1393 },
                'Copenhagen': { lat: 55.6761, lng: 12.5683 },
                'Prague': { lat: 50.0755, lng: 14.4378 },
                'Stockholm': { lat: 59.3293, lng: 18.0686 },
                'Budapest': { lat: 47.4979, lng: 19.0402 },
                'Istanbul': { lat: 41.0082, lng: 28.9784 },
                'Moscow': { lat: 55.7558, lng: 37.6173 },

                // Asia & Pacific
                'Tokyo': { lat: 35.6762, lng: 139.6503 },
                'Beijing': { lat: 39.9042, lng: 116.4074 },
                'Shanghai': { lat: 31.2304, lng: 121.4737 },
                'Hong Kong': { lat: 22.3193, lng: 114.1694 },
                'Singapore': { lat: 1.3521, lng: 103.8198 },
                'Seoul': { lat: 37.5665, lng: 126.9780 },
                'Bangkok': { lat: 13.7563, lng: 100.5018 },
                'Dubai': { lat: 25.2048, lng: 55.2708 },
                'Sydney': { lat: -33.8688, lng: 151.2093 },
                'Melbourne': { lat: -37.8136, lng: 144.9631 },
                'Mumbai': { lat: 19.0760, lng: 72.8777 },
                'Delhi': { lat: 28.6139, lng: 77.2090 },
                'Kuala Lumpur': { lat: 3.1390, lng: 101.6869 },
                'Jakarta': { lat: -6.2088, lng: 106.8456 },
                'Manila': { lat: 14.5995, lng: 120.9842 },
                'Taipei': { lat: 25.0330, lng: 121.5654 },
                'Auckland': { lat: -36.8485, lng: 174.7633 },
                'Ho Chi Minh City': { lat: 10.8231, lng: 106.6297 },

                // Africa & Middle East
                'Cairo': { lat: 30.0444, lng: 31.2357 },
                'Johannesburg': { lat: -26.2041, lng: 28.0473 },
                'Cape Town': { lat: -33.9249, lng: 18.4241 },
                'Nairobi': { lat: -1.2921, lng: 36.8219 },
                'Casablanca': { lat: 33.5731, lng: -7.5898 },
                'Tel Aviv': { lat: 32.0853, lng: 34.7818 },
                'Doha': { lat: 25.2854, lng: 51.5310 },
                'Abu Dhabi': { lat: 24.4539, lng: 54.3773 },
                'Riyadh': { lat: 24.7136, lng: 46.6753 }
            };

            // Airport code to city mapping
            const airportCodes = {
                // North America
                'JFK': 'New York',
                'LGA': 'New York',
                'EWR': 'New York',
                'LAX': 'Los Angeles',
                'SFO': 'San Francisco',
                'ORD': 'Chicago',
                'MIA': 'Miami',
                'DFW': 'Dallas',
                'BOS': 'Boston',
                'SEA': 'Seattle',
                'DEN': 'Denver',
                'ATL': 'Atlanta',
                'LAS': 'Las Vegas',
                'MCO': 'Orlando',
                'PHX': 'Phoenix',
                'PHL': 'Philadelphia',
                'IAH': 'Houston',
                'SAN': 'San Diego',
                'DCA': 'Washington',
                'IAD': 'Washington',
                'PDX': 'Portland',
                'MSP': 'Minneapolis',
                'MSY': 'New Orleans',
                'JAX': 'Jacksonville',
                'CLT': 'Charlotte',
                'DTW': 'Detroit',
                'TPA': 'Tampa',
                'AUS': 'Austin',
                'YYZ': 'Toronto',
                'YVR': 'Vancouver',
                'YUL': 'Montreal',
                'MEX': 'Mexico City',

                // Europe
                'LHR': 'London',
                'LGW': 'London',
                'CDG': 'Paris',
                'ORY': 'Paris',
                'FCO': 'Rome',
                'TXL': 'Berlin',
                'BER': 'Berlin',
                'MAD': 'Madrid',
                'AMS': 'Amsterdam',
                'BCN': 'Barcelona',
                'MUC': 'Munich',
                'MXP': 'Milan',
                'VIE': 'Vienna',
                'ATH': 'Athens',
                'DUB': 'Dublin',
                'ZRH': 'Zurich',
                'BRU': 'Brussels',
                'LIS': 'Lisbon',
                'CPH': 'Copenhagen',
                'PRG': 'Prague',
                'ARN': 'Stockholm',
                'BUD': 'Budapest',
                'IST': 'Istanbul',
                'SVO': 'Moscow',

                // Asia & Pacific
                'HND': 'Tokyo',
                'NRT': 'Tokyo',
                'PEK': 'Beijing',
                'PVG': 'Shanghai',
                'HKG': 'Hong Kong',
                'SIN': 'Singapore',
                'ICN': 'Seoul',
                'BKK': 'Bangkok',
                'DXB': 'Dubai',
                'SYD': 'Sydney',
                'MEL': 'Melbourne',
                'BOM': 'Mumbai',
                'DEL': 'Delhi',
                'KUL': 'Kuala Lumpur',
                'CGK': 'Jakarta',
                'MNL': 'Manila',
                'TPE': 'Taipei',
                'AKL': 'Auckland',
                'SGN': 'Ho Chi Minh City',

                // Africa & Middle East
                'CAI': 'Cairo',
                'JNB': 'Johannesburg',
                'CPT': 'Cape Town',
                'NBO': 'Nairobi',
                'CMN': 'Casablanca',
                'TLV': 'Tel Aviv',
                'DOH': 'Doha',
                'AUH': 'Abu Dhabi',
                'RUH': 'Riyadh'
            };

            try {
                const globe = Globe()
                    .globeImageUrl('https://unpkg.com/three-globe/example/img/earth-night.jpg')
                    .backgroundColor('#222222')
                    .width(globeContainer.clientWidth)
                    .height(globeContainer.clientHeight)
                    (globeElement);

                console.log('Inline globe created successfully');

                // Add initial animation
                globe.controls().autoRotate = true;
                globe.controls().autoRotateSpeed = 0.5;

                // Make the globe available globally
                window.globe = globe;

                // Helper function to extract city name from input
                function extractCityName(input) {
                    if (!input) return null;

                    // Check if it's an airport code
                    const code = input.match(/\b([A-Z]{3})\b/);
                    if (code && airportCodes[code[1]]) {
                        return airportCodes[code[1]];
                    }

                    // Try to extract city name from format like "New York (JFK)"
                    const cityMatch = input.match(/^([^(]+)/);
                    if (cityMatch) {
                        const cityName = cityMatch[1].trim();
                        // Check if this city is in our database
                        for (const city in cityCoordinates) {
                            if (cityName.toLowerCase().includes(city.toLowerCase()) ||
                                city.toLowerCase().includes(cityName.toLowerCase())) {
                                return city;
                            }
                        }
                    }

                    // If all else fails, return the input as is
                    return input;
                }

                // Provide a function to visualize routes
                window.visualizeRoute = function(origin, destination) {
                    if (!origin || !destination) {
                        console.log('Origin or destination is missing');
                        return;
                    }

                    console.log(`Visualizing route from ${origin} to ${destination}`);

                    // Extract city names
                    const originCity = extractCityName(origin);
                    const destCity = extractCityName(destination);

                    console.log(`Extracted cities: ${originCity} to ${destCity}`);

                    // Get coordinates
                    let originCoords = null;
                    let destCoords = null;

                    // Try to get coordinates for origin
                    if (cityCoordinates[originCity]) {
                        originCoords = cityCoordinates[originCity];
                        console.log(`Found coordinates for origin ${originCity}:`, originCoords);
                    } else {
                        // Try to find a partial match
                        for (const city in cityCoordinates) {
                            if (originCity.toLowerCase().includes(city.toLowerCase()) ||
                                city.toLowerCase().includes(originCity.toLowerCase())) {
                                originCoords = cityCoordinates[city];
                                console.log(`Found partial match for origin: ${city}`, originCoords);
                                break;
                            }
                        }

                        // If still not found, use default
                        if (!originCoords) {
                            originCoords = { lat: 40.7128, lng: -74.0060 }; // Default to New York
                            console.log(`Using default coordinates for origin ${originCity}:`, originCoords);
                        }
                    }

                    // Try to get coordinates for destination
                    if (cityCoordinates[destCity]) {
                        destCoords = cityCoordinates[destCity];
                        console.log(`Found coordinates for destination ${destCity}:`, destCoords);
                    } else {
                        // Try to find a partial match
                        for (const city in cityCoordinates) {
                            if (destCity.toLowerCase().includes(city.toLowerCase()) ||
                                city.toLowerCase().includes(destCity.toLowerCase())) {
                                destCoords = cityCoordinates[city];
                                console.log(`Found partial match for destination: ${city}`, destCoords);
                                break;
                            }
                        }

                        // If still not found, use default
                        if (!destCoords) {
                            destCoords = { lat: 51.5074, lng: -0.1278 }; // Default to London
                            console.log(`Using default coordinates for destination ${destCity}:`, destCoords);
                        }
                    }

                    // Ensure we have different coordinates for origin and destination
                    if (originCoords.lat === destCoords.lat && originCoords.lng === destCoords.lng) {
                        console.log('Origin and destination have the same coordinates, adjusting...');
                        // Slightly adjust destination coordinates to ensure we see a route
                        destCoords = {
                            lat: destCoords.lat + 5,
                            lng: destCoords.lng + 5
                        };
                    }

                    console.log('Final coordinates:', originCoords, destCoords);

                    // Stop auto-rotation
                    globe.controls().autoRotate = false;

                    // Clear existing data
                    globe.arcsData([]);
                    globe.pointsData([]);

                    // Create arc data
                    const arcData = [{
                        startLat: originCoords.lat,
                        startLng: originCoords.lng,
                        endLat: destCoords.lat,
                        endLng: destCoords.lng,
                        color: '#2e7d32' // Green
                    }];

                    // Add arc
                    globe.arcsData(arcData)
                        .arcColor('color')
                        .arcStroke(0.5)
                        .arcDashLength(0.4)
                        .arcDashGap(0.2)
                        .arcDashAnimateTime(1500)
                        .arcAltitude(0.3)
                        .arcsTransitionDuration(1000);

                    // Add points
                    const pointsData = [
                        { lat: originCoords.lat, lng: originCoords.lng, size: 0.8, color: '#ffffff', label: originCity },
                        { lat: destCoords.lat, lng: destCoords.lng, size: 0.8, color: '#ffffff', label: destCity }
                    ];

                    globe.pointsData(pointsData)
                        .pointColor('color')
                        .pointAltitude(0.02)
                        .pointRadius('size')
                        .pointLabel('label')
                        .pointsMerge(false);

                    // Calculate the midpoint for centering the view
                    const midLat = (originCoords.lat + destCoords.lat) / 2;
                    const midLng = (originCoords.lng + destCoords.lng) / 2;

                    // Calculate distance to determine appropriate altitude
                    const distance = Math.sqrt(
                        Math.pow(originCoords.lat - destCoords.lat, 2) +
                        Math.pow(originCoords.lng - destCoords.lng, 2)
                    );

                    // Adjust altitude based on distance
                    const altitude = Math.max(1.5, Math.min(4, distance / 30));

                    // Center the view
                    globe.pointOfView({ lat: midLat, lng: midLng, altitude: altitude }, 1000);

                    console.log('Route visualization complete');
                };

                // Handle window resize
                window.addEventListener('resize', function() {
                    globe.width(globeContainer.clientWidth);
                    globe.height(globeContainer.clientHeight);
                });
            } catch (error) {
                console.error('Error creating inline globe:', error);
            }
        });
    </script>

    <!-- Authentication initialization -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Transportation page DOM loaded, checking auth status...');
            // Initialize authentication
            if (typeof auth !== 'undefined') {
                // Auth.js will handle initialization on its own
                console.log('Auth module found on transportation page');

                // Add event listener to logout buttons
                const logoutButtons = document.querySelectorAll('.logout-btn');
                logoutButtons.forEach(btn => {
                    btn.addEventListener('click', function(e) {
                        e.preventDefault();
                        auth.logout();
                        // Redirect to home page after logout
                        window.location.href = 'index.html';
                    });
                });
            } else {
                console.error('Auth module not found on transportation page!');
            }
        });

        // Also check auth status immediately in case DOM is already loaded
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            console.log('Transportation page already loaded, checking auth status immediately...');
            if (typeof auth !== 'undefined') {
                console.log('Auth module found, ensuring UI is updated');
                setTimeout(() => {
                    // Force UI update based on localStorage
                    auth.init();
                }, 100);
            }
        }
    </script>
</body>
</html>
