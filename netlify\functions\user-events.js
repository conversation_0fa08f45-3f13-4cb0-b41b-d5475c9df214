// Netlify function for managing user events (similar to user-markers.js)
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://vwpyvjoycnrgmpatzqzk.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ3cHl2am95Y25yZ21wYXR6cXprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDExMzk0MTksImV4cCI6MjA1NjcxNTQxOX0.sG7amfnd8UTMv3zzmaa3KKyPRTzW70cDW9VVcUCSq4c';
const supabase = createClient(supabaseUrl, supabaseKey);

// CORS headers
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Content-Type': 'application/json'
};

// Helper function to get user ID from auth token
async function getUserIdFromToken(authHeader) {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    if (error || !user) {
      console.error('Error getting user from token:', error);
      return null;
    }
    return user.id;
  } catch (error) {
    console.error('Error verifying token:', error);
    return null;
  }
}

exports.handler = async (event, context) => {
  console.log('user-events function called with method:', event.httpMethod);
  
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  try {
    // Get user ID from Authorization header
    const authHeader = event.headers.authorization || event.headers.Authorization;
    const userId = await getUserIdFromToken(authHeader);
    
    if (!userId) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    console.log('Authenticated user:', userId);

    switch (event.httpMethod) {
      case 'GET':
        // Get all user events
        console.log('Fetching events for user:', userId);
        
        const { data: events, error: fetchError } = await supabase
          .from('user_events')
          .select('*')
          .eq('user_id', userId)
          .order('start_time', { ascending: true });

        if (fetchError) {
          console.error('Error fetching events:', fetchError);
          
          // Check if the error is because the table doesn't exist
          if (fetchError.code === '42P01') {
            console.log('Table user_events does not exist, returning empty array');
            return {
              statusCode: 200,
              headers,
              body: JSON.stringify([])
            };
          }
          
          throw fetchError;
        }

        // Convert database format to client format
        const clientEvents = (events || []).map(event => ({
          id: event.event_id,
          title: event.title,
          start: event.start_time,
          end: event.end_time,
          description: event.description,
          location: event.location,
          category: event.category,
          user_id: event.user_id
        }));

        console.log(`Returning ${clientEvents.length} events for user ${userId}`);
        return {
          statusCode: 200,
          headers,
          body: JSON.stringify(clientEvents)
        };

      case 'POST':
        // Add or update a user event
        const eventData = JSON.parse(event.body);
        console.log('Saving event for user:', userId, 'Event:', eventData.title);
        
        // Check for existing event with same event_id and user_id
        const { data: existingEvents, error: checkError } = await supabase
          .from('user_events')
          .select('*')
          .eq('user_id', userId)
          .eq('event_id', eventData.id);

        if (checkError) {
          console.error('Error checking for existing event:', checkError);
          throw checkError;
        }

        // Convert client format to database format
        const dbEvent = {
          user_id: userId,
          event_id: eventData.id,
          title: eventData.title,
          start_time: eventData.start,
          end_time: eventData.end || null,
          description: eventData.description || null,
          location: eventData.location || null,
          category: eventData.category || null
        };

        if (existingEvents && existingEvents.length > 0) {
          // Update existing event
          console.log('Updating existing event:', eventData.id);
          const { data: updatedEvent, error: updateError } = await supabase
            .from('user_events')
            .update(dbEvent)
            .eq('user_id', userId)
            .eq('event_id', eventData.id)
            .select()
            .single();

          if (updateError) {
            console.error('Error updating event:', updateError);
            throw updateError;
          }

          return {
            statusCode: 200,
            headers,
            body: JSON.stringify(updatedEvent)
          };
        } else {
          // Insert new event
          console.log('Inserting new event:', eventData.id);
          const { data: newEvent, error: insertError } = await supabase
            .from('user_events')
            .insert([dbEvent])
            .select()
            .single();

          if (insertError) {
            console.error('Error inserting event:', insertError);
            throw insertError;
          }

          return {
            statusCode: 201,
            headers,
            body: JSON.stringify(newEvent)
          };
        }

      case 'DELETE':
        // Delete a user event
        const eventId = event.queryStringParameters?.id;
        
        if (!eventId) {
          return {
            statusCode: 400,
            headers,
            body: JSON.stringify({ error: 'Event ID is required' })
          };
        }

        console.log('Deleting event:', eventId, 'for user:', userId);

        const { error: deleteError } = await supabase
          .from('user_events')
          .delete()
          .eq('user_id', userId)
          .eq('event_id', eventId);

        if (deleteError) {
          console.error('Error deleting event:', deleteError);
          throw deleteError;
        }

        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({ success: true })
        };

      default:
        return {
          statusCode: 405,
          headers,
          body: JSON.stringify({ error: 'Method not allowed' })
        };
    }
  } catch (error) {
    console.error('Error in user-events function:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        error: 'Internal server error',
        message: error.message 
      })
    };
  }
};
