/* Modal Form Fixes */

/* Fix form rows to have consistent alignment */
#externalReservationForm .form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    /* Increased to accommodate the hint text */
    align-items: flex-start;
    /* Changed from flex-end to ensure consistent alignment */
}

/* Special handling for date-time row */
#externalReservationForm .date-time-row {
    align-items: flex-start;
}

#externalReservationForm .date-time-row .form-group {
    position: relative;
}

/* Make all form groups in the modal the same height */
#externalReservationForm .form-group {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
}

/* Ensure all inputs have the same height */
#externalReservationForm input[type="text"],
#externalReservationForm input[type="time"],
#externalReservationForm select,
#externalReservationForm .date-picker {
    height: 40px;
    /* Fixed height for all inputs */
    box-sizing: border-box;
    padding: 10px;
    margin-bottom: 0;
    /* Remove any bottom margin */
}

/* Specific fixes for date and time inputs */
#externalDate,
#externalTime {
    height: 40px !important;
    line-height: 20px;
    padding: 10px;
    box-sizing: border-box;
}

/* Fix for flatpickr calendar */
.flatpickr-calendar {
    margin-top: 2px;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: auto !important;
    z-index: 100 !important;
}

/* Fix the hint text positioning */
#externalReservationForm .input-hint {
    margin-top: 4px;
    font-size: 12px;
    color: #aaa;
    line-height: 1.2;
    position: absolute;
    bottom: -20px;
}

/* Ensure the date picker doesn't have different styling from flatpickr */
#externalReservationForm .flatpickr-input {
    height: 40px;
    box-sizing: border-box;
}

/* Fix spacing for the form groups */
#externalReservationForm .form-group:not(:last-child) {
    margin-bottom: 15px;
}

/* Fix the textarea height */
#externalReservationForm textarea {
    min-height: 80px;
    resize: vertical;
}

/* Fix the modal width on smaller screens */
@media (max-width: 600px) {
    .modal-content {
        width: 95%;
        margin: 5% auto;
    }

    #externalReservationForm .form-row {
        flex-direction: column;
        gap: 10px;
    }

    #externalReservationForm .form-group {
        width: 100%;
    }
}