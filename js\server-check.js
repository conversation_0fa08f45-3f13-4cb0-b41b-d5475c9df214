/**
 * Server Check Script
 * This script checks if the backend server is running and displays a message if it's not
 */

document.addEventListener('DOMContentLoaded', function () {
    // Function to check if the server is running
    async function checkServerStatus() {
        try {
            // Determine which API URL to use based on environment
            const isProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
            const apiBaseUrl = isProduction ? '/api' : 'http://localhost:8888/api';

            const response = await fetch(`${apiBaseUrl}/health`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                },
                // Short timeout to avoid long waits if server is down
                signal: AbortSignal.timeout(2000)
            });

            if (response.ok) {
                console.log(`Backend server at ${apiBaseUrl} is running`);
                // Hide any error messages if they exist
                const errorBanner = document.getElementById('server-error-banner');
                if (errorBanner) {
                    errorBanner.style.display = 'none';
                }
                return true;
            } else {
                console.error(`Backend server at ${apiBaseUrl} returned an error`);
                showServerError(isProduction);
                return false;
            }
        } catch (error) {
            const isProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
            console.error(`Backend server is not running:`, error);
            showServerError(isProduction);
            return false;
        }
    }

    // Function to show server error message
    function showServerError(isProduction = false) {
        // Check if the banner already exists
        let errorBanner = document.getElementById('server-error-banner');

        if (!errorBanner) {
            // Create the error banner
            errorBanner = document.createElement('div');
            errorBanner.id = 'server-error-banner';
            errorBanner.className = 'server-error-banner';

            // Style the banner
            errorBanner.style.position = 'fixed';
            errorBanner.style.top = '60px'; // Below navbar
            errorBanner.style.left = '0';
            errorBanner.style.width = '100%';
            errorBanner.style.backgroundColor = '#f44336';
            errorBanner.style.color = 'white';
            errorBanner.style.padding = '10px';
            errorBanner.style.textAlign = 'center';
            errorBanner.style.zIndex = '1000';
            errorBanner.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';

            // Different messages for production vs development
            let errorMessage;
            if (isProduction) {
                errorMessage = `
                    <strong>Server Connection Issue!</strong>
                    We're having trouble connecting to our flight data server.
                    This could be temporary - please try again in a few minutes.
                `;
            } else {
                errorMessage = `
                    <strong>API Not Available!</strong>
                    Airport search and other API features require the Netlify Functions to be running.
                    If you're developing locally, please start the Netlify dev server with <code>netlify dev</code>.
                `;
            }

            // Add the message
            errorBanner.innerHTML = `
                ${errorMessage}
                <button id="close-error-banner" style="margin-left: 10px; padding: 2px 8px; background: white; color: #f44336; border: none; border-radius: 4px; cursor: pointer;">×</button>
            `;

            // Add to the document
            document.body.appendChild(errorBanner);

            // Add close button functionality
            document.getElementById('close-error-banner').addEventListener('click', function () {
                errorBanner.style.display = 'none';
            });
        } else {
            // Show the existing banner
            errorBanner.style.display = 'block';
        }
    }

    // Check server status when the page loads
    checkServerStatus();

    // Also check periodically in case the server goes down
    setInterval(checkServerStatus, 30000); // Check every 30 seconds
});
