/**
 * Travelpayouts Booking Link Generator
 * 
 * This function generates booking links for flight search results.
 * It follows the Travelpayouts API requirements to only generate links when a user clicks "Book".
 * 
 * IMPORTANT: According to Travelpayouts rules, links should only be generated when a user clicks
 * the "Book" button. Automatic collection of all links is prohibited.
 */

const axios = require('axios');

// Headers for CORS and content type
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Content-Type': 'application/json'
};

// Handle preflight OPTIONS request
exports.handler = async function(event, context) {
  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ message: 'Preflight call successful' })
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed. Please use POST.' })
    };
  }

  try {
    // Parse request body
    const requestBody = JSON.parse(event.body || '{}');
    
    // Extract required parameters
    const { search_id, url_key, marker } = requestBody;
    
    // Validate required parameters
    if (!search_id || !url_key || !marker) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ 
          error: 'Missing required parameters. Please provide search_id, url_key, and marker.' 
        })
      };
    }
    
    console.log(`Generating booking link for search_id: ${search_id}, url_key: ${url_key}`);
    
    // Construct the API URL
    const apiUrl = `https://api.travelpayouts.com/v1/flight_searches/${search_id}/clicks/${url_key}.json?marker=${marker}`;
    
    // Make the request to Travelpayouts API
    const response = await axios.get(apiUrl);
    
    // Check if the response contains a valid URL
    if (!response.data || !response.data.url) {
      throw new Error('No valid booking URL returned from Travelpayouts API');
    }
    
    // Return the booking URL and additional information
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        url: response.data.url,
        gate_id: response.data.gate_id,
        click_id: response.data.click_id,
        method: response.data.method || 'GET',
        params: response.data.params || {}
      })
    };
  } catch (error) {
    console.error('Error generating booking link:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        error: `Failed to generate booking link: ${error.message}`,
        details: error.response?.data || {}
      })
    };
  }
};
