/**
 * API Client for Vestigia
 *
 * This file provides a client-side interface to the backend API
 * that protects our API keys.
 */

// API base URL configuration for Netlify Functions
const API_CONFIG = {
  // Local development URL (when using Netlify CLI)
  development: 'http://localhost:8888/api',

  // Production URL (Netlify Functions)
  production: '/api'
};

// Determine which environment we're in
const isProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';

// Select the appropriate API base URL
const API_BASE_URL = isProduction ? API_CONFIG.production : API_CONFIG.development;

console.log(`Using API base URL: ${API_BASE_URL} (${isProduction ? 'production' : 'development'} mode)`);

/**
 * Flight API Client
 */
const flightAPI = {
  /**
   * Search for flights
   * @param {Object} params - Search parameters
   * @returns {Promise} - Promise resolving to flight search results
   */
  searchFlights: async function (params) {
    try {
      const response = await fetch(`${API_BASE_URL}/flights/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(params)
      });

      if (!response.ok) {
        throw new Error('Flight search failed');
      }

      return await response.json();
    } catch (error) {
      console.error('Error searching flights:', error);
      throw error;
    }
  },

  /**
   * Get flight details
   * @param {string} flightId - Flight identifier
   * @returns {Promise} - Promise resolving to flight details
   */
  getFlightDetails: async function (flightId) {
    try {
      const response = await fetch(`${API_BASE_URL}/flights/${flightId}`);

      if (!response.ok) {
        throw new Error('Failed to get flight details');
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting flight details:', error);
      throw error;
    }
  }
};

/**
 * Airport API Client
 */
const airportAPI = {
  /**
   * Search for airports by query
   * @param {string} query - Search query
   * @returns {Promise<Array>} Airport search results
   */
  searchAirports: async (query) => {
    try {
      const response = await fetch(`${API_BASE_URL}/airports/search?query=${encodeURIComponent(query)}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to search airports');
      }

      return await response.json();
    } catch (error) {
      console.error('Error searching airports:', error);
      throw error;
    }
  },

  /**
   * Get popular airports
   * @returns {Promise<Array>} Popular airports
   */
  getPopularAirports: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/airports/popular`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get popular airports');
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting popular airports:', error);
      throw error;
    }
  },

  // Amadeus airport search functionality has been removed
};

/**
 * City API Client
 */
const cityAPI = {
  /**
   * Search for cities by name prefix
   * @param {string} namePrefix - City name prefix
   * @param {number} limit - Maximum number of results
   * @returns {Promise<Object>} City search results
   */
  searchCities: async (namePrefix, limit = 5) => {
    try {
      const response = await fetch(`${API_BASE_URL}/cities/search?namePrefix=${encodeURIComponent(namePrefix)}&limit=${limit}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to search cities');
      }

      return await response.json();
    } catch (error) {
      console.error('Error searching cities:', error);
      throw error;
    }
  }
};

/**
 * Map API Client
 */
const mapAPI = {
  /**
   * Get directions from OpenRouteService
   * @param {string} start - Start coordinates (format: "lon,lat")
   * @param {string} end - End coordinates (format: "lon,lat")
   * @param {string} profile - Routing profile (e.g., "driving-car", "foot-walking")
   * @returns {Promise<Object>} Directions result
   */
  getDirections: async (start, end, profile = 'driving-car') => {
    try {
      const response = await fetch(`${API_BASE_URL}/maps/directions?start=${encodeURIComponent(start)}&end=${encodeURIComponent(end)}&profile=${profile}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get directions');
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting directions:', error);
      throw error;
    }
  },

  /**
   * Search for places using OpenRouteService
   * @param {string} text - Search text
   * @param {string} focus - Focus point coordinates (format: "lon,lat")
   * @param {number} radius - Search radius in meters
   * @returns {Promise<Object>} Places search results
   */
  searchPlaces: async (text, focus, radius) => {
    try {
      let url = `${API_BASE_URL}/maps/places?text=${encodeURIComponent(text)}`;

      if (focus) {
        url += `&focus=${encodeURIComponent(focus)}`;
      }

      if (radius) {
        url += `&radius=${radius}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to search places');
      }

      return await response.json();
    } catch (error) {
      console.error('Error searching places:', error);
      throw error;
    }
  }
};

// Export the API clients
console.log('API client script loaded, setting up secureAPI object');
window.secureAPI = {
  flight: flightAPI,
  airport: airportAPI,
  city: cityAPI,
  map: mapAPI
};
console.log('secureAPI object created with all modules:', {
  flight: !!window.secureAPI.flight,
  airport: !!window.secureAPI.airport,
  city: !!window.secureAPI.city,
  map: !!window.secureAPI.map
});
