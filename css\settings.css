/* Settings Page Styles */
.settings-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 2rem;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.settings-container h1 {
    color: #2c3e50;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.settings-section {
    margin-bottom: 3rem;
    padding: 1.5rem;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: #f9f9f9;
}

.settings-section h2 {
    color: #34495e;
    margin-top: 0;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 1.5rem;
}

.settings-form {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: #2c3e50;
}

.form-group input {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-primary {
    background-color: #3498db;
    color: white;
    max-width: 200px;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.danger-zone {
    border-color: #e74c3c;
    background-color: #fef5f5;
}

.danger-zone h2 {
    color: #e74c3c;
    border-color: #f5b7b1;
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.danger-note {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-top: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .settings-container {
        margin: 1rem;
        padding: 1rem;
    }
    
    .settings-section {
        padding: 1rem;
    }
    
    .btn {
        width: 100%;
        max-width: 100%;
    }
}
