/* Styles for airline and agency logos */

/* Airline logo in flight card */
.airline-logo {
    max-height: 30px;
    max-width: 60px;
    margin-right: 10px;
    object-fit: contain;
}

/* Airline info container */
.airline-info {
    display: flex;
    flex-direction: column;
}

/* Adjust airline container to display logo and info side by side */
.airline {
    display: flex;
    align-items: center;
}

/* Agency logo in booking modal */
.agency-logo {
    max-height: 30px;
    max-width: 100px;
    margin-top: 10px;
    margin-bottom: 5px;
    object-fit: contain;
}

/* Agency info in booking modal */
.agency-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
    font-size: 0.9em;
    color: #666;
}

.agency-name {
    margin-top: 5px;
    font-weight: bold;
}

/* Retina display support */
@media 
(-webkit-min-device-pixel-ratio: 2), 
(min-resolution: 192dpi) {
    .airline-logo[src*=".png"]:not([src*="@2x"]) {
        content: attr(src);
        src: attr(src, url) ~"@2x.png";
    }
    
    .agency-logo[src*=".png"]:not([src*="@2x"]) {
        content: attr(src);
        src: attr(src, url) ~"@2x.png";
    }
}
